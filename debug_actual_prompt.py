#!/usr/bin/env python3
"""
Debug what prompt is actually being sent to Ollama for 'atoms'
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def debug_actual_prompt():
    """Debug the actual prompt being generated for 'atoms'"""
    print("🔍 DEBUGGING: Actual Prompt Generation for 'atoms'")
    print("=" * 60)
    
    try:
        from knowledge_app.core.intelligent_prompt_generator import get_intelligent_prompt_generator
        
        # Step 1: Test intelligent prompt generation
        print("🧠 Step 1: Testing intelligent prompt generation...")
        
        prompt_generator = get_intelligent_prompt_generator()
        result = prompt_generator.generate_intelligent_prompt(
            raw_input="atoms",
            difficulty="medium",
            question_type="mixed"
        )
        
        print(f"✅ Intelligent prompt generated!")
        print(f"📊 Resolution confidence: {result.get('confidence', 'N/A')}")
        print(f"🎯 Resolved topic: {result['resolution']['resolved_topic']}")
        print(f"📚 Subject area: {result['resolution']['subject_area']}")
        print(f"🔧 Enhanced context: {result['enhanced_context'][:100]}...")
        
        # Step 2: Show the actual prompt
        print(f"\n📝 ACTUAL PROMPT BEING SENT TO OLLAMA:")
        print("=" * 60)
        actual_prompt = result["prompt"]
        print(actual_prompt)
        print("=" * 60)
        
        # Step 3: Check if it's using Inquisitor's Mandate
        if "### INSTRUCTION ###" in actual_prompt and "machine" in actual_prompt:
            print("✅ USING INQUISITOR'S MANDATE PROMPT! 🎉")
        elif "You are a science educator" in actual_prompt:
            print("⚠️ Using basic science educator prompt (not Inquisitor's Mandate)")
        else:
            print("❓ Unknown prompt type")
        
        # Step 4: Check context quality
        context = result['enhanced_context']
        if len(context) > 200:
            print("✅ Rich context provided")
        else:
            print("⚠️ Limited context provided")
            
        # Step 5: Test if this would generate the same question
        print(f"\n🤔 ANALYSIS:")
        print(f"   - Topic resolution: atoms -> {result['resolution']['resolved_topic']}")
        print(f"   - Context length: {len(context)} characters")
        print(f"   - Prompt type: {'Inquisitor' if '### INSTRUCTION ###' in actual_prompt else 'Basic'}")
        
        if "fundamental unit of matter" in actual_prompt:
            print("🚨 WARNING: Prompt contains 'fundamental unit of matter' - this might bias the AI!")
        else:
            print("✅ Prompt doesn't contain biasing phrases")
            
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ollama_direct():
    """Test sending the actual prompt to Ollama"""
    print("\n🚀 TESTING: Direct Ollama Call with Actual Prompt")
    print("=" * 60)
    
    try:
        import requests
        import json
        from knowledge_app.core.intelligent_prompt_generator import get_intelligent_prompt_generator
        
        # Get the actual prompt
        prompt_generator = get_intelligent_prompt_generator()
        result = prompt_generator.generate_intelligent_prompt(
            raw_input="atoms",
            difficulty="medium", 
            question_type="mixed"
        )
        
        actual_prompt = result["prompt"]
        
        # Send to Ollama with the new high-temperature settings
        payload = {
            "model": "mathstral:latest",
            "prompt": actual_prompt,
            "format": "json",
            "stream": False,
            "options": {
                "temperature": 0.8,  # New high temperature
                "top_p": 0.95,
                "top_k": 80,
                "num_predict": 1800,
                "repeat_penalty": 1.2,
                "seed": -1  # Random seed
            }
        }
        
        print(f"🚀 Sending to Ollama with high temperature (0.8)...")
        response = requests.post("http://localhost:11434/api/generate", json=payload, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            raw_response = result.get('response', '')
            
            print(f"📄 Raw Ollama response:")
            print("-" * 40)
            print(raw_response)
            print("-" * 40)
            
            # Try to parse JSON
            try:
                parsed = json.loads(raw_response)
                question = parsed.get('question', 'N/A')
                
                print(f"✅ JSON parsing successful!")
                print(f"❓ Generated question: {question}")
                
                if "fundamental unit of matter" in question:
                    print("🚨 STILL GENERATING THE SAME QUESTION!")
                    print("   This suggests the AI model has a strong bias towards this question")
                    print("   Even with high temperature and Inquisitor's Mandate prompt")
                else:
                    print("🎉 DIFFERENT QUESTION GENERATED!")
                    print("   The prompting and temperature changes are working!")
                
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                return False
        else:
            print(f"❌ Ollama request failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Direct test failed: {e}")
        return False

def main():
    print("🔬 PROMPT DEBUGGING FOR 'ATOMS'")
    print("=" * 60)
    
    # Test 1: Debug prompt generation
    prompt_works = debug_actual_prompt()
    
    # Test 2: Test with Ollama
    ollama_works = test_ollama_direct()
    
    print(f"\n📊 SUMMARY:")
    print(f"   Prompt generation: {'✅' if prompt_works else '❌'}")
    print(f"   Ollama test: {'✅' if ollama_works else '❌'}")
    
    if prompt_works and ollama_works:
        print(f"\n🎉 Prompting system is working correctly!")
        return 0
    else:
        print(f"\n❌ Issues found in prompting system")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
