#!/usr/bin/env python3
"""
Test script to verify that expert mode questions are not cached
and generate different questions each time.
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_expert_mode_no_caching():
    """Test that expert mode generates different questions each time"""
    print("🧪 Testing Expert Mode Cache Fix")
    print("=" * 50)
    
    try:
        from knowledge_app.core.inference import CloudInference
        
        print("1️⃣ Initializing CloudInference...")
        
        # Create inference instance
        config = {
            'model': 'gpt-3.5-turbo',
            'timeout': 30.0,
            'use_local_inference': False
        }
        
        inference = CloudInference(config)
        
        print("2️⃣ Testing expert mode question generation (should not cache)...")
        
        # Generate 3 expert questions with same parameters
        questions = []
        for i in range(3):
            print(f"\n   Generating expert question #{i+1}...")
            
            try:
                question_data = inference.generate_quiz_question(
                    category="numerical", 
                    difficulty="expert"
                )
                
                if question_data and question_data.get("question"):
                    question_text = question_data["question"]
                    questions.append(question_text)
                    print(f"   ✅ Generated: {question_text[:80]}...")
                else:
                    print(f"   ❌ Failed to generate question #{i+1}")
                    
            except Exception as e:
                print(f"   ❌ Error generating question #{i+1}: {e}")
        
        print("\n3️⃣ Analyzing results...")
        
        if len(questions) < 2:
            print("   ❌ Not enough questions generated to test caching")
            return False
        
        # Check if questions are different
        unique_questions = set(questions)
        
        print(f"   📊 Generated {len(questions)} questions")
        print(f"   📊 Unique questions: {len(unique_questions)}")
        
        if len(unique_questions) == len(questions):
            print("   ✅ SUCCESS: All questions are unique (no caching)")
            print("   🎉 Expert mode cache fix is working!")
            return True
        else:
            print("   ❌ FAILURE: Some questions are duplicated (caching still active)")
            print("   🚨 Expert mode cache fix needs more work")
            
            # Show which questions were duplicated
            from collections import Counter
            question_counts = Counter(questions)
            for question, count in question_counts.items():
                if count > 1:
                    print(f"   🔄 Repeated {count} times: {question[:60]}...")
            
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_regular_mode_caching():
    """Test that regular mode still uses caching"""
    print("\n🧪 Testing Regular Mode Caching")
    print("=" * 50)
    
    try:
        from knowledge_app.core.inference import CloudInference
        
        print("1️⃣ Initializing CloudInference...")
        
        # Create inference instance
        config = {
            'model': 'gpt-3.5-turbo',
            'timeout': 30.0,
            'use_local_inference': False
        }
        
        inference = CloudInference(config)
        
        print("2️⃣ Testing medium mode question generation (should cache)...")
        
        # Generate 2 medium questions with same parameters
        questions = []
        for i in range(2):
            print(f"\n   Generating medium question #{i+1}...")
            
            try:
                question_data = inference.generate_quiz_question(
                    category="physics", 
                    difficulty="medium"
                )
                
                if question_data and question_data.get("question"):
                    question_text = question_data["question"]
                    questions.append(question_text)
                    print(f"   ✅ Generated: {question_text[:80]}...")
                else:
                    print(f"   ❌ Failed to generate question #{i+1}")
                    
            except Exception as e:
                print(f"   ❌ Error generating question #{i+1}: {e}")
        
        print("\n3️⃣ Analyzing results...")
        
        if len(questions) < 2:
            print("   ❌ Not enough questions generated to test caching")
            return False
        
        # Check if questions are the same (cached)
        if questions[0] == questions[1]:
            print("   ✅ SUCCESS: Questions are identical (caching working)")
            print("   💾 Regular mode caching is working!")
            return True
        else:
            print("   ⚠️ Questions are different (caching may not be working)")
            print("   📝 This could be normal if cache expired or wasn't set")
            print(f"   Q1: {questions[0][:60]}...")
            print(f"   Q2: {questions[1][:60]}...")
            return True  # This is not necessarily a failure
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all cache tests"""
    print("🚀 Expert Mode Cache Fix Test Suite")
    print("=" * 60)
    
    # Test 1: Expert mode should not cache
    expert_success = test_expert_mode_no_caching()
    
    # Test 2: Regular mode should still cache
    regular_success = test_regular_mode_caching()
    
    # Overall results
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    print(f"   Expert Mode No-Cache Test: {'✅ PASS' if expert_success else '❌ FAIL'}")
    print(f"   Regular Mode Cache Test: {'✅ PASS' if regular_success else '❌ FAIL'}")
    
    overall_success = expert_success and regular_success
    print(f"\n🎯 OVERALL: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
    
    if overall_success:
        print("\n🎉 Cache fix is working correctly!")
        print("   - Expert mode generates unique questions (no caching)")
        print("   - Regular mode still benefits from caching")
        print("   - The MOT/VSEPR question repetition should be fixed!")
    else:
        print("\n⚠️ Some cache issues found. Check the detailed output above.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
