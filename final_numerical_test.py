#!/usr/bin/env python3
"""
Final test to generate 2 actual numerical PhD-level questions
Keep testing until we get real numerical questions
"""

import sys
import os
import time
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, Qt

def is_numerical_question(question_text, options):
    """Check if a question is actually numerical"""
    import re
    
    # Check for calculation keywords
    calc_keywords = ['calculate', 'compute', 'energy', 'wavelength', 'binding', 'mass', 'frequency']
    has_calc_keywords = any(kw in question_text.lower() for kw in calc_keywords)
    
    # Check for numbers in question or options
    has_numbers = bool(re.search(r'\d+', question_text)) or any(
        re.search(r'\d+', str(opt)) for opt in options
    )
    
    # Check for units
    units = ['eV', 'nm', 'Hz', 'J', 'kg', 'mol', 'K', 'Pa', 'V', 'A']
    has_units = any(unit in question_text for unit in units) or any(
        any(unit in str(opt) for unit in units) for opt in options
    )
    
    # Forbidden conceptual patterns
    forbidden = ['electron configuration', 'what is the', 'which theory', 'primary difference']
    is_conceptual = any(pattern in question_text.lower() for pattern in forbidden)
    
    return (has_calc_keywords or has_numbers or has_units) and not is_conceptual

def main():
    print("🔢 FINAL NUMERICAL QUESTION TEST")
    print("=" * 50)
    print("Goal: Generate 2 actual PhD-level numerical questions")
    print("Will keep testing until we get real numerical questions!")
    print("=" * 50)
    
    # Fix Qt WebEngine initialization
    QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
    app = QApplication([])
    
    # Create the app
    from knowledge_app.webengine_app import KnowledgeAppWebEngine
    knowledge_app = KnowledgeAppWebEngine()
    knowledge_app.show()
    
    # Wait for initialization
    time.sleep(3)
    
    generated_questions = []
    attempt = 1
    
    def generate_and_test_question():
        nonlocal attempt, generated_questions
        
        try:
            print(f"\n🔢 ATTEMPT {attempt}: Generating numerical question...")
            
            # Initialize unified manager if needed
            if not (hasattr(knowledge_app, 'unified_manager') and knowledge_app.unified_manager):
                from knowledge_app.core.unified_inference_manager import initialize_unified_inference, UnifiedInferenceManager
                
                success = initialize_unified_inference()
                if success:
                    knowledge_app.unified_manager = UnifiedInferenceManager()
                    print("✅ Unified manager initialized")
                else:
                    print("❌ Failed to initialize unified manager")
                    return
            
            # Generate question using direct OllamaJSONGenerator with two-model pipeline
            from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator

            generator = OllamaJSONGenerator()
            if not generator.initialize():
                print("❌ Failed to initialize OllamaJSONGenerator")
                return

            # FORCE DeepSeek R1 to be used
            print(f"🔍 Current active model: {generator.active_model}")
            if 'deepseek' not in generator.active_model.lower():
                print("🔧 Forcing DeepSeek R1 selection...")
                generator.active_model = 'deepseek-r1:14b'
                print(f"✅ Forced active model: {generator.active_model}")

            questions = generator.generate_mcq(
                topic="atoms",
                context="",
                num_questions=1,
                difficulty="expert",
                game_mode="expert",
                question_type="numerical"
            )

            result = questions[0] if questions else None
            
            if result:
                question = result.get('question', '')
                options = result.get('options', [])
                
                print(f"📝 Generated Question: {question}")
                print(f"🔢 Options: {options}")
                
                # Test if it's actually numerical
                if is_numerical_question(question, options):
                    print("✅ SUCCESS: This is a numerical question!")
                    
                    # Format for UI
                    formatted_question = {
                        "question": question,
                        "options": options,
                        "correct_answer": result.get('correct', 'A'),
                        "explanation": result.get('explanation', ''),
                        "question_number": len(generated_questions) + 1,
                        "total_questions": 2,
                        "is_loading": False
                    }
                    
                    generated_questions.append(formatted_question)
                    
                    # Display in UI
                    knowledge_app.bridge.questionReceived.emit(formatted_question)
                    
                    print(f"🎉 Question {len(generated_questions)}/2 completed!")
                    
                    if len(generated_questions) >= 2:
                        print("\n🎉 SUCCESS: Generated 2 numerical questions!")
                        print("=" * 50)
                        for i, q in enumerate(generated_questions, 1):
                            print(f"Question {i}: {q['question']}")
                            print(f"Options: {q['options']}")
                            print()
                        print("🔚 Test complete!")
                        return
                    else:
                        # Generate next question
                        attempt += 1
                        QTimer.singleShot(3000, generate_and_test_question)
                        return
                else:
                    print("❌ FAILED: Still conceptual, not numerical!")
                    print("🔄 Trying again...")
            else:
                print("❌ No question generated")
            
            # Try again
            attempt += 1
            if attempt <= 10:  # Max 10 attempts
                QTimer.singleShot(2000, generate_and_test_question)
            else:
                print("💥 FAILED: Could not generate numerical questions after 10 attempts")
                app.quit()
                
        except Exception as e:
            print(f"❌ Generation failed: {e}")
            attempt += 1
            if attempt <= 10:
                QTimer.singleShot(2000, generate_and_test_question)
            else:
                app.quit()
    
    # Start generation after app is ready
    QTimer.singleShot(5000, generate_and_test_question)
    
    # Auto-close after 5 minutes
    def timeout_close():
        print("⏰ Timeout reached, closing app...")
        app.quit()
    
    QTimer.singleShot(300000, timeout_close)  # 5 minutes
    
    print("⏳ App running... Generating numerical questions...")
    app.exec_()

if __name__ == "__main__":
    main()
