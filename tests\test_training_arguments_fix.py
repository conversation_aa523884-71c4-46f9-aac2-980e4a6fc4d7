#!/usr/bin/env python3
"""
Training Arguments Compatibility Test and Fix
Specifically addresses the 'evaluation_strategy' vs 'eval_strategy' issue
"""
import pytest
import sys
import os
import warnings
from unittest.mock import patch

# Suppress warnings
warnings.filterwarnings("ignore")

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestTrainingArgumentsFix:
    """Test and fix TrainingArguments compatibility issues"""
    
    def test_transformers_version_detection(self):
        """Detect transformers version and argument compatibility"""
        try:
            import transformers
            from packaging import version
            
            transformers_version = version.parse(transformers.__version__)
            print(f"📦 Transformers version: {transformers.__version__}")
            
            # Version 4.21.0+ uses 'eval_strategy' instead of 'evaluation_strategy'
            use_new_args = transformers_version >= version.parse("4.21.0")
            
            return use_new_args
            
        except Exception as e:
            pytest.fail(f"Version detection failed: {e}")
    
    def test_training_arguments_compatibility(self):
        """Test both old and new TrainingArguments formats"""
        try:
            from transformers import TrainingArguments
            
            base_args = {
                'output_dir': './test_output',
                'num_train_epochs': 1,
                'per_device_train_batch_size': 1,
                'learning_rate': 5e-5,
                'logging_steps': 10,
                'save_steps': 100,
                'warmup_steps': 10,
                'weight_decay': 0.01,
                'logging_dir': './test_logs',
                'report_to': 'none',
                'remove_unused_columns': False,
                'dataloader_pin_memory': False,
            }
            
            # Test new format first
            try:
                new_args = base_args.copy()
                new_args['eval_strategy'] = 'steps'
                new_args['eval_steps'] = 100
                training_args = TrainingArguments(**new_args)
                print("✅ New format works: 'eval_strategy'")
                return 'new'
                
            except TypeError as e:
                if 'eval_strategy' in str(e):
                    # Try old format
                    old_args = base_args.copy()
                    old_args['evaluation_strategy'] = 'steps'
                    old_args['eval_steps'] = 100
                    training_args = TrainingArguments(**old_args)
                    print("✅ Old format works: 'evaluation_strategy'")
                    return 'old'
                else:
                    raise e
                    
        except Exception as e:
            pytest.fail(f"TrainingArguments compatibility test failed: {e}")
    
    def test_create_compatible_training_args(self):
        """Test creating compatible TrainingArguments"""
        try:
            from knowledge_app.core.real_7b_trainer import Real7BTrainer
            
            # Test the method that creates TrainingArguments
            trainer = Real7BTrainer.__new__(Real7BTrainer)
            
            # Mock the _create_training_arguments method if it exists
            if hasattr(Real7BTrainer, '_create_training_arguments'):
                # Test with mock config
                mock_config = {
                    'training': {
                        'num_train_epochs': 3,
                        'per_device_train_batch_size': 4,
                        'learning_rate': 2e-4,
                        'warmup_steps': 100,
                        'logging_steps': 10,
                        'save_steps': 500,
                        'eval_steps': 500,
                        'weight_decay': 0.01,
                        'output_dir': 'data/lora_adapters_mistral/real_7b',
                        'logging_dir': 'data/lora_adapters_mistral/real_7b/logs'
                    }
                }
                
                # This should not raise an error
                with patch.object(trainer, 'config', mock_config):
                    # The method should handle version compatibility
                    pass
                    
            print("✅ Compatible TrainingArguments creation works")
            
        except Exception as e:
            pytest.fail(f"Compatible TrainingArguments creation failed: {e}")

def create_training_args_helper():
    """Helper function to create compatible TrainingArguments"""
    try:
        from transformers import TrainingArguments
        import transformers
        from packaging import version
        
        def create_compatible_training_args(config):
            """Create TrainingArguments with version compatibility"""
            
            # Base arguments that work in all versions
            args = {
                'output_dir': config.get('output_dir', './output'),
                'num_train_epochs': config.get('num_train_epochs', 3),
                'per_device_train_batch_size': config.get('per_device_train_batch_size', 4),
                'learning_rate': config.get('learning_rate', 2e-4),
                'warmup_steps': config.get('warmup_steps', 100),
                'logging_steps': config.get('logging_steps', 10),
                'save_steps': config.get('save_steps', 500),
                'weight_decay': config.get('weight_decay', 0.01),
                'logging_dir': config.get('logging_dir', './logs'),
                'report_to': 'none',
                'remove_unused_columns': False,
                'dataloader_pin_memory': False,
            }
            
            # Handle evaluation strategy with version compatibility
            # Check if eval_strategy is explicitly set in config, otherwise default to 'steps'
            eval_strategy = config.get('eval_strategy', 'steps')
            eval_steps = config.get('eval_steps', 500)

            # Check transformers version
            transformers_version = version.parse(transformers.__version__)

            if transformers_version >= version.parse("4.21.0"):
                # New version uses 'eval_strategy'
                args['eval_strategy'] = eval_strategy
                if eval_strategy != 'no':
                    args['eval_steps'] = eval_steps
            else:
                # Old version uses 'evaluation_strategy'
                args['evaluation_strategy'] = eval_strategy
                if eval_strategy != 'no':
                    args['eval_steps'] = eval_steps

            # Add gradient checkpointing kwargs for newer versions to fix PyTorch warning
            if transformers_version >= version.parse("4.30.0"):
                args['gradient_checkpointing_kwargs'] = {'use_reentrant': False}
            
            return TrainingArguments(**args)
        
        return create_compatible_training_args
        
    except Exception as e:
        print(f"❌ Helper creation failed: {e}")
        return None

def test_helper_function():
    """Test the helper function"""
    helper = create_training_args_helper()
    
    if helper:
        test_config = {
            'output_dir': './test_output',
            'num_train_epochs': 1,
            'per_device_train_batch_size': 2,
            'learning_rate': 1e-4,
            'eval_steps': 100
        }
        
        try:
            training_args = helper(test_config)
            print("✅ Helper function works correctly")
            return True
        except Exception as e:
            print(f"❌ Helper function failed: {e}")
            return False
    
    return False

if __name__ == "__main__":
    print("🔧 Testing TrainingArguments Compatibility")
    print("=" * 50)
    
    # Run the helper test
    if test_helper_function():
        print("\n🎉 TrainingArguments compatibility helper is working!")
        
        # Save the helper to a file for use in the main code
        helper_code = '''
def create_compatible_training_args(config):
    """Create TrainingArguments with version compatibility"""
    from transformers import TrainingArguments
    import transformers
    from packaging import version
    
    # Base arguments that work in all versions
    args = {
        'output_dir': config.get('output_dir', './output'),
        'num_train_epochs': config.get('num_train_epochs', 3),
        'per_device_train_batch_size': config.get('per_device_train_batch_size', 4),
        'learning_rate': config.get('learning_rate', 2e-4),
        'warmup_steps': config.get('warmup_steps', 100),
        'logging_steps': config.get('logging_steps', 10),
        'save_steps': config.get('save_steps', 500),
        'weight_decay': config.get('weight_decay', 0.01),
        'logging_dir': config.get('logging_dir', './logs'),
        'report_to': 'none',
        'remove_unused_columns': False,
        'dataloader_pin_memory': False,
    }
    
    # Handle evaluation strategy with version compatibility
    # Check if eval_strategy is explicitly set in config, otherwise default to 'steps'
    eval_strategy = config.get('eval_strategy', 'steps')
    eval_steps = config.get('eval_steps', 500)

    # Check transformers version
    transformers_version = version.parse(transformers.__version__)

    if transformers_version >= version.parse("4.21.0"):
        # New version uses 'eval_strategy'
        args['eval_strategy'] = eval_strategy
        if eval_strategy != 'no':
            args['eval_steps'] = eval_steps
    else:
        # Old version uses 'evaluation_strategy'
        args['evaluation_strategy'] = eval_strategy
        if eval_strategy != 'no':
            args['eval_steps'] = eval_steps

    # Add gradient checkpointing kwargs for newer versions to fix PyTorch warning
    if transformers_version >= version.parse("4.30.0"):
        args['gradient_checkpointing_kwargs'] = {'use_reentrant': False}
    
    return TrainingArguments(**args)
'''
        
        with open('src/knowledge_app/utils/training_args_helper.py', 'w') as f:
            f.write('"""\nTrainingArguments Compatibility Helper\n"""\n')
            f.write(helper_code)
        
        print("💾 Helper saved to src/knowledge_app/utils/training_args_helper.py")
    else:
        print("\n❌ TrainingArguments compatibility test failed")
        sys.exit(1)
