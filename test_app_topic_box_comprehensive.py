#!/usr/bin/env python3
"""
Comprehensive test of the app's topic box with random words
Tests the complete pipeline: UI → Semantic Mapping → Question Generation
"""

import sys
import os
import time
import random
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_topic_box_random_inputs():
    """Test app topic box with tons of random inputs"""
    print("🎯 COMPREHENSIVE TOPIC BOX TEST")
    print("=" * 70)
    
    # Massive collection of random inputs to test
    random_inputs = [
        # Medical/Psychology abbreviations
        "CBT", "PTSD", "ADHD", "OCD", "MRI", "CT", "ECG", "DNA", "RNA", "PCR",
        "HIV", "AIDS", "CPR", "ICU", "ER", "OR", "IV", "BP", "HR", "BMI",
        
        # Technology/Computing
        "AI", "ML", "API", "SQL", "HTTP", "GPU", "CPU", "RAM", "SSD", "IoT",
        "VR", "AR", "5G", "WiFi", "LAN", "WAN", "TCP", "IP", "DNS", "SSL",
        "AWS", "GCP", "SDK", "IDE", "GUI", "CLI", "OS", "VM", "DB", "UI",
        
        # Science/Math
        "DNA", "ATP", "CO2", "H2O", "NaCl", "pH", "UV", "IR", "RF", "EM",
        "calculus", "algebra", "geometry", "physics", "chemistry", "biology",
        "quantum", "relativity", "entropy", "photon", "electron", "proton",
        
        # Misspelled words
        "quantom", "phisics", "mathemetics", "biologgy", "chemestry", "psycology",
        "artficial", "inteligence", "machne", "lerning", "algoritm", "databse",
        
        # Random combinations
        "AI-ML", "CBT/DBT", "HTTP/2", "5G-WiFi", "DNA-RNA", "CPU-GPU",
        "VR+AR", "IoT&AI", "ML+DL", "API+SDK", "DB+SQL", "UI/UX",
        
        # Foreign/Technical terms
        "schadenfreude", "zeitgeist", "kaizen", "ubuntu", "feng shui",
        "satori", "ikigai", "hygge", "lagom", "ubuntu", "namaste",
        
        # Single letters/numbers/symbols
        "X", "Y", "Z", "π", "∞", "α", "β", "γ", "δ", "λ", "μ", "σ",
        "42", "3.14", "2.71", "1.41", "0.618", "299792458",
        
        # Nonsense/Random
        "xyzabc", "qwerty", "asdfgh", "123abc", "abc123", "test123",
        "random", "stuff", "things", "words", "blah", "meh",
        
        # Edge cases
        "", " ", "   ", "\n", "\t", ".", "?", "!", "@", "#", "$", "%",
        
        # Long technical terms
        "electroencephalography", "pneumonoultramicroscopicsilicovolcanoconiosiss",
        "antidisestablishmentarianism", "supercalifragilisticexpialidocious",
        
        # Mixed case and special chars
        "CbT", "aI", "mL", "DnA", "hTtP", "WiFi", "iPhone", "MacBook",
        "COVID-19", "SARS-CoV-2", "mRNA", "CRISPR", "PCR-RT",
        
        # Academic subjects
        "neuroscience", "biochemistry", "thermodynamics", "electromagnetism",
        "organic chemistry", "molecular biology", "cognitive science",
        "machine learning", "deep learning", "neural networks",
        
        # Real-world topics
        "climate change", "renewable energy", "sustainable development",
        "artificial intelligence", "quantum computing", "blockchain",
        "cryptocurrency", "virtual reality", "augmented reality"
    ]
    
    print(f"📋 Testing {len(random_inputs)} diverse inputs through the complete pipeline...")
    print()
    
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        mcq_manager = get_mcq_manager()
        print("✅ MCQ Manager initialized")
        
        # Test parameters
        difficulties = ["easy", "medium", "hard", "expert"]
        question_types = ["conceptual", "numerical", "mixed"]
        
        results = []
        successful_generations = 0
        semantic_mappings = 0
        instruction_prompts = 0
        
        # Sample random inputs for testing (test subset to avoid taking too long)
        test_sample = random.sample(random_inputs, min(20, len(random_inputs)))
        
        for i, topic in enumerate(test_sample):
            print(f"🧪 Test {i+1:2d}/{len(test_sample)}: '{topic}'")
            
            # Random difficulty and question type
            difficulty = random.choice(difficulties)
            question_type = random.choice(question_types)
            
            try:
                start_time = time.time()
                
                # Test the complete pipeline as if from the UI topic box
                quiz_params = {
                    "topic": topic,
                    "difficulty": difficulty,
                    "game_mode": "casual",
                    "submode": question_type,
                    "num_questions": 1,
                    "mode": "auto"
                }
                
                # This tests the complete flow: semantic mapping → instruction generation → MCQ creation
                result = mcq_manager.generate_quiz(quiz_params)
                
                end_time = time.time()
                generation_time = end_time - start_time
                
                if result and hasattr(result, 'question'):
                    successful_generations += 1
                    
                    # Analyze the result
                    question_text = result.question
                    question_length = len(question_text)
                    has_numbers = any(char.isdigit() for char in question_text)
                    
                    print(f"   ✅ SUCCESS ({generation_time:.1f}s)")
                    print(f"   📝 Question: {question_text[:80]}...")
                    print(f"   🎯 Type: {question_type} | Difficulty: {difficulty}")
                    print(f"   📊 Length: {question_length} chars | Has numbers: {has_numbers}")
                    
                    # Check if question type matches request
                    type_match = "✅"
                    if question_type == "numerical" and not has_numbers:
                        type_match = "⚠️ No numbers in numerical question"
                    elif question_type == "conceptual" and has_numbers:
                        type_match = "⚠️ Numbers in conceptual question"
                    
                    print(f"   {type_match}")
                    
                    results.append({
                        "topic": topic,
                        "success": True,
                        "time": generation_time,
                        "question_length": question_length,
                        "type_match": type_match.startswith("✅"),
                        "difficulty": difficulty,
                        "question_type": question_type
                    })
                    
                else:
                    print(f"   ❌ FAILED - No question generated")
                    results.append({
                        "topic": topic,
                        "success": False,
                        "time": generation_time,
                        "difficulty": difficulty,
                        "question_type": question_type
                    })
                
            except Exception as e:
                print(f"   ❌ ERROR: {e}")
                results.append({
                    "topic": topic,
                    "success": False,
                    "error": str(e),
                    "difficulty": difficulty,
                    "question_type": question_type
                })
            
            print("-" * 50)
        
        # Comprehensive analysis
        print("\n📊 COMPREHENSIVE ANALYSIS")
        print("=" * 70)
        
        successful = [r for r in results if r.get("success", False)]
        failed = [r for r in results if not r.get("success", False)]
        
        success_rate = len(successful) / len(results) * 100
        avg_time = sum(r.get("time", 0) for r in successful) / len(successful) if successful else 0
        
        print(f"🎯 Overall Success Rate: {len(successful)}/{len(results)} ({success_rate:.1f}%)")
        print(f"⏱️  Average Generation Time: {avg_time:.2f}s")
        
        if successful:
            # Type matching analysis
            type_matches = [r for r in successful if r.get("type_match", False)]
            type_match_rate = len(type_matches) / len(successful) * 100
            print(f"🎯 Question Type Accuracy: {len(type_matches)}/{len(successful)} ({type_match_rate:.1f}%)")
            
            # Question quality analysis
            avg_length = sum(r.get("question_length", 0) for r in successful) / len(successful)
            print(f"📝 Average Question Length: {avg_length:.0f} characters")
            
            # Difficulty distribution
            by_difficulty = {}
            for r in successful:
                diff = r.get("difficulty", "unknown")
                by_difficulty[diff] = by_difficulty.get(diff, 0) + 1
            
            print(f"📊 Success by Difficulty:")
            for diff, count in by_difficulty.items():
                print(f"   {diff}: {count} questions")
            
            # Question type distribution
            by_type = {}
            for r in successful:
                qtype = r.get("question_type", "unknown")
                by_type[qtype] = by_type.get(qtype, 0) + 1
            
            print(f"📊 Success by Question Type:")
            for qtype, count in by_type.items():
                print(f"   {qtype}: {count} questions")
        
        if failed:
            print(f"\n❌ Failed Topics ({len(failed)}):")
            for r in failed[:10]:  # Show first 10 failures
                topic = r.get("topic", "unknown")
                error = r.get("error", "generation failed")
                print(f"   '{topic}': {error}")
        
        # Final verdict
        print(f"\n🎯 FINAL VERDICT:")
        if success_rate >= 90:
            print("🎉 EXCELLENT: Semantic mapping and generation pipeline working perfectly!")
            return True
        elif success_rate >= 75:
            print("✅ GOOD: Pipeline working well with minor issues")
            return True
        elif success_rate >= 50:
            print("⚠️ MIXED: Pipeline working but needs improvement")
            return False
        else:
            print("❌ POOR: Pipeline has significant issues")
            return False
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_semantic_mapping_accuracy():
    """Test semantic mapping accuracy with known inputs"""
    print("\n🧠 SEMANTIC MAPPING ACCURACY TEST")
    print("=" * 70)
    
    # Known inputs with expected mappings
    known_mappings = [
        {"input": "CBT", "expected_type": "conceptual", "expected_expansion": "cognitive behavioral therapy"},
        {"input": "calculus", "expected_type": "numerical", "expected_expansion": "calculus"},
        {"input": "AI", "expected_type": "mixed", "expected_expansion": "artificial intelligence"},
        {"input": "DNA", "expected_type": "conceptual", "expected_expansion": "dna"},
        {"input": "physics", "expected_type": "mixed", "expected_expansion": "physics"},
        {"input": "algebra", "expected_type": "numerical", "expected_expansion": "algebra"},
        {"input": "psychology", "expected_type": "conceptual", "expected_expansion": "psychology"},
        {"input": "statistics", "expected_type": "numerical", "expected_expansion": "statistics"}
    ]
    
    try:
        from knowledge_app.core.intelligent_semantic_mapper import get_semantic_mapper
        
        mapper = get_semantic_mapper()
        print("✅ Semantic mapper initialized")
        
        correct_types = 0
        correct_expansions = 0
        instruction_prompts = 0
        
        for i, test_case in enumerate(known_mappings):
            input_text = test_case["input"]
            expected_type = test_case["expected_type"]
            expected_expansion = test_case["expected_expansion"]
            
            print(f"\n🧪 Test {i+1}: '{input_text}'")
            
            result = mapper.map_topic_semantically(input_text)
            
            # Check type accuracy
            actual_type = result.question_type.lower()
            type_correct = expected_type in actual_type or actual_type in expected_type
            if type_correct:
                correct_types += 1
                print(f"   ✅ Type: {actual_type} (expected: {expected_type})")
            else:
                print(f"   ❌ Type: {actual_type} (expected: {expected_type})")
            
            # Check expansion quality
            actual_expansion = result.expanded_topic.lower()
            expansion_correct = expected_expansion in actual_expansion or any(word in actual_expansion for word in expected_expansion.split())
            if expansion_correct:
                correct_expansions += 1
                print(f"   ✅ Expansion: {result.expanded_topic}")
            else:
                print(f"   ⚠️ Expansion: {result.expanded_topic}")
            
            # Check if reasoning is instruction prompt
            reasoning = result.reasoning.lower()
            is_instruction = any(word in reasoning for word in ["generate", "create", "questions", "problems"])
            if is_instruction:
                instruction_prompts += 1
                print(f"   ✅ Instruction: {result.reasoning[:60]}...")
            else:
                print(f"   ❌ Not instruction: {result.reasoning[:60]}...")
        
        # Results
        type_accuracy = correct_types / len(known_mappings) * 100
        expansion_accuracy = correct_expansions / len(known_mappings) * 100
        instruction_accuracy = instruction_prompts / len(known_mappings) * 100
        
        print(f"\n📊 SEMANTIC MAPPING RESULTS:")
        print(f"🎯 Type Accuracy: {correct_types}/{len(known_mappings)} ({type_accuracy:.1f}%)")
        print(f"📝 Expansion Accuracy: {correct_expansions}/{len(known_mappings)} ({expansion_accuracy:.1f}%)")
        print(f"🧠 Instruction Prompts: {instruction_prompts}/{len(known_mappings)} ({instruction_accuracy:.1f}%)")
        
        overall_score = (type_accuracy + expansion_accuracy + instruction_accuracy) / 3
        print(f"🎯 Overall Semantic Score: {overall_score:.1f}%")
        
        return overall_score >= 75
        
    except Exception as e:
        print(f"❌ Semantic mapping test failed: {e}")
        return False

def main():
    """Run comprehensive topic box tests"""
    print("🚀 COMPREHENSIVE TOPIC BOX TEST SUITE")
    print("=" * 80)
    
    tests = [
        ("Semantic Mapping Accuracy", test_semantic_mapping_accuracy),
        ("Random Topic Box Inputs", test_topic_box_random_inputs)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🚀 Starting: {test_name}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 80)
    print("📊 FINAL TEST RESULTS:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} test suites passed")
    
    if passed == len(results):
        print("🎉 Topic box semantic mapping is working perfectly!")
        print("✅ Random inputs are properly mapped and generate appropriate questions!")
    else:
        print("⚠️ Some issues detected in the topic box pipeline.")

if __name__ == "__main__":
    main()
