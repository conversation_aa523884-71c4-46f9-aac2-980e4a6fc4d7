#!/usr/bin/env python3
"""
Test script to verify async operations are working and not blocking
"""

import sys
import os
import time
import asyncio
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_async_semantic_mapping():
    """Test that semantic mapping runs asynchronously"""
    print("🧠 ASYNC SEMANTIC MAPPING TEST")
    print("=" * 50)
    
    try:
        from knowledge_app.core.mcq_manager import MCQManager
        
        mcq_manager = MCQManager()
        print("✅ MCQ Manager initialized")
        
        # Test async semantic preprocessing directly
        async def test_async_preprocessing():
            print("🧪 Testing async semantic preprocessing...")
            
            start_time = time.time()
            
            # This should run asynchronously without blocking
            topic_analysis = await asyncio.get_event_loop().run_in_executor(
                None,
                mcq_manager._preprocess_topic_with_semantic_mapping,
                "AI"
            )
            
            end_time = time.time()
            
            print(f"✅ Async semantic preprocessing completed in {end_time - start_time:.2f}s")
            print(f"   Original: AI")
            print(f"   Processed: {topic_analysis.get('processed_topic', 'N/A')}")
            print(f"   Method: {topic_analysis.get('processing_method', 'N/A')}")
            
            return True
        
        # Run the async test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async_preprocessing())
            return result
        finally:
            loop.close()
        
    except Exception as e:
        print(f"❌ Async semantic mapping test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_async_quiz_generation():
    """Test that quiz generation runs asynchronously"""
    print("\n🚀 ASYNC QUIZ GENERATION TEST")
    print("=" * 50)
    
    try:
        from knowledge_app.core.mcq_manager import MCQManager
        
        mcq_manager = MCQManager()
        print("✅ MCQ Manager initialized")
        
        # Test async quiz generation
        async def test_async_generation():
            print("🧪 Testing async quiz generation...")
            
            quiz_params = {
                "topic": "AI",
                "difficulty": "medium",
                "game_mode": "casual",
                "submode": "mixed",
                "num_questions": 1,
                "mode": "auto"
            }
            
            start_time = time.time()
            
            # This should run the full async pipeline
            result = await mcq_manager.generate_quiz_async(quiz_params)
            
            end_time = time.time()
            
            if result and hasattr(result, 'question'):
                print(f"✅ Async quiz generation completed in {end_time - start_time:.2f}s")
                print(f"   Question: {result.question[:80]}...")
                print(f"   Options: {len(result.options)} choices")
                return True
            else:
                print("❌ Async quiz generation failed - no result")
                return False
        
        # Run the async test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async_generation())
            return result
        finally:
            loop.close()
        
    except Exception as e:
        print(f"❌ Async quiz generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sync_wrapper():
    """Test that sync wrapper works for backward compatibility"""
    print("\n🔄 SYNC WRAPPER TEST")
    print("=" * 50)
    
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        mcq_manager = get_mcq_manager()
        print("✅ MCQ Manager obtained")
        
        quiz_params = {
            "topic": "AI",
            "difficulty": "medium",
            "game_mode": "casual",
            "submode": "mixed",
            "num_questions": 1,
            "mode": "auto"
        }
        
        print("🧪 Testing sync wrapper (should run async internally)...")
        
        start_time = time.time()
        
        # This should use the sync wrapper that runs async internally
        result = mcq_manager.generate_quiz(quiz_params)
        
        end_time = time.time()
        
        if result and hasattr(result, 'question'):
            print(f"✅ Sync wrapper completed in {end_time - start_time:.2f}s")
            print(f"   Question: {result.question[:80]}...")
            print(f"   Options: {len(result.options)} choices")
            return True
        else:
            print("❌ Sync wrapper failed - no result")
            return False
        
    except Exception as e:
        print(f"❌ Sync wrapper test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_responsiveness_simulation():
    """Simulate UI responsiveness during generation"""
    print("\n📱 UI RESPONSIVENESS SIMULATION")
    print("=" * 50)
    
    try:
        import threading
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        mcq_manager = get_mcq_manager()
        print("✅ MCQ Manager obtained")
        
        # Simulate UI thread activity
        ui_responsive = True
        ui_updates = 0
        
        def ui_thread_simulation():
            nonlocal ui_responsive, ui_updates
            start_time = time.time()
            
            while ui_responsive and (time.time() - start_time) < 10:  # Run for max 10 seconds
                ui_updates += 1
                time.sleep(0.1)  # Simulate 10 FPS UI updates
        
        # Start UI simulation thread
        ui_thread = threading.Thread(target=ui_thread_simulation)
        ui_thread.start()
        
        print("🧪 Starting quiz generation while UI thread runs...")
        
        quiz_params = {
            "topic": "quantum physics",  # More complex topic
            "difficulty": "medium",
            "game_mode": "casual",
            "submode": "mixed",
            "num_questions": 1,
            "mode": "auto"
        }
        
        generation_start = time.time()
        
        # Generate quiz (this should not block the UI thread)
        result = mcq_manager.generate_quiz(quiz_params)
        
        generation_end = time.time()
        ui_responsive = False  # Stop UI simulation
        ui_thread.join()
        
        print(f"✅ Generation completed in {generation_end - generation_start:.2f}s")
        print(f"📱 UI remained responsive: {ui_updates} updates during generation")
        
        if ui_updates > 10:  # Should have many UI updates if non-blocking
            print("✅ UI responsiveness maintained during generation")
            return True
        else:
            print("⚠️ UI may have been blocked during generation")
            return False
        
    except Exception as e:
        print(f"❌ UI responsiveness test failed: {e}")
        return False

def main():
    """Run async fix tests"""
    print("🚀 ASYNC FIX TEST SUITE")
    print("=" * 60)
    
    tests = [
        ("Async Semantic Mapping", test_async_semantic_mapping),
        ("Async Quiz Generation", test_async_quiz_generation),
        ("Sync Wrapper", test_sync_wrapper),
        ("UI Responsiveness Simulation", test_ui_responsiveness_simulation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🚀 Starting: {test_name}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 Async operations working! UI should no longer freeze.")
    else:
        print("⚠️ Some async issues remain. Check output above.")

if __name__ == "__main__":
    main()
