#!/usr/bin/env python3
"""
🔥 Expert Mode System Utilization Debugger
Diagnoses why expert mode isn't properly utilizing your system resources
"""

import sys
import os
import json
import time
import psutil
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def check_system_specs():
    """Check current system specifications"""
    print("🖥️ SYSTEM SPECIFICATIONS")
    print("=" * 50)
    
    # CPU Info
    cpu_count = psutil.cpu_count(logical=False)
    cpu_threads = psutil.cpu_count(logical=True)
    cpu_freq = psutil.cpu_freq()
    
    print(f"CPU Cores: {cpu_count}")
    print(f"CPU Threads: {cpu_threads}")
    print(f"CPU Frequency: {cpu_freq.max if cpu_freq else 'Unknown'} MHz")
    
    # Memory Info
    memory = psutil.virtual_memory()
    print(f"Total RAM: {memory.total / (1024**3):.1f} GB")
    print(f"Available RAM: {memory.available / (1024**3):.1f} GB")
    print(f"Memory Usage: {memory.percent}%")
    
    # GPU Info
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            print(f"GPU: {gpu_name}")
            print(f"GPU Memory: {gpu_memory:.1f} GB")
            print(f"CUDA Version: {torch.version.cuda}")
        else:
            print("GPU: Not available or not CUDA-enabled")
    except ImportError:
        print("GPU: PyTorch not available")

def check_ollama_status():
    """Check Ollama service status and configuration"""
    print("\n🤖 OLLAMA STATUS")
    print("=" * 50)
    
    try:
        import requests
        
        # Check if Ollama is running
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama is running")
            print(f"Available models: {len(models)}")
            for model in models[:5]:  # Show first 5 models
                print(f"  - {model['name']}")
            if len(models) > 5:
                print(f"  ... and {len(models) - 5} more")
        else:
            print(f"❌ Ollama responded with status: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Ollama is not running or not accessible")
    except Exception as e:
        print(f"❌ Error checking Ollama: {e}")

def check_current_config():
    """Check current configuration settings"""
    print("\n⚙️ CURRENT CONFIGURATION")
    print("=" * 50)
    
    config_path = Path("config/unified_config.json")
    if config_path.exists():
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Check GPU settings
        gpu_config = config.get('performance', {}).get('gpu', {})
        print(f"GPU Enabled: {gpu_config.get('enable_gpu', 'Not set')}")
        print(f"GPU Memory Fraction: {gpu_config.get('gpu_memory_fraction', 'Not set')}")
        print(f"Allow GPU Growth: {gpu_config.get('allow_gpu_growth', 'Not set')}")
        
        # Check Ollama settings
        ollama_config = config.get('models', {}).get('local', {}).get('ollama', {}).get('settings', {})
        print(f"GPU Layers: {ollama_config.get('n_gpu_layers', 'Not set')}")
        print(f"Context Size: {ollama_config.get('n_ctx', 'Not set')}")
        print(f"Batch Size: {ollama_config.get('n_batch', 'Not set')}")
        print(f"Low VRAM Mode: {ollama_config.get('low_vram', 'Not set')}")
        
        # Check thread settings
        processing_config = config.get('performance', {}).get('processing', {})
        print(f"Max Threads: {processing_config.get('max_threads', 'Not set')}")
        print(f"Thread Pool Size: {processing_config.get('thread_pool_size', 'Not set')}")
        
    else:
        print("❌ Configuration file not found")

def monitor_resource_usage_during_expert_mode():
    """Monitor system resources during expert mode execution"""
    print("\n📊 RESOURCE MONITORING TEST")
    print("=" * 50)
    print("Testing expert mode generation and monitoring resource usage...")
    
    # Baseline measurements
    baseline_cpu = psutil.cpu_percent(interval=1)
    baseline_memory = psutil.virtual_memory().percent
    
    try:
        import torch
        if torch.cuda.is_available():
            baseline_gpu_memory = torch.cuda.memory_allocated() / (1024**3)
        else:
            baseline_gpu_memory = 0
    except:
        baseline_gpu_memory = 0
    
    print(f"Baseline - CPU: {baseline_cpu}%, Memory: {baseline_memory}%, GPU Memory: {baseline_gpu_memory:.2f}GB")
    
    # Test expert mode generation
    try:
        from knowledge_app.core.deepseek_integration import get_deepseek_pipeline
        
        print("\n🧠 Testing DeepSeek Pipeline...")
        pipeline = get_deepseek_pipeline()
        
        if pipeline and pipeline.is_ready():
            print("✅ DeepSeek pipeline is ready")
            
            # Start monitoring
            start_time = time.time()
            
            # Generate expert question
            print("🚀 Starting expert question generation...")
            result = pipeline.generate_expert_question(
                topic="quantum mechanics",
                difficulty="expert"
            )
            
            end_time = time.time()
            generation_time = end_time - start_time
            
            # Measure resource usage during generation
            peak_cpu = psutil.cpu_percent(interval=1)
            peak_memory = psutil.virtual_memory().percent
            
            try:
                if torch.cuda.is_available():
                    peak_gpu_memory = torch.cuda.memory_allocated() / (1024**3)
                else:
                    peak_gpu_memory = 0
            except:
                peak_gpu_memory = 0
            
            print(f"\n📈 RESOURCE USAGE DURING GENERATION:")
            print(f"Generation Time: {generation_time:.2f} seconds")
            print(f"Peak CPU Usage: {peak_cpu}%")
            print(f"Peak Memory Usage: {peak_memory}%")
            print(f"Peak GPU Memory: {peak_gpu_memory:.2f}GB")
            
            # Calculate utilization efficiency
            cpu_utilization = peak_cpu - baseline_cpu
            memory_utilization = peak_memory - baseline_memory
            gpu_utilization = peak_gpu_memory - baseline_gpu_memory
            
            print(f"\n🎯 UTILIZATION ANALYSIS:")
            print(f"CPU Increase: +{cpu_utilization:.1f}%")
            print(f"Memory Increase: +{memory_utilization:.1f}%")
            print(f"GPU Memory Increase: +{gpu_utilization:.2f}GB")
            
            # Analyze efficiency
            if cpu_utilization < 20:
                print("⚠️ LOW CPU UTILIZATION - System not fully utilized")
            if gpu_utilization < 1.0 and torch.cuda.is_available():
                print("⚠️ LOW GPU UTILIZATION - GPU not being used effectively")
            if generation_time > 60:
                print("⚠️ SLOW GENERATION - Performance optimization needed")
                
            if result:
                print("✅ Expert question generated successfully")
            else:
                print("❌ Expert question generation failed")
                
        else:
            print("❌ DeepSeek pipeline not ready")
            
    except Exception as e:
        print(f"❌ Error testing expert mode: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main diagnostic function"""
    print("🔥 EXPERT MODE SYSTEM UTILIZATION DIAGNOSTIC")
    print("=" * 60)
    
    check_system_specs()
    check_ollama_status()
    check_current_config()
    monitor_resource_usage_during_expert_mode()
    
    print("\n🎯 RECOMMENDATIONS:")
    print("=" * 50)
    print("1. If GPU utilization is low, increase n_gpu_layers")
    print("2. If CPU utilization is low, increase thread_pool_size")
    print("3. If generation is slow, optimize model loading")
    print("4. If memory usage is low, disable low_vram mode")
    print("5. Check Ollama model configuration for GPU acceleration")

if __name__ == "__main__":
    main()
