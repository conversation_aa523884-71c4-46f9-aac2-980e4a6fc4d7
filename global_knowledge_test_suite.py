#!/usr/bin/env python3
"""
Global Knowledge Test Suite - Massive Topic Expansion
Tests MCQ generation across hundreds of diverse topics from around the world
"""

import asyncio
import json
import time
import random
from datetime import datetime
from pathlib import Path

# MASSIVE GLOBAL TOPIC DATABASE
GLOBAL_TOPICS = {
    # SCIENCES - Physical & Natural
    "physics": [
        "quantum mechanics", "thermodynamics", "electromagnetism", "relativity theory",
        "particle physics", "astrophysics", "nuclear physics", "optics", "acoustics",
        "fluid dynamics", "solid state physics", "plasma physics", "biophysics"
    ],
    "chemistry": [
        "organic chemistry", "inorganic chemistry", "biochemistry", "physical chemistry",
        "analytical chemistry", "medicinal chemistry", "polymer chemistry", "electrochemistry",
        "photochemistry", "environmental chemistry", "forensic chemistry", "petrochemistry"
    ],
    "biology": [
        "molecular biology", "genetics", "ecology", "evolution", "microbiology",
        "neuroscience", "immunology", "botany", "zoology", "marine biology",
        "cell biology", "developmental biology", "biotechnology", "virology"
    ],
    "earth_sciences": [
        "geology", "meteorology", "oceanography", "seismology", "volcanology",
        "paleontology", "mineralogy", "hydrology", "climatology", "glaciology"
    ],
    
    # MATHEMATICS & COMPUTER SCIENCE
    "mathematics": [
        "calculus", "linear algebra", "differential equations", "number theory",
        "topology", "statistics", "probability", "discrete mathematics", "geometry",
        "trigonometry", "complex analysis", "abstract algebra", "graph theory"
    ],
    "computer_science": [
        "algorithms", "data structures", "machine learning", "artificial intelligence",
        "cybersecurity", "database systems", "computer networks", "operating systems",
        "software engineering", "computer graphics", "distributed systems", "blockchain"
    ],
    
    # HUMANITIES & SOCIAL SCIENCES
    "history": [
        "ancient civilizations", "world war history", "renaissance", "industrial revolution",
        "cold war", "medieval history", "colonial history", "civil rights movement",
        "ancient egypt", "roman empire", "byzantine empire", "ottoman empire"
    ],
    "philosophy": [
        "ethics", "metaphysics", "epistemology", "logic", "political philosophy",
        "philosophy of mind", "existentialism", "stoicism", "buddhist philosophy",
        "confucianism", "islamic philosophy", "western philosophy"
    ],
    "psychology": [
        "cognitive psychology", "behavioral psychology", "developmental psychology",
        "social psychology", "abnormal psychology", "neuropsychology", "personality psychology",
        "positive psychology", "forensic psychology", "educational psychology"
    ],
    "sociology": [
        "social theory", "criminology", "urban sociology", "political sociology",
        "economic sociology", "cultural sociology", "gender studies", "race relations",
        "social movements", "globalization", "social inequality", "family sociology"
    ],
    
    # LANGUAGES & LITERATURE
    "languages": [
        "english literature", "spanish literature", "french literature", "german literature",
        "russian literature", "chinese literature", "japanese literature", "arabic literature",
        "linguistics", "comparative literature", "poetry", "drama", "prose"
    ],
    "world_literature": [
        "shakespeare", "dante", "cervantes", "tolstoy", "dostoevsky", "kafka",
        "borges", "garcia marquez", "murakami", "chinua achebe", "toni morrison"
    ],
    
    # ARTS & CULTURE
    "visual_arts": [
        "renaissance art", "baroque art", "impressionism", "modern art", "contemporary art",
        "sculpture", "photography", "digital art", "street art", "islamic art",
        "asian art", "african art", "indigenous art", "art history"
    ],
    "music": [
        "classical music", "jazz", "blues", "rock music", "electronic music",
        "world music", "opera", "folk music", "hip hop", "reggae", "country music",
        "music theory", "ethnomusicology", "music production"
    ],
    "performing_arts": [
        "theater", "dance", "ballet", "opera", "musical theater", "contemporary dance",
        "traditional dance", "circus arts", "performance art", "mime"
    ],
    
    # WORLD CULTURES & RELIGIONS
    "religions": [
        "christianity", "islam", "judaism", "hinduism", "buddhism", "sikhism",
        "jainism", "taoism", "shintoism", "zoroastrianism", "bahai faith",
        "indigenous religions", "comparative religion", "theology"
    ],
    "world_cultures": [
        "african cultures", "asian cultures", "european cultures", "latin american cultures",
        "middle eastern cultures", "oceanic cultures", "indigenous cultures",
        "cultural anthropology", "folklore", "mythology", "traditions"
    ],
    
    # ECONOMICS & BUSINESS
    "economics": [
        "microeconomics", "macroeconomics", "international economics", "development economics",
        "behavioral economics", "environmental economics", "labor economics",
        "monetary policy", "fiscal policy", "economic history", "econometrics"
    ],
    "business": [
        "management", "marketing", "finance", "accounting", "entrepreneurship",
        "operations management", "human resources", "business strategy",
        "international business", "business ethics", "supply chain management"
    ],
    
    # MEDICINE & HEALTH
    "medicine": [
        "anatomy", "physiology", "pathology", "pharmacology", "surgery",
        "internal medicine", "pediatrics", "psychiatry", "radiology",
        "emergency medicine", "public health", "epidemiology", "medical ethics"
    ],
    "health_sciences": [
        "nutrition", "exercise science", "mental health", "global health",
        "health policy", "biomedical engineering", "medical technology",
        "alternative medicine", "preventive medicine", "health education"
    ],
    
    # ENGINEERING & TECHNOLOGY
    "engineering": [
        "mechanical engineering", "electrical engineering", "civil engineering",
        "chemical engineering", "aerospace engineering", "biomedical engineering",
        "environmental engineering", "industrial engineering", "materials science"
    ],
    "technology": [
        "robotics", "nanotechnology", "biotechnology", "renewable energy",
        "space technology", "telecommunications", "automation", "3d printing",
        "virtual reality", "augmented reality", "internet of things", "quantum computing"
    ],
    
    # GEOGRAPHY & ENVIRONMENTAL SCIENCE
    "geography": [
        "physical geography", "human geography", "cartography", "gis",
        "urban planning", "regional geography", "cultural geography",
        "economic geography", "political geography", "population geography"
    ],
    "environmental_science": [
        "climate change", "conservation biology", "environmental policy",
        "sustainable development", "renewable resources", "pollution control",
        "ecosystem management", "environmental chemistry", "green technology"
    ],
    
    # SPORTS & RECREATION
    "sports": [
        "football", "basketball", "soccer", "tennis", "golf", "baseball",
        "swimming", "athletics", "gymnastics", "martial arts", "cycling",
        "winter sports", "water sports", "extreme sports", "sports medicine"
    ],
    
    # FOOD & CULINARY ARTS
    "culinary": [
        "international cuisine", "cooking techniques", "food science", "nutrition",
        "wine and spirits", "baking and pastry", "food safety", "culinary history",
        "molecular gastronomy", "sustainable food", "food culture", "restaurant management"
    ],
    
    # CURRENT AFFAIRS & POLITICS
    "politics": [
        "political theory", "international relations", "comparative politics",
        "public policy", "political economy", "diplomacy", "governance",
        "democracy", "authoritarianism", "political movements", "elections"
    ],
    "current_affairs": [
        "global politics", "climate policy", "economic trends", "social issues",
        "technological developments", "international conflicts", "trade relations",
        "human rights", "migration", "urbanization", "digital transformation"
    ]
}

def generate_massive_test_suite():
    """Generate a massive test suite from all global topics"""
    all_tests = []
    test_id = 1
    
    difficulties = ["easy", "medium", "hard", "expert"]
    
    for category, topics in GLOBAL_TOPICS.items():
        for topic in topics:
            for difficulty in difficulties:
                all_tests.append({
                    "test_id": test_id,
                    "category": category,
                    "topic": topic,
                    "difficulty": difficulty,
                    "domain": category.replace("_", " ").title(),
                    "expected_keywords": get_expected_keywords(category, topic)
                })
                test_id += 1
    
    return all_tests

def get_expected_keywords(category, topic):
    """Get expected keywords for validation"""
    keyword_map = {
        "physics": ["force", "energy", "momentum", "wave", "particle", "quantum"],
        "chemistry": ["molecule", "atom", "bond", "reaction", "compound", "solution"],
        "mathematics": ["equation", "function", "derivative", "integral", "theorem", "proof"],
        "biology": ["cell", "organism", "evolution", "genetics", "protein", "DNA"],
        "history": ["civilization", "empire", "war", "revolution", "culture", "society"],
        "philosophy": ["ethics", "logic", "metaphysics", "knowledge", "reality", "truth"],
        "medicine": ["diagnosis", "treatment", "patient", "disease", "therapy", "clinical"],
        "engineering": ["design", "system", "structure", "analysis", "optimization", "technology"]
    }
    
    return keyword_map.get(category, ["concept", "theory", "principle", "method", "analysis"])

async def test_topic_generation(test_case):
    """Test MCQ generation for a specific topic"""
    try:
        from src.knowledge_app.core.unified_inference_manager import generate_mcq_unified
        
        topic = test_case["topic"]
        difficulty = test_case["difficulty"]
        expected_keywords = test_case["expected_keywords"]
        
        result = generate_mcq_unified(
            topic=topic,
            difficulty=difficulty,
            question_type="mixed",
            timeout=30.0
        )
        
        if not result:
            return False, "Generation failed"
        
        question = result.get('question', '')
        options = result.get('options', [])
        correct = result.get('correct_answer', '') or result.get('correct', '')
        
        # Validation checks
        checks = {
            'has_question': bool(question.strip()),
            'has_question_mark': question.endswith('?'),
            'has_options': len(options) == 4,
            'has_correct': correct in ['A', 'B', 'C', 'D'],
            'sufficient_length': len(question) >= 50,
            'has_keywords': any(keyword.lower() in question.lower() for keyword in expected_keywords)
        }
        
        passed = sum(checks.values())
        total = len(checks)
        
        return passed >= (total * 0.8), f"{passed}/{total} checks passed"  # 80% pass threshold
        
    except Exception as e:
        return False, f"Error: {e}"

async def run_batch_test(batch_num, batch_tests):
    """Run a single batch of 20 tests"""
    print(f"\n🚀 BATCH {batch_num} - RUNNING {len(batch_tests)} TESTS")
    print("=" * 60)

    start_time = time.time()
    passed = 0
    category_stats = {}

    for i, test_case in enumerate(batch_tests, 1):
        category = test_case["category"]
        topic = test_case["topic"]
        difficulty = test_case["difficulty"]

        print(f"\n🧪 Test {i:2d}/{len(batch_tests)}: [{category}] {topic} ({difficulty})")

        success, message = await test_topic_generation(test_case)

        if success:
            print(f"   ✅ PASS - {message}")
            passed += 1
        else:
            print(f"   ❌ FAIL - {message}")

        # Track category stats
        if category not in category_stats:
            category_stats[category] = {"passed": 0, "total": 0}
        category_stats[category]["total"] += 1
        if success:
            category_stats[category]["passed"] += 1

    duration = time.time() - start_time
    pass_rate = (passed / len(batch_tests)) * 100

    print(f"\n📊 BATCH {batch_num} RESULTS:")
    print(f"   Tests: {passed}/{len(batch_tests)} passed ({pass_rate:.1f}%)")
    print(f"   Duration: {duration:.1f} seconds")
    print(f"   Speed: {len(batch_tests)/duration:.2f} tests/second")

    return passed, len(batch_tests), category_stats, duration

async def run_massive_global_test_suite():
    """Run the massive global knowledge test suite in batches"""
    print("🌍 MASSIVE GLOBAL KNOWLEDGE TEST SUITE - BATCH MODE")
    print("=" * 80)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Generate all test cases
    all_tests = generate_massive_test_suite()
    total_tests = len(all_tests)

    print(f"📊 Generated {total_tests} test cases across {len(GLOBAL_TOPICS)} categories")
    print(f"🌐 Categories: {', '.join(GLOBAL_TOPICS.keys())}")

    # Create diverse sample for batching
    sample_size = 60  # 3 batches of 20 each
    sampled_tests = random.sample(all_tests, min(sample_size, total_tests))

    # Split into batches of 20
    batch_size = 20
    batches = [sampled_tests[i:i+batch_size] for i in range(0, len(sampled_tests), batch_size)]

    print(f"\n🎯 Running {len(batches)} batches of {batch_size} tests each...")

    # Run batches
    total_passed = 0
    total_tested = 0
    all_category_stats = {}
    total_duration = 0

    for batch_num, batch_tests in enumerate(batches, 1):
        batch_passed, batch_total, batch_stats, batch_duration = await run_batch_test(batch_num, batch_tests)

        total_passed += batch_passed
        total_tested += batch_total
        total_duration += batch_duration

        # Merge category stats
        for category, stats in batch_stats.items():
            if category not in all_category_stats:
                all_category_stats[category] = {"passed": 0, "total": 0}
            all_category_stats[category]["passed"] += stats["passed"]
            all_category_stats[category]["total"] += stats["total"]

        # Ask user if they want to continue to next batch
        if batch_num < len(batches):
            print(f"\n⏸️  Batch {batch_num} complete. Ready for Batch {batch_num + 1}?")
            print(f"   Current overall: {total_passed}/{total_tested} ({(total_passed/total_tested*100):.1f}%)")

    pass_rate = (total_passed / total_tested) * 100
    
    # Final results summary
    print("\n" + "=" * 80)
    print("🏆 MASSIVE GLOBAL TEST RESULTS - BATCH MODE")
    print("=" * 80)

    print(f"📊 Overall Performance:")
    print(f"   Total Batches Run: {len(batches)}")
    print(f"   Total Tests Run: {total_tested}")
    print(f"   Tests Passed: {total_passed}")
    print(f"   Pass Rate: {pass_rate:.1f}%")
    print(f"   Total Duration: {total_duration:.1f} seconds")
    print(f"   Average Speed: {total_tested/total_duration:.2f} tests/second")

    print(f"\n📈 Category Breakdown:")
    for category, stats in sorted(all_category_stats.items()):
        cat_pass_rate = (stats["passed"] / stats["total"]) * 100 if stats["total"] > 0 else 0
        print(f"   {category.replace('_', ' ').title():<20}: {stats['passed']:2d}/{stats['total']:2d} ({cat_pass_rate:5.1f}%)")

    # Estimate full suite performance
    estimated_full_duration = (total_duration / total_tested) * total_tests
    print(f"\n🔮 Full Suite Estimates:")
    print(f"   Total Available Tests: {total_tests}")
    print(f"   Estimated Full Duration: {estimated_full_duration/60:.1f} minutes")
    print(f"   Estimated Full Pass Rate: {pass_rate:.1f}%")
    
    # Success criteria
    if pass_rate >= 80:
        print(f"\n🎉 EXCELLENT: {pass_rate:.1f}% pass rate across global topics!")
        return True
    elif pass_rate >= 70:
        print(f"\n👏 GOOD: {pass_rate:.1f}% pass rate - strong global coverage!")
        return True
    else:
        print(f"\n📊 PROGRESS: {pass_rate:.1f}% pass rate - room for improvement")
        return False

async def main():
    """Main execution"""
    try:
        # Initialize system
        print("🔧 Initializing system...")
        from src.knowledge_app.core.unified_inference_manager import initialize_unified_inference
        
        success = initialize_unified_inference({
            'timeout': 30.0,
            'mode': 'auto',
            'prefer_local': True
        })
        
        if not success:
            print("❌ System initialization failed")
            return 1
        
        print("✅ System initialized successfully")
        
        # Run massive test suite
        success = await run_massive_global_test_suite()
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"❌ Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
