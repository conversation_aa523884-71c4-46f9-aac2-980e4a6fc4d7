#!/usr/bin/env python3
"""
Debug the intelligent prompt generator to see what prompts it's creating
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging to see what's happening
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

def test_intelligent_prompt_generator():
    """Test what the intelligent prompt generator creates"""
    print("🧠 Testing Intelligent Prompt Generator...")
    
    try:
        from knowledge_app.core.intelligent_prompt_generator import get_intelligent_prompt_generator
        
        # Get the generator
        prompt_generator = get_intelligent_prompt_generator()
        
        # Test with physics
        print("🔍 Testing with 'physics'...")
        result = prompt_generator.generate_intelligent_prompt(
            raw_input="physics",
            difficulty="medium",
            question_type="mixed"
        )
        
        print(f"📝 Intelligent prompt result:")
        print(f"   Confidence: {result.get('confidence', 'N/A')}")
        print(f"   Metadata: {result.get('metadata', {})}")
        print(f"   Prompt length: {len(result.get('prompt', ''))}")
        print(f"   Prompt preview: {result.get('prompt', '')[:200]}...")
        
        # Show the full prompt
        print("\n📄 FULL PROMPT:")
        print("-" * 80)
        print(result.get('prompt', ''))
        print("-" * 80)
        
        return result
        
    except Exception as e:
        print(f"❌ Intelligent prompt test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_simple_vs_intelligent():
    """Compare simple prompt vs intelligent prompt"""
    print("\n🔄 Comparing Simple vs Intelligent Prompts...")
    
    # Test simple prompt
    simple_prompt = """Create 1 multiple choice question about physics at medium level.

After thinking, output a JSON object with this exact format:
{
  "question": "Your question text here?",
  "options": ["Option A text", "Option B text", "Option C text", "Option D text"],
  "correct_answer": "A",
  "explanation": "Brief explanation"
}

Requirements:
- Question must end with ?
- Exactly 4 options
- correct_answer must be A, B, C, or D

Topic: physics
Difficulty: medium

CRITICAL: You MUST respond with ONLY valid JSON. No explanations, no text before or after the JSON.

Start your response with { and end with }. Nothing else."""

    print("📝 Simple prompt:")
    print(simple_prompt[:300] + "...")
    
    # Test with Ollama
    try:
        import requests
        
        # Test simple prompt
        print("\n🧪 Testing simple prompt with llama3.1...")
        payload = {
            "model": "llama3.1:latest",
            "prompt": simple_prompt,
            "format": "json",
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_predict": 500
            }
        }
        
        response = requests.post("http://localhost:11434/api/generate", json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            raw_response = result.get('response', '')
            
            print(f"✅ Simple prompt response:")
            print(f"📝 Response: {raw_response}")
            
            # Try to parse
            try:
                parsed = json.loads(raw_response)
                print(f"✅ Simple prompt - JSON parsing successful!")
                print(f"📋 Parsed: {parsed}")
                return True
            except:
                print(f"❌ Simple prompt - JSON parsing failed")
                return False
        else:
            print(f"❌ Simple prompt - Request failed")
            return False
            
    except Exception as e:
        print(f"❌ Simple prompt test failed: {e}")
        return False

def test_with_fixed_prompt():
    """Test with a completely fixed, simple prompt"""
    print("\n🔧 Testing with ultra-simple prompt...")
    
    # 🚫 NO HARDCODED SAMPLE QUESTIONS - Use AI generation only
    ultra_simple_prompt = """Generate a physics multiple choice question in JSON format.

Output only JSON with this structure:
{
  "question": "...",
  "options": ["...", "...", "...", "..."],
  "correct_answer": "A",
  "explanation": "..."
}

Generate a question about physics. Output only JSON."""

    try:
        import requests
        
        payload = {
            "model": "llama3.1:latest",
            "prompt": ultra_simple_prompt,
            "format": "json",
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_predict": 300
            }
        }
        
        response = requests.post("http://localhost:11434/api/generate", json=payload, timeout=20)
        
        if response.status_code == 200:
            result = response.json()
            raw_response = result.get('response', '')
            
            print(f"✅ Ultra-simple response:")
            print(f"📝 Response: {raw_response}")
            
            # Try to parse
            try:
                parsed = json.loads(raw_response)
                print(f"✅ Ultra-simple - JSON parsing successful!")
                print(f"📋 Parsed: {parsed}")
                return True
            except:
                print(f"❌ Ultra-simple - JSON parsing failed")
                return False
        else:
            print(f"❌ Ultra-simple - Request failed")
            return False
            
    except Exception as e:
        print(f"❌ Ultra-simple test failed: {e}")
        return False

def main():
    print("🧠 Intelligent Prompt Debug Tool")
    print("=" * 60)
    
    # Test 1: What does the intelligent prompt generator create?
    intelligent_result = test_intelligent_prompt_generator()
    
    # Test 2: Compare simple vs intelligent
    simple_works = test_simple_vs_intelligent()
    
    # Test 3: Ultra-simple test
    ultra_simple_works = test_with_fixed_prompt()
    
    print("\n📊 SUMMARY:")
    print(f"   Intelligent prompt created: {'✅' if intelligent_result else '❌'}")
    print(f"   Simple prompt works: {'✅' if simple_works else '❌'}")
    print(f"   Ultra-simple works: {'✅' if ultra_simple_works else '❌'}")
    
    if ultra_simple_works:
        print("\n💡 SOLUTION: Use ultra-simple prompts instead of complex intelligent ones!")
        return 0
    else:
        print("\n❌ Even simple prompts are failing")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
