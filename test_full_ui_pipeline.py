#!/usr/bin/env python3
"""
🔥 COMPREHENSIVE UI-TO-BACKEND PIPELINE TEST
This test simulates the EXACT flow from UI to backend to identify real issues:
1. Token streaming not working
2. Same questions repeating
3. Numerical choice not being respected
"""

import sys
import os
import time
import json
import threading
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from knowledge_app.webengine_app import KnowledgeAppWebEngine
from knowledge_app.core.mcq_manager import MCQManager
from knowledge_app.core.topic_analyzer import TopicAnalyzer

def test_token_streaming_pipeline():
    """Test the actual token streaming pipeline"""
    print("🌊 TESTING TOKEN STREAMING PIPELINE")
    print("-" * 50)
    
    try:
        # Check if token streaming is enabled in UI
        html_file = Path("src/knowledge_app/web/app.html")
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Check checkbox state
        checkbox_enabled = 'id="token-streaming-enabled"' in html_content and 'checked>' in html_content
        print(f"✅ Token streaming checkbox enabled: {checkbox_enabled}")
        
        # Check JavaScript token streaming logic
        js_file = Path("src/knowledge_app/web/app.js")
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Look for token streaming function
        has_streaming_function = 'shouldUseTokenStreaming' in js_content
        has_streaming_visualization = 'startTokenStreamingVisualization' in js_content
        
        print(f"✅ Token streaming function exists: {has_streaming_function}")
        print(f"✅ Token streaming visualization exists: {has_streaming_visualization}")
        
        # Check if streaming is actually enabled
        if 'return false' in js_content and 'EMERGENCY' in js_content:
            print("❌ FOUND EMERGENCY DISABLE - Token streaming is disabled!")
            return False
        
        return checkbox_enabled and has_streaming_function and has_streaming_visualization
        
    except Exception as e:
        print(f"❌ Token streaming test failed: {e}")
        return False

def test_topic_analysis_pipeline():
    """Test the topic analysis pipeline with different topics"""
    print("\n🔍 TESTING TOPIC ANALYSIS PIPELINE")
    print("-" * 50)
    
    try:
        analyzer = TopicAnalyzer()
        
        test_cases = [
            ("atoms", "numerical"),
            ("physics", "numerical"), 
            ("chemistry", "numerical"),
            ("history", "conceptual"),
            ("literature", "conceptual")
        ]
        
        all_passed = True
        for topic, expected_type in test_cases:
            profile = analyzer.get_topic_profile(topic)
            detected_type = profile.get('detected_type', 'unknown')
            is_numerical_possible = profile.get('is_numerical_possible', False)
            
            print(f"Topic: '{topic}'")
            print(f"  Expected: {expected_type}")
            print(f"  Detected: {detected_type}")
            print(f"  Numerical possible: {is_numerical_possible}")
            
            # Check if detection matches expectation
            if expected_type == "numerical":
                if detected_type == "numerical" or is_numerical_possible:
                    print(f"  ✅ PASS")
                else:
                    print(f"  ❌ FAIL - Should be numerical")
                    all_passed = False
            else:
                if detected_type == "conceptual" or not is_numerical_possible:
                    print(f"  ✅ PASS")
                else:
                    print(f"  ❌ FAIL - Should be conceptual")
                    all_passed = False
            print()
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Topic analysis test failed: {e}")
        return False

def test_question_generation_pipeline():
    """Test the actual question generation pipeline"""
    print("🧠 TESTING QUESTION GENERATION PIPELINE")
    print("-" * 50)
    
    try:
        # Initialize MCQ Manager (this is what the UI uses)
        mcq_manager = MCQManager()
        
        # Test different topics and question types
        test_cases = [
            {"topic": "atoms", "difficulty": "medium", "question_type": "numerical"},
            {"topic": "atoms", "difficulty": "medium", "question_type": "conceptual"},
            {"topic": "physics", "difficulty": "medium", "question_type": "numerical"},
        ]
        
        generated_questions = []
        
        for i, params in enumerate(test_cases, 1):
            print(f"Test {i}: Generating question for {params}")
            
            try:
                # This simulates what the UI does
                quiz_params = {
                    "topic": params["topic"],
                    "difficulty": params["difficulty"],
                    "game_mode": "casual",
                    "submode": params["question_type"],
                    "num_questions": 1
                }
                
                # Try to generate a question
                print(f"  Calling MCQ Manager with: {quiz_params}")
                
                # This is the actual method the UI calls
                result = mcq_manager.generate_quiz(quiz_params)
                
                if result and 'questions' in result:
                    questions = result['questions']
                    if questions:
                        print(f"  ✅ Generated {len(questions)} questions")
                        print(f"  ✅ First question: {questions[0].get('question', 'N/A')[:100]}...")
                        generated_questions.append(result)
                    else:
                        print(f"  ❌ No questions in result")
                else:
                    print(f"  ❌ Failed to generate quiz: {result}")
                
            except Exception as e:
                print(f"  ❌ Generation failed: {e}")
        
        # Check for question diversity
        if len(generated_questions) > 1:
            all_questions = []
            for result in generated_questions:
                if 'questions' in result:
                    all_questions.extend(result['questions'])

            question_texts = [q.get('question', '') for q in all_questions]
            unique_questions = set(question_texts)
            print(f"\nQuestion diversity check:")
            print(f"  Generated {len(all_questions)} total questions")
            print(f"  Unique questions: {len(unique_questions)}")

            if len(unique_questions) < len(all_questions):
                print("  ⚠️ WARNING: Same questions being repeated!")
                for i, q in enumerate(question_texts):
                    print(f"    Q{i+1}: {q[:50]}...")
            else:
                print("  ✅ All questions are unique")
        
        return len(generated_questions) > 0
        
    except Exception as e:
        print(f"❌ Question generation test failed: {e}")
        return False

def test_ui_backend_integration():
    """Test the UI-backend integration"""
    print("\n🔗 TESTING UI-BACKEND INTEGRATION")
    print("-" * 50)
    
    try:
        # Check if WebEngineApp can be initialized
        print("Initializing WebEngineApp...")
        
        # This is what happens when the app starts
        app = KnowledgeAppWebEngine()
        
        print("✅ KnowledgeAppWebEngine initialized successfully")
        
        # Check if MCQ Manager is available through bridge
        if hasattr(app, 'bridge') and hasattr(app.bridge, 'mcq_manager'):
            print("✅ MCQ Manager available in KnowledgeAppWebEngine")
        else:
            print("❌ MCQ Manager NOT available in KnowledgeAppWebEngine")
            return False
        
        # Test topic analysis integration
        test_topic = "atoms"
        print(f"Testing topic analysis for: {test_topic}")
        
        # This simulates what happens when user types in the topic box
        if hasattr(app, 'analyzeTopic'):
            print("✅ analyzeTopic method available")
        else:
            print("❌ analyzeTopic method NOT available")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ UI-Backend integration test failed: {e}")
        return False

def main():
    """Run comprehensive pipeline tests"""
    print("🔥 COMPREHENSIVE UI-TO-BACKEND PIPELINE TEST")
    print("=" * 60)
    print("This test simulates the EXACT user experience to find real issues")
    print("=" * 60)
    
    results = []
    
    # Test 1: Token streaming pipeline
    results.append(test_token_streaming_pipeline())
    
    # Test 2: Topic analysis pipeline  
    results.append(test_topic_analysis_pipeline())
    
    # Test 3: Question generation pipeline
    results.append(test_question_generation_pipeline())
    
    # Test 4: UI-backend integration
    results.append(test_ui_backend_integration())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 60)
    
    test_names = [
        "Token Streaming Pipeline",
        "Topic Analysis Pipeline", 
        "Question Generation Pipeline",
        "UI-Backend Integration"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed != total:
        print("\n🔥 ISSUES IDENTIFIED:")
        for i, (name, result) in enumerate(zip(test_names, results)):
            if not result:
                print(f"  ❌ {name} - NEEDS FIXING")
        
        print("\n💡 NEXT STEPS:")
        print("  1. Fix the failing pipeline components")
        print("  2. Test each fix individually") 
        print("  3. Re-run this comprehensive test")
        print("  4. Only then test the full UI")
    else:
        print("\n🎉 ALL PIPELINE TESTS PASSED!")
        print("The app should work correctly now.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
