#!/usr/bin/env python3
"""
Test the final integration - exactly what the app uses
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_generate_mcq_unified():
    """Test the exact function the app uses"""
    print("🚀 Testing generate_mcq_unified() - Exact App Integration")
    print("=" * 60)
    
    try:
        from knowledge_app.core.unified_inference_manager import generate_mcq_unified, initialize_unified_inference

        # Initialize the system first
        print("🔧 Initializing unified inference system...")
        init_success = initialize_unified_inference()
        if not init_success:
            print("❌ Failed to initialize unified inference system")
            return False
        print("✅ Unified inference system initialized")

        # Test with the problematic inputs
        test_cases = [
            ("magnetism", "medium"),
            ("dfs", "medium"), 
            ("sdfsf", "easy"),
            ("physics", "medium")
        ]
        
        for topic, difficulty in test_cases:
            print(f"\n🔍 Testing: '{topic}' (difficulty: {difficulty})")
            
            try:
                result = generate_mcq_unified(
                    topic=topic,
                    difficulty=difficulty,
                    question_type="mixed",
                    timeout=30.0
                )
                
                if result:
                    print(f"✅ SUCCESS for '{topic}'!")
                    print(f"   📝 Original: {result.get('original_input', topic)}")
                    print(f"   🎯 Resolved: {result.get('resolved_topic', 'N/A')}")
                    print(f"   ❓ Question: {result.get('question', 'N/A')[:80]}...")
                    print(f"   🔢 Options: {len(result.get('options', []))} choices")
                    print(f"   ✅ Correct: {result.get('correct', 'N/A')}")
                    return True  # At least one success
                else:
                    print(f"❌ FAILED for '{topic}' - No result returned")
                    
            except Exception as e:
                print(f"❌ EXCEPTION for '{topic}': {e}")
                import traceback
                traceback.print_exc()
        
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🧠 Final Integration Test - Exact App Path")
    print("=" * 60)
    
    success = test_generate_mcq_unified()
    
    if success:
        print("\n🎉 SUCCESS! The app integration works!")
        print("💡 Your app should now generate questions for ANY input!")
        print("🚀 Try typing 'magnetism', 'dfs', or any random text in your app!")
    else:
        print("\n❌ Integration test failed.")
        print("💡 The app may still have issues with question generation.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
