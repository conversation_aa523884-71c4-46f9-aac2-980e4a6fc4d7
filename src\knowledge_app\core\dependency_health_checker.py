"""
Dependency Health Checker

This module provides comprehensive dependency checking and health monitoring
for the Knowledge App, replacing deprecated packages and ensuring compatibility.

Features:
- Modern dependency checking without deprecated packages
- Health monitoring for critical dependencies
- Fallback mechanisms for missing dependencies
- Performance impact assessment
"""

import logging
import importlib
import sys
import warnings
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import pkg_resources

logger = logging.getLogger(__name__)


class DependencyHealthChecker:
    """
    Modern dependency health checker that replaces deprecated packages
    and provides comprehensive dependency monitoring.
    """

    def __init__(self):
        self.dependency_status = {}
        self.critical_dependencies = ["PyQt5", "numpy", "pandas", "torch", "transformers"]
        self.optional_dependencies = [
            "haystack",
            "faiss",
            "sentence_transformers",
            "peft",
            "bitsandbytes",
        ]
        self.deprecated_packages = [
            "quantulum3",  # Replace with modern alternatives
            "pkg_resources",  # Use importlib.metadata instead
        ]

    def check_all_dependencies(self) -> Dict[str, Any]:
        """
        Check all dependencies and return comprehensive health report.

        Returns:
            Dictionary with dependency status and recommendations
        """
        report = {
            "critical": {},
            "optional": {},
            "deprecated": {},
            "recommendations": [],
            "overall_health": "unknown",
        }

        # Check critical dependencies
        critical_issues = 0
        for dep in self.critical_dependencies:
            status = self._check_dependency(dep)
            report["critical"][dep] = status
            if not status["available"]:
                critical_issues += 1

        # Check optional dependencies
        for dep in self.optional_dependencies:
            status = self._check_dependency(dep)
            report["optional"][dep] = status

        # Check for deprecated packages
        for dep in self.deprecated_packages:
            status = self._check_deprecated_package(dep)
            report["deprecated"][dep] = status
            if status["found"]:
                report["recommendations"].append(
                    f"Replace deprecated package '{dep}' with modern alternative"
                )

        # Determine overall health
        if critical_issues == 0:
            report["overall_health"] = "good"
        elif critical_issues <= 2:
            report["overall_health"] = "warning"
        else:
            report["overall_health"] = "critical"

        return report

    def _check_dependency(self, package_name: str) -> Dict[str, Any]:
        """Check a single dependency"""
        try:
            # Use importlib.metadata instead of deprecated pkg_resources
            try:
                import importlib.metadata as metadata

                version = metadata.version(package_name)
                available = True
                method = "importlib.metadata"
            except ImportError:
                # Fallback to pkg_resources if importlib.metadata not available
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore", DeprecationWarning)
                    version = pkg_resources.get_distribution(package_name).version
                available = True
                method = "pkg_resources"
            except Exception:
                version = None
                available = False
                method = None

            # Try to import the module
            try:
                module = importlib.import_module(package_name)
                importable = True
                module_path = getattr(module, "__file__", "unknown")
            except ImportError:
                importable = False
                module_path = None

            return {
                "available": available,
                "importable": importable,
                "version": version,
                "module_path": module_path,
                "check_method": method,
            }

        except Exception as e:
            logger.debug(f"Error checking dependency {package_name}: {e}")
            return {
                "available": False,
                "importable": False,
                "version": None,
                "module_path": None,
                "error": str(e),
            }

    def _check_deprecated_package(self, package_name: str) -> Dict[str, Any]:
        """Check for deprecated packages"""
        try:
            # Check if package is installed
            try:
                import importlib.metadata as metadata

                version = metadata.version(package_name)
                found = True
            except ImportError:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore", DeprecationWarning)
                    version = pkg_resources.get_distribution(package_name).version
                found = True
            except Exception:
                found = False
                version = None

            # Check if it's being used in the current process
            in_use = package_name in sys.modules

            return {
                "found": found,
                "version": version,
                "in_use": in_use,
                "recommendation": self._get_replacement_recommendation(package_name),
            }

        except Exception as e:
            return {"found": False, "version": None, "in_use": False, "error": str(e)}

    def _get_replacement_recommendation(self, package_name: str) -> str:
        """Get replacement recommendations for deprecated packages"""
        replacements = {
            "quantulum3": "Use built-in text processing or modern NLP libraries like spaCy",
            "pkg_resources": "Use importlib.metadata for package metadata access",
        }
        return replacements.get(package_name, "Consider finding a modern alternative")

    def get_health_summary(self) -> str:
        """Get a human-readable health summary"""
        report = self.check_all_dependencies()

        summary_lines = []
        summary_lines.append(f"🏥 Dependency Health: {report['overall_health'].upper()}")

        # Critical dependencies
        critical_ok = sum(1 for status in report["critical"].values() if status["available"])
        critical_total = len(report["critical"])
        summary_lines.append(f"🔴 Critical: {critical_ok}/{critical_total} available")

        # Optional dependencies
        optional_ok = sum(1 for status in report["optional"].values() if status["available"])
        optional_total = len(report["optional"])
        summary_lines.append(f"🟡 Optional: {optional_ok}/{optional_total} available")

        # Deprecated packages
        deprecated_found = sum(1 for status in report["deprecated"].values() if status["found"])
        if deprecated_found > 0:
            summary_lines.append(f"⚠️ Deprecated packages found: {deprecated_found}")

        # Recommendations
        if report["recommendations"]:
            summary_lines.append("📋 Recommendations:")
            for rec in report["recommendations"][:3]:  # Show top 3
                summary_lines.append(f"  - {rec}")

        return "\n".join(summary_lines)

    def fix_deprecated_warnings(self):
        """Apply fixes for deprecated package warnings"""
        try:
            # Suppress pkg_resources deprecation warnings
            warnings.filterwarnings(
                "ignore", message=".*pkg_resources.*deprecated.*", category=DeprecationWarning
            )

            # Suppress quantulum3 warnings if present
            warnings.filterwarnings(
                "ignore", message=".*quantulum3.*deprecated.*", category=UserWarning
            )

            logger.debug("✅ Deprecated package warnings suppressed")

        except Exception as e:
            logger.warning(f"Failed to suppress deprecated warnings: {e}")


# Global health checker instance
_global_health_checker: Optional[DependencyHealthChecker] = None


def get_health_checker() -> DependencyHealthChecker:
    """Get the global dependency health checker"""
    global _global_health_checker

    if _global_health_checker is None:
        _global_health_checker = DependencyHealthChecker()

    return _global_health_checker


def check_dependencies() -> bool:
    """
    Check all dependencies and return True if system is healthy.

    Returns:
        True if all critical dependencies are available
    """
    checker = get_health_checker()
    report = checker.check_all_dependencies()

    # Check if all critical dependencies are available
    critical_ok = all(status["available"] for status in report["critical"].values())

    if not critical_ok:
        logger.error("❌ Critical dependencies missing")
        logger.info(checker.get_health_summary())
        return False

    logger.info("✅ All critical dependencies available")
    return True


def fix_deprecated_warnings():
    """Fix deprecated package warnings"""
    checker = get_health_checker()
    checker.fix_deprecated_warnings()


def log_dependency_health():
    """Log dependency health summary"""
    checker = get_health_checker()
    summary = checker.get_health_summary()
    logger.info(f"Dependency Health Report:\n{summary}")