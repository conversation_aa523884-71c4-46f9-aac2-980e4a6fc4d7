#!/usr/bin/env python3
"""
Direct test of DeepSeek pipeline to bypass unified manager fallbacks
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_deepseek_direct():
    print("🧠 DIRECT DEEPSEEK PIPELINE TEST")
    print("=" * 50)
    print("Testing DeepSeek two-model pipeline directly")
    print("Bypassing unified manager fallbacks")
    print("=" * 50)
    
    try:
        from knowledge_app.core.deepseek_integration import DeepSeekTwoModelPipeline
        
        # Create DeepSeek pipeline
        print("🔧 Creating DeepSeek pipeline...")
        pipeline = DeepSeekTwoModelPipeline()
        
        # Check availability
        print("🔍 Checking pipeline availability...")
        if not pipeline.is_available():
            print("❌ DeepSeek pipeline not available!")
            print(f"   Available models: {pipeline.available_models}")
            print(f"   Thinking model: {pipeline.thinking_model}")
            print(f"   JSON model: {pipeline.json_model}")
            return
        
        print("✅ DeepSeek pipeline available!")
        print(f"   Thinking model: {pipeline.thinking_model}")
        print(f"   JSON model: {pipeline.json_model}")
        
        # Generate expert question
        print("\n🧠 Generating expert numerical question...")
        print("   Topic: atoms")
        print("   Difficulty: expert")
        print("   Type: numerical")
        
        result = pipeline.generate_expert_question(
            topic="atoms",
            difficulty="expert", 
            question_type="numerical",
            context=None
        )
        
        if result:
            print("\n✅ DeepSeek generation successful!")
            print("-" * 40)
            print(f"Question: {result.get('question', 'N/A')}")
            print()
            
            options = result.get('options', {})
            if isinstance(options, dict):
                for key, value in options.items():
                    print(f"{key}. {value}")
            elif isinstance(options, list):
                for i, option in enumerate(options):
                    letter = chr(65 + i)  # A, B, C, D
                    print(f"{letter}. {option}")
            
            print()
            print(f"Correct Answer: {result.get('correct', result.get('correct_answer', 'N/A'))}")
            print(f"Explanation: {result.get('explanation', 'N/A')}")
            
            # Analysis
            print("\n🔍 Analysis:")
            print("-" * 40)
            
            question_text = result.get('question', '').lower()
            
            # Check for numerical content
            numerical_terms = ['calculate', 'number', 'value', 'formula', 'equation', 'units', 'mass', 'energy', 'wavelength', 'frequency', 'joules', 'nm', 'ev', 'constant']
            numerical_found = [term for term in numerical_terms if term in question_text]
            
            # Check for expert-level content  
            expert_terms = ['quantum', 'relativistic', 'orbital', 'electron', 'nuclear', 'atomic', 'molecular', 'spectroscopy', 'ionization', 'excitation', 'rydberg', 'fine structure', 'hyperfine']
            expert_found = [term for term in expert_terms if term in question_text]
            
            print(f"🔢 Numerical content: {len(numerical_found) > 0} - Found: {numerical_found}")
            print(f"🎓 Expert-level content: {len(expert_found) > 0} - Found: {expert_found}")
            
            # Check if this is the old basic question
            if "ground state electron configuration" in question_text:
                print("⚠️  WARNING: Still getting basic electron configuration question!")
                print("   DeepSeek pipeline may be falling back to other generators")
            else:
                print("🎉 SUCCESS: Got a different question - DeepSeek working!")
                
        else:
            print("❌ DeepSeek generation failed!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_deepseek_direct()
