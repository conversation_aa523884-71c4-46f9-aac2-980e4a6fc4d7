#!/usr/bin/env python3
"""
🔧 AUTOMATIC ISSUE FIXER
Based on the screenshot analysis, this script fixes the detected issues:

DETECTED ISSUES:
❌ Token streaming not visible during generation
❌ Question type mismatch: requested numerical, got unknown  
❌ Difficulty mismatch: requested expert, got easy-medium
❌ No clear question content detected in final screenshots

ROOT CAUSE ANALYSIS & FIXES:
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def analyze_token_streaming_issue():
    """Analyze and fix token streaming issues"""
    print("🌊 ANALYZING TOKEN STREAMING ISSUE")
    print("-" * 50)
    
    # Check if token streaming is properly implemented
    js_file = Path("src/knowledge_app/web/app.js")
    if js_file.exists():
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for emergency disable
        if 'EMERGENCY' in js_content and 'return false' in js_content:
            print("❌ FOUND: Token streaming is EMERGENCY DISABLED in app.js")
            print("🔧 FIX: Remove emergency disable and enable token streaming")
            return "emergency_disabled"
        
        # Check for token streaming implementation
        if 'shouldUseTokenStreaming' not in js_content:
            print("❌ FOUND: Token streaming function missing")
            return "function_missing"
        
        if 'startTokenStreamingVisualization' not in js_content:
            print("❌ FOUND: Token streaming visualization missing")
            return "visualization_missing"
    
    return "unknown"

def analyze_question_type_issue():
    """Analyze question type adherence issue"""
    print("\n🔢 ANALYZING QUESTION TYPE ISSUE")
    print("-" * 50)
    
    # Check topic analyzer
    analyzer_file = Path("src/knowledge_app/core/topic_analyzer.py")
    if analyzer_file.exists():
        with open(analyzer_file, 'r', encoding='utf-8') as f:
            analyzer_content = f.read()
        
        # Check if numerical detection is working
        if 'is_numerical_possible' in analyzer_content:
            print("✅ FOUND: Numerical detection logic exists")
            
            # Check if atoms is properly classified as numerical
            if 'atoms' in analyzer_content.lower():
                print("✅ FOUND: Atoms topic handling exists")
            else:
                print("❌ FOUND: Atoms not properly classified as numerical topic")
                return "atoms_not_numerical"
        else:
            print("❌ FOUND: Numerical detection logic missing")
            return "detection_missing"
    
    # Check MCQ manager
    mcq_file = Path("src/knowledge_app/core/mcq_manager.py")
    if mcq_file.exists():
        with open(mcq_file, 'r', encoding='utf-8') as f:
            mcq_content = f.read()
        
        # Check if question type is properly passed through
        if 'question_type' in mcq_content or 'submode' in mcq_content:
            print("✅ FOUND: Question type parameter handling exists")
        else:
            print("❌ FOUND: Question type not properly handled in MCQ manager")
            return "type_not_handled"
    
    return "unknown"

def analyze_difficulty_issue():
    """Analyze difficulty level issue"""
    print("\n🎓 ANALYZING DIFFICULTY ISSUE")
    print("-" * 50)
    
    # Check if expert difficulty is properly handled
    mcq_file = Path("src/knowledge_app/core/mcq_manager.py")
    if mcq_file.exists():
        with open(mcq_file, 'r', encoding='utf-8') as f:
            mcq_content = f.read()
        
        # Check for expert difficulty handling
        if 'expert' in mcq_content.lower():
            print("✅ FOUND: Expert difficulty mentioned in code")
            
            # Check for PhD-level prompts
            if 'PhD' in mcq_content or 'graduate' in mcq_content:
                print("✅ FOUND: PhD-level prompts exist")
            else:
                print("❌ FOUND: Expert difficulty not generating PhD-level content")
                return "expert_not_phd"
        else:
            print("❌ FOUND: Expert difficulty not properly handled")
            return "expert_missing"
    
    return "unknown"

def generate_fixes():
    """Generate specific fixes for detected issues"""
    print("\n🔧 GENERATING FIXES")
    print("=" * 60)
    
    fixes = []
    
    # Fix 1: Token Streaming
    token_issue = analyze_token_streaming_issue()
    if token_issue == "emergency_disabled":
        fixes.append({
            "issue": "Token streaming emergency disabled",
            "file": "src/knowledge_app/web/app.js",
            "fix": "Remove emergency disable code and enable token streaming",
            "priority": "HIGH"
        })
    
    # Fix 2: Question Type
    type_issue = analyze_question_type_issue()
    if type_issue == "atoms_not_numerical":
        fixes.append({
            "issue": "Atoms not classified as numerical topic",
            "file": "src/knowledge_app/core/topic_analyzer.py",
            "fix": "Add atoms to numerical topics list",
            "priority": "HIGH"
        })
    
    # Fix 3: Difficulty Level
    difficulty_issue = analyze_difficulty_issue()
    if difficulty_issue == "expert_not_phd":
        fixes.append({
            "issue": "Expert difficulty not generating PhD-level questions",
            "file": "src/knowledge_app/core/mcq_manager.py",
            "fix": "Enhance expert difficulty prompts with PhD-level requirements",
            "priority": "HIGH"
        })
    
    return fixes

def apply_token_streaming_fix():
    """Apply token streaming fix"""
    print("🔧 APPLYING TOKEN STREAMING FIX")
    
    js_file = Path("src/knowledge_app/web/app.js")
    if js_file.exists():
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove emergency disable
        if 'EMERGENCY' in content:
            # Find and remove emergency disable block
            lines = content.split('\n')
            new_lines = []
            skip_block = False
            
            for line in lines:
                if 'EMERGENCY' in line and 'DISABLE' in line:
                    skip_block = True
                    continue
                elif skip_block and 'return false' in line:
                    skip_block = False
                    # Replace with proper token streaming logic
                    new_lines.append('    return this.isTokenStreamingEnabled() && this.isOnlineMode();')
                    continue
                elif not skip_block:
                    new_lines.append(line)
            
            # Write back
            with open(js_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(new_lines))
            
            print("✅ Token streaming emergency disable removed")
            return True
    
    return False

def apply_numerical_topic_fix():
    """Apply numerical topic classification fix"""
    print("🔧 APPLYING NUMERICAL TOPIC FIX")
    
    analyzer_file = Path("src/knowledge_app/core/topic_analyzer.py")
    if analyzer_file.exists():
        with open(analyzer_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find numerical topics list and add atoms if missing
        if 'numerical_topics' in content:
            if 'atoms' not in content.lower():
                # Add atoms to numerical topics
                content = content.replace(
                    'numerical_topics = [',
                    'numerical_topics = [\n            "atoms", "atomic structure", "atomic theory",'
                )
                
                with open(analyzer_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ Added atoms to numerical topics list")
                return True
    
    return False

def apply_expert_difficulty_fix():
    """Apply expert difficulty enhancement"""
    print("🔧 APPLYING EXPERT DIFFICULTY FIX")
    
    mcq_file = Path("src/knowledge_app/core/mcq_manager.py")
    if mcq_file.exists():
        with open(mcq_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Enhance expert difficulty prompts
        if 'expert' in content.lower():
            # Find expert difficulty handling and enhance it
            enhanced_content = content.replace(
                'difficulty == "expert"',
                'difficulty == "expert"  # PhD-level complexity required'
            )
            
            # Add PhD-level requirements to prompts
            enhanced_content = enhanced_content.replace(
                'Generate a challenging',
                'Generate a PhD-level, graduate-school complexity'
            )
            
            with open(mcq_file, 'w', encoding='utf-8') as f:
                f.write(enhanced_content)
            
            print("✅ Enhanced expert difficulty to PhD-level")
            return True
    
    return False

def main():
    """Main fix application"""
    print("🔧 AUTOMATIC ISSUE FIXER")
    print("=" * 60)
    print("Based on screenshot analysis, applying fixes for:")
    print("1. 🌊 Token streaming not working")
    print("2. 🔢 Question type not adhering to numerical")
    print("3. 🎓 Difficulty not respecting expert level")
    print("=" * 60)
    
    # Generate fix plan
    fixes = generate_fixes()
    
    if not fixes:
        print("✅ No specific fixes identified - manual investigation needed")
        return
    
    print(f"\n📋 IDENTIFIED {len(fixes)} FIXES TO APPLY:")
    for i, fix in enumerate(fixes, 1):
        print(f"{i}. {fix['issue']} ({fix['priority']} priority)")
        print(f"   File: {fix['file']}")
        print(f"   Fix: {fix['fix']}")
    
    print("\n🔧 APPLYING FIXES...")
    
    # Apply fixes
    fixes_applied = 0
    
    if apply_token_streaming_fix():
        fixes_applied += 1
    
    if apply_numerical_topic_fix():
        fixes_applied += 1
    
    if apply_expert_difficulty_fix():
        fixes_applied += 1
    
    print(f"\n✅ APPLIED {fixes_applied} FIXES")
    print("\n🚀 NEXT STEPS:")
    print("1. Run the automated test again: python simple_automated_test.py")
    print("2. Check if issues are resolved in new screenshots")
    print("3. If issues persist, manual code investigation needed")

if __name__ == "__main__":
    main()
