#!/usr/bin/env python3
"""
Test UnifiedInferenceManager initialization
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_unified_manager_init():
    """Test UnifiedInferenceManager initialization and wait for completion"""
    print("🔧 TESTING UNIFIED INFERENCE MANAGER INITIALIZATION")
    print("=" * 60)
    
    try:
        from knowledge_app.core.unified_inference_manager import get_unified_inference_manager
        
        print("\n1️⃣ Creating UnifiedInferenceManager...")
        manager = get_unified_inference_manager()
        
        print("\n2️⃣ Initial status...")
        status = manager.get_status()
        print(f"   📊 Initial state: {status.get('state', 'unknown')}")
        print(f"   🏠 Local available: {status.get('local_available', False)}")
        print(f"   ☁️ Cloud available: {status.get('cloud_available', False)}")
        
        print("\n3️⃣ Waiting for initialization to complete...")
        max_wait = 30  # 30 seconds max
        wait_time = 0
        
        while wait_time < max_wait:
            status = manager.get_status()
            state = status.get('state', 'unknown')
            
            print(f"   ⏱️ {wait_time}s - State: {state}")
            
            if state == 'ready':
                print("   ✅ Initialization complete!")
                break
            elif state == 'error':
                print("   ❌ Initialization failed!")
                break
            
            await asyncio.sleep(2)
            wait_time += 2
        
        print("\n4️⃣ Final status...")
        final_status = manager.get_status()
        print(f"   📊 Final state: {final_status.get('state', 'unknown')}")
        print(f"   🏠 Local available: {final_status.get('local_available', False)}")
        print(f"   ☁️ Cloud available: {final_status.get('cloud_available', False)}")
        
        # Check individual model status
        models = final_status.get('models', {})
        print(f"\n5️⃣ Model status:")
        for model_name, available in models.items():
            status_icon = "✅" if available else "❌"
            print(f"   {status_icon} {model_name}: {available}")
        
        # Test if local is available
        local_available = final_status.get('local_available', False)
        if local_available:
            print("\n🎉 SUCCESS: Local models are available!")
            
            # Try a simple generation test
            print("\n6️⃣ Testing simple generation...")
            try:
                from knowledge_app.core.unified_inference_manager import InferenceRequest
                
                request = InferenceRequest(
                    request_id="test_generation",
                    operation="generate_mcq",
                    params={
                        "topic": "basic math",
                        "difficulty": "easy",
                        "question_type": "multiple_choice",
                        "context": None,
                        "adapter_name": None
                    },
                    timeout=30.0
                )
                
                print("   🔄 Sending test request...")
                result = await manager._handle_mcq_generation(request)
                
                if result:
                    print("   ✅ Test generation successful!")
                    print(f"   ❓ Question: {result.get('question', 'N/A')[:100]}...")
                    return True
                else:
                    print("   ❌ Test generation failed - no result")
                    return False
                    
            except Exception as e:
                print(f"   ❌ Test generation error: {e}")
                return False
        else:
            print("\n❌ FAILED: Local models are not available")
            return False
            
    except Exception as e:
        print(f"❌ UnifiedInferenceManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_unified_manager_init())
    if success:
        print("\n🎉 SUCCESS: UnifiedInferenceManager is working!")
    else:
        print("\n❌ FAILED: UnifiedInferenceManager has issues")
