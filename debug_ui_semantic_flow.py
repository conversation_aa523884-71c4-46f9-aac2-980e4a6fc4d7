#!/usr/bin/env python3
"""
🔍 Debug UI Semantic Flow
Real-time debugging to see what happens when you type in the UI
"""

import sys
import os
import time
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup detailed logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('semantic_debug.log')
    ]
)

def test_direct_semantic_calls():
    """Test direct semantic mapping calls to verify it works"""
    print("🔍 TESTING DIRECT SEMANTIC MAPPING")
    print("=" * 50)
    
    test_inputs = ["FHD", "HD", "SVD", "MBT", "CBT"]
    
    try:
        from knowledge_app.core.intelligent_semantic_mapper import get_semantic_mapper
        
        mapper = get_semantic_mapper()
        print(f"✅ Semantic mapper initialized: {mapper.model_name}")
        
        for test_input in test_inputs:
            print(f"\n🧪 Testing '{test_input}':")
            
            try:
                result = mapper.map_topic_semantically(test_input)
                print(f"   ✅ Result: {result.expanded_topic} → {result.question_type}")
                print(f"   Confidence: {result.confidence:.2f}")
                print(f"   Is abbreviation: {result.is_abbreviation}")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to test semantic mapping: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_topic_analyzer_integration():
    """Test TopicAnalyzer integration"""
    print("\n🔗 TESTING TOPIC ANALYZER INTEGRATION")
    print("=" * 50)
    
    test_inputs = ["FHD", "HD", "SVD", "MBT", "CBT"]
    
    try:
        from knowledge_app.core.topic_analyzer import get_topic_analyzer
        
        analyzer = get_topic_analyzer()
        print(f"✅ TopicAnalyzer created")
        print(f"   Use semantic mapping: {analyzer.use_semantic_mapping}")
        print(f"   Semantic mapper available: {analyzer.semantic_mapper is not None}")
        
        for test_input in test_inputs:
            print(f"\n🧪 Testing '{test_input}' through TopicAnalyzer:")
            
            try:
                profile = analyzer.get_topic_profile(test_input)
                print(f"   ✅ Detected type: {profile.get('detected_type')}")
                print(f"   Expanded: {profile.get('expanded_topic', 'N/A')}")
                print(f"   Semantic analysis: {profile.get('semantic_analysis', False)}")
                print(f"   Confidence: {profile.get('confidence')}")
            except Exception as e:
                print(f"   ❌ Error: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to test TopicAnalyzer: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_ui_flow():
    """Simulate exactly what the UI does"""
    print("\n🎮 SIMULATING UI FLOW")
    print("=" * 50)
    
    test_inputs = ["FHD", "HD", "SVD"]
    
    try:
        # This simulates exactly what happens when you type in the UI:
        # 1. JavaScript calls pythonBridge.analyzeTopic(topic)
        # 2. This calls webengine_app.analyzeTopic(topic)
        # 3. Which calls topic_analyzer.get_topic_profile(topic)
        
        from knowledge_app.core.topic_analyzer import get_topic_analyzer
        
        # Get the same analyzer instance the UI would use
        analyzer = get_topic_analyzer()
        
        for test_input in test_inputs:
            print(f"\n🎮 Simulating UI input: '{test_input}'")
            print(f"   Step 1: User types '{test_input}' in topic field")
            print(f"   Step 2: JavaScript calls analyzeTopic('{test_input}')")
            print(f"   Step 3: Python calls get_topic_profile('{test_input}')")
            
            try:
                # This is exactly what the UI flow does
                profile = analyzer.get_topic_profile(test_input.strip())
                
                print(f"   Step 4: Analysis complete!")
                print(f"   ✅ Result:")
                print(f"      - Type: {profile.get('detected_type')}")
                print(f"      - Expanded: {profile.get('expanded_topic', 'N/A')}")
                print(f"      - Conceptual possible: {profile.get('is_conceptual_possible')}")
                print(f"      - Numerical possible: {profile.get('is_numerical_possible')}")
                print(f"      - Semantic used: {profile.get('semantic_analysis', False)}")
                
                # Check UI recommendations
                ui_recs = profile.get('ui_recommendations', {})
                if ui_recs:
                    print(f"      - UI should highlight: {[k for k, v in ui_recs.items() if v and 'highlight' in k]}")
                
            except Exception as e:
                print(f"   ❌ UI flow failed: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to simulate UI flow: {e}")
        return False

def check_phi_model_availability():
    """Check if Phi model is actually available and working"""
    print("\n🤖 CHECKING PHI MODEL AVAILABILITY")
    print("=" * 50)
    
    try:
        import requests
        
        # Check Ollama status
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = [m['name'] for m in response.json().get('models', [])]
            print(f"✅ Ollama running with {len(models)} models")
            
            # Check for Phi models
            phi_models = [m for m in models if 'phi' in m.lower()]
            if phi_models:
                print(f"✅ Phi models available: {phi_models}")
                
                # Test a simple call to Phi
                test_response = requests.post(
                    "http://localhost:11434/api/generate",
                    json={
                        "model": phi_models[0],
                        "prompt": "What does 'FHD' stand for?",
                        "stream": False,
                        "options": {"num_predict": 50}
                    },
                    timeout=30
                )
                
                if test_response.status_code == 200:
                    result = test_response.json().get('response', '')
                    print(f"✅ Phi model test successful: {result[:100]}...")
                    return True
                else:
                    print(f"❌ Phi model test failed: {test_response.status_code}")
                    return False
            else:
                print(f"❌ No Phi models found. Available: {models}")
                return False
        else:
            print(f"❌ Ollama not responding: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Phi model: {e}")
        return False

def main():
    """Main debug function"""
    print("🔍 UI SEMANTIC FLOW DEBUG")
    print("=" * 60)
    print("This will help debug why FHD, HD, SVD aren't being recognized")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # Test 1: Phi model availability
    if check_phi_model_availability():
        success_count += 1
        print("✅ Test 1/4: Phi model available and working")
    else:
        print("❌ Test 1/4: Phi model issues detected")
    
    # Test 2: Direct semantic mapping
    if test_direct_semantic_calls():
        success_count += 1
        print("✅ Test 2/4: Direct semantic mapping working")
    else:
        print("❌ Test 2/4: Direct semantic mapping failed")
    
    # Test 3: TopicAnalyzer integration
    if test_topic_analyzer_integration():
        success_count += 1
        print("✅ Test 3/4: TopicAnalyzer integration working")
    else:
        print("❌ Test 3/4: TopicAnalyzer integration failed")
    
    # Test 4: UI flow simulation
    if simulate_ui_flow():
        success_count += 1
        print("✅ Test 4/4: UI flow simulation working")
    else:
        print("❌ Test 4/4: UI flow simulation failed")
    
    print(f"\n🎯 DEBUG RESULTS: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 EVERYTHING WORKING - UI CONNECTION ISSUE!")
        print("   The backend is fine, check:")
        print("   1. App restart needed")
        print("   2. Browser cache/refresh")
        print("   3. JavaScript console errors")
    elif success_count >= 2:
        print("✅ MOSTLY WORKING - SOME ISSUES")
        print("   Check the failed tests above")
    else:
        print("❌ MAJOR ISSUES DETECTED")
        print("   Multiple components failing")
    
    print(f"\n📋 NEXT STEPS:")
    print(f"   1. Check semantic_debug.log for detailed logs")
    print(f"   2. Restart your app completely")
    print(f"   3. Try typing 'FHD' slowly in the UI")
    print(f"   4. Check browser console (F12) for errors")

if __name__ == "__main__":
    main()
