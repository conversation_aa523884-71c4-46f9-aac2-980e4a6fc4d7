#!/usr/bin/env python3
"""
Test to verify that questions are now varied and not hardcoded
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_question_variety():
    """Test that multiple generations produce different questions"""
    print("🔄 TESTING: Question Variety and Randomness")
    print("=" * 60)
    
    try:
        from knowledge_app.core.unified_inference_manager import generate_mcq_unified
        
        # Generate multiple questions about the same topic
        topic = "atoms"
        questions = []
        
        print(f"🧪 Generating 3 questions about '{topic}' to test variety...")
        
        for i in range(3):
            print(f"\n🔄 Generation {i+1}/3...")
            
            try:
                result = generate_mcq_unified(
                    topic=topic,
                    difficulty="medium",
                    question_type="mixed",
                    timeout=30.0
                )
                
                if result and result.get('question'):
                    question_text = result['question']
                    questions.append(question_text)
                    print(f"   ✅ Generated: {question_text[:80]}...")
                else:
                    print(f"   ❌ Failed to generate question {i+1}")
                    
            except Exception as e:
                print(f"   ❌ Error generating question {i+1}: {e}")
        
        # Analyze variety
        print(f"\n📊 ANALYSIS:")
        print(f"   Total questions generated: {len(questions)}")
        
        if len(questions) >= 2:
            unique_questions = set(questions)
            print(f"   Unique questions: {len(unique_questions)}")
            
            if len(unique_questions) == len(questions):
                print("   ✅ ALL QUESTIONS ARE DIFFERENT! 🎉")
                print("   🚀 Randomness is working correctly!")
                return True
            elif len(unique_questions) > 1:
                print("   ⚠️ Some variety detected, but some duplicates")
                print("   🔧 May need to increase temperature further")
                return True
            else:
                print("   ❌ ALL QUESTIONS ARE IDENTICAL!")
                print("   🚨 Hardcoded questions or deterministic generation detected")
                return False
        else:
            print("   ❌ Not enough questions generated to test variety")
            return False
            
        # Show all questions for comparison
        print(f"\n📝 ALL GENERATED QUESTIONS:")
        for i, q in enumerate(questions, 1):
            print(f"   {i}. {q}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🎲 QUESTION VARIETY TEST")
    print("=" * 60)
    print("This test verifies that:")
    print("1. Questions are generated by AI (not hardcoded)")
    print("2. Multiple generations produce different questions")
    print("3. Randomness settings are working correctly")
    print()
    
    success = test_question_variety()
    
    if success:
        print("\n🎉 SUCCESS: Question generation is working with variety!")
        print("🚀 Your app should now generate different questions each time!")
        return 0
    else:
        print("\n❌ FAILED: Questions are still identical or hardcoded")
        print("🔧 May need to adjust temperature or check for remaining hardcoded content")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
