#!/usr/bin/env python3
"""
Test DeepSeek R1 direct output to see what it's actually generating
"""

import sys
import os
import requests
import json
from pathlib import Path

def test_deepseek_direct():
    """Test DeepSeek R1 directly to see what it generates"""
    
    print("🧠 TESTING DEEPSEEK R1 DIRECT OUTPUT")
    print("=" * 60)
    
    try:
        # Test DeepSeek R1 directly with PhD-level prompt
        prompt = """You are DeepSeek R1, an advanced reasoning model. Generate a PhD-level NUMERICAL question about atomic physics.

REQUIREMENTS:
- Must involve calculations, formulas, or quantitative analysis
- PhD-level complexity (relativistic effects, quantum field theory, many-body systems)
- Include specific numerical values and units
- Focus on advanced topics like fine structure, hyperfine splitting, relativistic corrections
- NOT basic electron configuration questions

TOPIC: Atoms (advanced quantum mechanics)
DIFFICULTY: Expert/PhD level
TYPE: Numerical

Generate a question that would challenge a physics PhD student."""

        print("🚀 Sending prompt to DeepSeek R1...")
        print(f"📝 Prompt length: {len(prompt)} characters")
        
        payload = {
            "model": "deepseek-r1:14b",
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.8,
                "num_predict": 2000,
                "top_p": 0.9,
                "top_k": 40
            }
        }
        
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=payload,
            timeout=600  # 10 minute timeout
        )
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            
            print(f"✅ DeepSeek R1 responded!")
            print(f"📄 Response length: {len(response_text)} characters")
            print("\n" + "=" * 60)
            print("🧠 DEEPSEEK R1 RAW OUTPUT:")
            print("=" * 60)
            print(response_text)
            print("=" * 60)
            
            # Analyze the output
            phd_terms = [
                'relativistic', 'dirac', 'perturbation', 'fine structure', 
                'hyperfine', 'quantum field', 'many-body', 'correlation',
                'spin-orbit', 'zeeman', 'stark', 'lamb shift', 'casimir',
                'qed', 'renormalization', 'feynman', 'scattering'
            ]
            
            numerical_terms = [
                'calculate', 'determine', 'find', 'compute', 'energy', 
                'wavelength', 'frequency', 'mass', 'charge', 'momentum',
                'cross-section', 'probability', 'rate', 'lifetime'
            ]
            
            basic_terms = [
                'electron configuration', 'ground state', 'valence', 
                'orbital', 'shell', 'subshell', '1s', '2s', '2p'
            ]
            
            response_lower = response_text.lower()
            
            found_phd = [term for term in phd_terms if term in response_lower]
            found_numerical = [term for term in numerical_terms if term in response_lower]
            found_basic = [term for term in basic_terms if term in response_lower]
            
            print(f"\n🎓 PhD-level terms found: {found_phd}")
            print(f"🔢 Numerical terms found: {found_numerical}")
            print(f"❌ Basic terms found: {found_basic}")
            
            is_phd = len(found_phd) > 0
            is_numerical = len(found_numerical) > 0
            is_basic = len(found_basic) > 0
            
            print(f"\n📊 ANALYSIS:")
            print(f"   PhD-level: {'YES' if is_phd else 'NO'}")
            print(f"   Numerical: {'YES' if is_numerical else 'NO'}")
            print(f"   Basic/undergraduate: {'YES' if is_basic else 'NO'}")
            
            if is_phd and is_numerical and not is_basic:
                print("\n🎉 SUCCESS: DeepSeek R1 generated PhD-level numerical content!")
                return True
            elif is_basic:
                print("\n❌ FAILED: DeepSeek R1 generated basic undergraduate content")
                return False
            else:
                print("\n⚠️ MIXED: DeepSeek R1 output needs analysis")
                return False
                
        else:
            print(f"❌ DeepSeek R1 API error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek R1 test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_deepseek_direct()
    
    if success:
        print("\n✅ DeepSeek R1 is generating PhD-level content!")
        print("🔍 The issue must be in the UI integration or JSON formatting")
    else:
        print("\n❌ DeepSeek R1 is not generating PhD-level content")
        print("🔧 Need to improve the prompting to DeepSeek R1")
