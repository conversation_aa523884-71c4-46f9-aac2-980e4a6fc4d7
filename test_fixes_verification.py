#!/usr/bin/env python3
"""
🚀 Test script to verify the three critical fixes:
1. Token streaming is enabled by default
2. Questions are saved to database for review history
3. Numerical topics like "atoms" are correctly detected as numerical
"""

import sys
import os
import sqlite3
import json
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from knowledge_app.core.topic_analyzer import TopicAnalyzer
from knowledge_app.core.question_history_storage import QuestionHistoryStorage

def test_token_streaming_default():
    """Test 1: Verify token streaming checkbox is enabled by default"""
    print("🌊 TEST 1: Token Streaming Default State")
    
    html_file = Path("src/knowledge_app/web/app.html")
    if not html_file.exists():
        print("❌ HTML file not found")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if the checkbox has 'checked' attribute
    if 'id="token-streaming-enabled"' in content and 'checked>' in content:
        print("✅ Token streaming checkbox is enabled by default")
        return True
    else:
        print("❌ Token streaming checkbox is NOT enabled by default")
        return False

def test_numerical_topic_detection():
    """Test 2: Verify numerical topics are correctly detected"""
    print("\n🔢 TEST 2: Numerical Topic Detection")
    
    try:
        analyzer = TopicAnalyzer()
        
        # Test clearly numerical topics
        test_topics = [
            "atoms",
            "atomic structure", 
            "physics",
            "chemistry",
            "mathematics",
            "molecular biology"
        ]
        
        all_passed = True
        for topic in test_topics:
            profile = analyzer.get_topic_profile(topic)
            detected_type = profile.get('detected_type', 'unknown')
            is_numerical_possible = profile.get('is_numerical_possible', False)
            
            print(f"  Topic: '{topic}' → {detected_type} (numerical_possible: {is_numerical_possible})")
            
            # For clearly numerical topics, we expect either:
            # 1. detected_type = "numerical", OR
            # 2. is_numerical_possible = True
            if detected_type == 'numerical' or is_numerical_possible:
                print(f"    ✅ Correctly identified as numerical-capable")
            else:
                print(f"    ❌ Failed to identify as numerical-capable")
                all_passed = False
        
        if all_passed:
            print("✅ All numerical topics correctly detected")
            return True
        else:
            print("❌ Some numerical topics failed detection")
            return False
            
    except Exception as e:
        print(f"❌ Topic analysis test failed: {e}")
        return False

def test_database_storage():
    """Test 3: Verify database storage is working"""
    print("\n💾 TEST 3: Database Storage")
    
    try:
        # Check if database file exists
        db_path = Path("user_data/question_history.sqlite")
        if not db_path.exists():
            print("❌ Question history database does not exist")
            return False
        
        # Test database connection and schema
        storage = QuestionHistoryStorage()
        
        # Test saving a question
        test_question = {
            "question": "What is the atomic number of hydrogen?",
            "options": ["1", "2", "3", "4"],
            "correct_answer": "1",
            "explanation": "Hydrogen has one proton, so its atomic number is 1."
        }
        
        test_quiz_params = {
            "topic": "atoms",
            "difficulty": "medium",
            "question_type": "numerical",
            "game_mode": "casual"
        }
        
        question_id = storage.save_question(test_question, test_quiz_params)
        
        if question_id:
            print(f"✅ Successfully saved test question to database: {question_id}")
            
            # Verify it can be retrieved
            questions = storage.get_questions_by_topic("atoms", limit=1)
            if questions and len(questions) > 0:
                print("✅ Successfully retrieved question from database")
                return True
            else:
                print("❌ Failed to retrieve question from database")
                return False
        else:
            print("❌ Failed to save question to database")
            return False
            
    except Exception as e:
        print(f"❌ Database storage test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 KNOWLEDGE APP FIXES VERIFICATION")
    print("=" * 50)
    
    results = []
    
    # Test 1: Token streaming default
    results.append(test_token_streaming_default())
    
    # Test 2: Numerical topic detection
    results.append(test_numerical_topic_detection())
    
    # Test 3: Database storage
    results.append(test_database_storage())
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    
    passed = sum(results)
    total = len(results)
    
    test_names = [
        "Token Streaming Default",
        "Numerical Topic Detection", 
        "Database Storage"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        return True
    else:
        print("⚠️ Some fixes need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
