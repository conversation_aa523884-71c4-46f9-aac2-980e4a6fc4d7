#!/usr/bin/env python3
"""
Fast Batch Test Runner - Run 20 tests at a time
"""

import asyncio
import sys
import time
from datetime import datetime

async def run_single_batch(batch_number=1):
    """Run a single batch of 20 tests"""
    print(f"🚀 RUNNING BATCH {batch_number} - 20 GLOBAL KNOWLEDGE TESTS")
    print("=" * 60)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Initialize system
        print("🔧 Initializing system...")
        from src.knowledge_app.core.unified_inference_manager import initialize_unified_inference
        
        success = initialize_unified_inference({
            'timeout': 30.0,
            'mode': 'auto',
            'prefer_local': True
        })
        
        if not success:
            print("❌ System initialization failed")
            return False
        
        print("✅ System initialized successfully")
        
        # Import the batch test function
        from global_knowledge_test_suite import run_batch_test, generate_massive_test_suite
        import random
        
        # Generate test cases and select batch
        all_tests = generate_massive_test_suite()
        
        # Select 20 diverse tests for this batch
        batch_tests = random.sample(all_tests, 20)
        
        # Run the batch
        passed, total, category_stats, duration = await run_batch_test(batch_number, batch_tests)
        
        # Summary
        pass_rate = (passed / total) * 100
        print(f"\n🏆 BATCH {batch_number} FINAL RESULTS:")
        print(f"   ✅ Passed: {passed}/{total} ({pass_rate:.1f}%)")
        print(f"   ⏱️ Duration: {duration:.1f} seconds")
        print(f"   🚀 Speed: {total/duration:.2f} tests/second")
        
        if pass_rate >= 80:
            print(f"   🎉 EXCELLENT performance!")
        elif pass_rate >= 70:
            print(f"   👏 GOOD performance!")
        else:
            print(f"   📊 Room for improvement")
        
        return pass_rate >= 70
        
    except Exception as e:
        print(f"❌ Batch test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main batch runner"""
    if len(sys.argv) > 1:
        try:
            batch_number = int(sys.argv[1])
        except ValueError:
            batch_number = 1
    else:
        batch_number = 1
    
    print(f"🌍 FAST BATCH TEST RUNNER")
    print(f"Running Batch {batch_number} of Global Knowledge Tests")
    
    success = await run_single_batch(batch_number)
    
    if success:
        print(f"\n✅ Batch {batch_number} completed successfully!")
        print(f"💡 To run next batch: python run_batch_tests.py {batch_number + 1}")
    else:
        print(f"\n❌ Batch {batch_number} had issues")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
