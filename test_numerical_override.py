#!/usr/bin/env python3
"""
Test the numerical override directly with Ollama
"""

import requests
import json

def test_numerical_override():
    """Test the numerical override prompt directly"""
    
    prompt = """🔢 CRITICAL: Generate a CALCULATION-BASED question about atoms that requires mathematical problem-solving.

MANDATORY REQUIREMENTS:
- Question MUST involve calculations with specific numbers
- Include numerical values (energies, wavelengths, masses, etc.)
- All answer options MUST be numerical values with units
- Use formulas like E = hf, E = -13.6/n², λ = hc/E, etc.

EXAMPLES OF REQUIRED QUESTIONS:
- "Calculate the energy of an electron in the n=3 shell of hydrogen (ground state = -13.6 eV)"
- "What is the wavelength of light emitted when an electron transitions from n=4 to n=2 in hydrogen?"
- "Calculate the binding energy of the outermost electron in sodium (ionization energy = 5.14 eV)"

FORBIDDEN: Electron configuration questions, conceptual questions, theory explanations

Generate a numerical calculation question about atoms at expert level.

Respond with ONLY valid JSON in this exact format:
{
  "question": "Calculate the [specific calculation problem]?",
  "options": ["numerical answer with units", "numerical answer with units", "numerical answer with units", "numerical answer with units"],
  "correct_answer": "A",
  "explanation": "Step-by-step calculation"
}

CRITICAL: You MUST respond with ONLY valid JSON. No explanations, no text before or after the JSON.

JSON Format:
{
  "question": "string",
  "options": ["string", "string", "string", "string"],
  "correct_answer": "string (must be 'A', 'B', 'C', or 'D')",
  "explanation": "string"
}"""

    try:
        print("🔢 Testing numerical override with Ollama...")
        
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "llama3.1:8b",
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9
                }
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            generated_text = result.get('response', '')
            
            print(f"✅ Generated response ({len(generated_text)} chars):")
            print(f"📝 Response: {generated_text}")
            
            # Try to parse as JSON
            try:
                # Extract JSON from response
                import re
                json_match = re.search(r'\{.*\}', generated_text, re.DOTALL)
                if json_match:
                    json_text = json_match.group()
                    question_data = json.loads(json_text)
                    
                    print(f"\n✅ Successfully parsed JSON:")
                    print(f"   Question: {question_data.get('question', 'N/A')}")
                    print(f"   Options: {question_data.get('options', 'N/A')}")
                    print(f"   Correct: {question_data.get('correct_answer', 'N/A')}")
                    
                    # Check if it's actually numerical
                    question = question_data.get('question', '')
                    options = question_data.get('options', [])
                    
                    import re
                    has_numbers = bool(re.search(r'\d+', question)) or any(
                        re.search(r'\d+', str(opt)) for opt in options
                    )
                    
                    calc_keywords = ['calculate', 'compute', 'energy', 'wavelength', 'binding']
                    has_calc_keywords = any(kw in question.lower() for kw in calc_keywords)
                    
                    if has_numbers and has_calc_keywords:
                        print("🎉 SUCCESS: This is a numerical calculation question!")
                        return True
                    else:
                        print("❌ FAILED: Still not numerical despite override")
                        return False
                else:
                    print("❌ No JSON found in response")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                return False
        else:
            print(f"❌ Ollama request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_numerical_override()
    if success:
        print("\n🎉 Numerical override works! The issue is elsewhere.")
    else:
        print("\n💥 Numerical override failed. Need to fix the prompt.")
