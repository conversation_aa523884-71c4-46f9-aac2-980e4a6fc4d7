#!/usr/bin/env python3
"""
🚀 Expert Mode GPU Optimization Script
Forces maximum system utilization for expert mode
"""

import sys
import os
import json
import requests
import time
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def optimize_ollama_for_gpu():
    """Optimize Ollama configuration for maximum GPU utilization"""
    print("🚀 OPTIMIZING OLLAMA FOR MAXIMUM GPU UTILIZATION")
    print("=" * 60)
    
    try:
        # Check current models
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code != 200:
            print("❌ Ollama not running. Please start Ollama first.")
            return False
            
        models = response.json().get('models', [])
        print(f"Found {len(models)} models")
        
        # Find DeepSeek models
        deepseek_models = [m for m in models if 'deepseek' in m['name'].lower()]
        llama_models = [m for m in models if 'llama' in m['name'].lower()]
        
        if not deepseek_models:
            print("❌ No DeepSeek models found. Please install DeepSeek models first.")
            return False
            
        print(f"Found DeepSeek models: {[m['name'] for m in deepseek_models]}")
        print(f"Found Llama models: {[m['name'] for m in llama_models]}")
        
        # Configure each model for maximum GPU usage
        for model in deepseek_models + llama_models:
            model_name = model['name']
            print(f"\n🔧 Optimizing {model_name}...")
            
            # Create optimized modelfile
            modelfile_content = f"""FROM {model_name}

# GPU Optimization Parameters
PARAMETER num_gpu 50
PARAMETER num_thread 16
PARAMETER num_ctx 8192
PARAMETER num_batch 1024
PARAMETER num_parallel 4
PARAMETER use_mmap true
PARAMETER use_mlock true
PARAMETER low_vram false
PARAMETER numa false
PARAMETER rope_freq_base 10000.0
PARAMETER rope_freq_scale 1.0
PARAMETER mul_mat_q true
PARAMETER f16_kv true
PARAMETER logits_all false
PARAMETER vocab_only false
PARAMETER embedding false

# Performance optimization
PARAMETER temperature 0.7
PARAMETER top_k 40
PARAMETER top_p 0.9
PARAMETER repeat_last_n 64
PARAMETER repeat_penalty 1.1
PARAMETER seed -1
PARAMETER tfs_z 1.0
PARAMETER typical_p 1.0
PARAMETER mirostat 0
PARAMETER mirostat_eta 0.1
PARAMETER mirostat_tau 5.0
"""
            
            # Apply the optimized configuration
            try:
                create_response = requests.post(
                    "http://localhost:11434/api/create",
                    json={
                        "name": f"{model_name}-optimized",
                        "modelfile": modelfile_content
                    },
                    timeout=300
                )
                
                if create_response.status_code == 200:
                    print(f"✅ Created optimized version: {model_name}-optimized")
                else:
                    print(f"⚠️ Failed to optimize {model_name}: {create_response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error optimizing {model_name}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error optimizing Ollama: {e}")
        return False

def update_deepseek_config():
    """Update DeepSeek configuration for optimized models"""
    print("\n🧠 UPDATING DEEPSEEK CONFIGURATION")
    print("=" * 50)
    
    try:
        from knowledge_app.core.deepseek_integration import DeepSeekConfig
        
        # Create optimized config
        config = DeepSeekConfig()
        
        # Use optimized models
        config.thinking_model = "deepseek-r1:14b-optimized"
        config.json_model = "llama3.1:8b-optimized"
        
        # Optimize parameters
        config.max_thinking_tokens = 4000
        config.max_json_tokens = 1000
        config.temperature_thinking = 0.9
        config.temperature_json = 0.3
        config.timeout = 300
        
        print(f"✅ Updated DeepSeek config:")
        print(f"   Thinking model: {config.thinking_model}")
        print(f"   JSON model: {config.json_model}")
        print(f"   Max thinking tokens: {config.max_thinking_tokens}")
        print(f"   Timeout: {config.timeout}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating DeepSeek config: {e}")
        return False

def test_optimized_expert_mode():
    """Test the optimized expert mode"""
    print("\n🧪 TESTING OPTIMIZED EXPERT MODE")
    print("=" * 50)
    
    try:
        from knowledge_app.core.deepseek_integration import get_deepseek_pipeline
        
        # Get optimized pipeline
        pipeline = get_deepseek_pipeline()
        
        if not pipeline.is_ready():
            print("❌ DeepSeek pipeline not ready")
            return False
            
        print("🚀 Starting optimized expert question generation...")
        start_time = time.time()
        
        result = pipeline.generate_expert_question(
            topic="quantum mechanics",
            difficulty="expert"
        )
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        if result:
            print(f"✅ Expert question generated in {generation_time:.2f} seconds")
            print(f"   Question: {result.get('question', 'N/A')[:100]}...")
            
            # Check if it's faster than before
            if generation_time < 60:
                print("🎉 SIGNIFICANT SPEED IMPROVEMENT!")
            elif generation_time < 80:
                print("✅ Good speed improvement")
            else:
                print("⚠️ Still slow - may need further optimization")
                
            return True
        else:
            print("❌ Expert question generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing optimized expert mode: {e}")
        import traceback
        traceback.print_exc()
        return False

def force_gpu_acceleration():
    """Force GPU acceleration for all models"""
    print("\n💪 FORCING GPU ACCELERATION")
    print("=" * 50)
    
    try:
        # Set environment variables for GPU acceleration
        os.environ['CUDA_VISIBLE_DEVICES'] = '0'
        os.environ['OLLAMA_NUM_GPU'] = '50'
        os.environ['OLLAMA_GPU_LAYERS'] = '50'
        os.environ['OLLAMA_FLASH_ATTENTION'] = '1'
        os.environ['OLLAMA_NUM_THREAD'] = '16'
        os.environ['OLLAMA_NUM_PARALLEL'] = '4'
        
        print("✅ Set GPU acceleration environment variables:")
        print(f"   CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES')}")
        print(f"   OLLAMA_NUM_GPU: {os.environ.get('OLLAMA_NUM_GPU')}")
        print(f"   OLLAMA_GPU_LAYERS: {os.environ.get('OLLAMA_GPU_LAYERS')}")
        print(f"   OLLAMA_FLASH_ATTENTION: {os.environ.get('OLLAMA_FLASH_ATTENTION')}")
        print(f"   OLLAMA_NUM_THREAD: {os.environ.get('OLLAMA_NUM_THREAD')}")
        print(f"   OLLAMA_NUM_PARALLEL: {os.environ.get('OLLAMA_NUM_PARALLEL')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting GPU acceleration: {e}")
        return False

def main():
    """Main optimization function"""
    print("🔥 EXPERT MODE GPU OPTIMIZATION")
    print("=" * 60)
    
    success_count = 0
    total_steps = 4
    
    # Step 1: Force GPU acceleration
    if force_gpu_acceleration():
        success_count += 1
        print("✅ Step 1/4: GPU acceleration forced")
    else:
        print("❌ Step 1/4: Failed to force GPU acceleration")
    
    # Step 2: Optimize Ollama models
    if optimize_ollama_for_gpu():
        success_count += 1
        print("✅ Step 2/4: Ollama models optimized")
    else:
        print("❌ Step 2/4: Failed to optimize Ollama models")
    
    # Step 3: Update DeepSeek config
    if update_deepseek_config():
        success_count += 1
        print("✅ Step 3/4: DeepSeek config updated")
    else:
        print("❌ Step 3/4: Failed to update DeepSeek config")
    
    # Step 4: Test optimized expert mode
    if test_optimized_expert_mode():
        success_count += 1
        print("✅ Step 4/4: Optimized expert mode tested")
    else:
        print("❌ Step 4/4: Failed to test optimized expert mode")
    
    print(f"\n🎯 OPTIMIZATION COMPLETE: {success_count}/{total_steps} steps successful")
    
    if success_count == total_steps:
        print("🎉 EXPERT MODE FULLY OPTIMIZED!")
        print("   Your system should now utilize GPU and CPU much more effectively")
    elif success_count >= 2:
        print("✅ PARTIAL OPTIMIZATION SUCCESSFUL")
        print("   Some improvements applied, expert mode should be faster")
    else:
        print("❌ OPTIMIZATION FAILED")
        print("   Expert mode may still have performance issues")

if __name__ == "__main__":
    main()
