#!/usr/bin/env python3
"""
Quick debug test to see generation logs
"""

import sys
import os
import time
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from knowledge_app.webengine_app import KnowledgeAppWebEngine

def main():
    print("🔥 QUICK DEBUG TEST - Starting app to see generation logs")
    
    app = QApplication([])
    
    # Create the app
    knowledge_app = KnowledgeAppWebEngine()
    knowledge_app.show()
    
    # Wait a bit for initialization
    time.sleep(2)
    
    # Simulate starting a quiz
    print("🚀 Simulating quiz start...")
    quiz_params = {
        "topic": "atoms",
        "mode": "online", 
        "game_mode": "casual",
        "submode": "numerical",
        "difficulty": "expert",
        "num_questions": 2
    }
    
    import json
    knowledge_app.bridge.startQuiz(json.dumps(quiz_params))
    
    # Wait to see logs
    print("⏳ Waiting 30 seconds to see generation logs...")
    time.sleep(30)
    
    print("✅ Debug test complete")
    app.quit()

if __name__ == "__main__":
    main()
