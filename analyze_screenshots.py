#!/usr/bin/env python3
"""
🔍 AUTOMATIC SCREENSHOT ANALYSIS
Analyzes the test screenshots to detect specific issues:
1. Token streaming not working
2. Question not adhering to numerical type
3. Expert difficulty not being respected
4. Same questions repeating
"""

import os
import sys
from pathlib import Path
import json
from datetime import datetime

# Add OCR capability for text extraction from screenshots
try:
    import pytesseract
    from PIL import Image
    import cv2
    import numpy as np
except ImportError:
    print("Installing required packages for screenshot analysis...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pytesseract", "pillow", "opencv-python"])
    import pytesseract
    from PIL import Image
    import cv2
    import numpy as np

class ScreenshotAnalyzer:
    """Analyzes screenshots to detect UI issues automatically"""
    
    def __init__(self, screenshots_dir="test_screenshots"):
        self.screenshots_dir = Path(screenshots_dir)
        self.analysis_results = {
            "timestamp": datetime.now().isoformat(),
            "issues_found": [],
            "token_streaming": {"enabled": False, "working": False, "evidence": []},
            "question_type": {"requested": "numerical", "actual": "unknown", "evidence": []},
            "difficulty": {"requested": "expert", "actual": "unknown", "evidence": []},
            "question_content": {"questions": [], "duplicates": False},
            "ui_state": {"screenshots_analyzed": 0, "errors": []}
        }
    
    def extract_text_from_image(self, image_path):
        """Extract text from screenshot using OCR"""
        try:
            # Load image
            image = cv2.imread(str(image_path))
            if image is None:
                return ""
            
            # Convert to RGB
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Extract text using pytesseract
            text = pytesseract.image_to_string(image_rgb)
            return text.strip()
        except Exception as e:
            print(f"⚠️ OCR failed for {image_path}: {e}")
            return ""
    
    def analyze_token_streaming(self, screenshot_files):
        """Analyze if token streaming is working"""
        print("🌊 Analyzing token streaming...")
        
        # Check if token streaming was enabled in settings
        settings_screenshots = [f for f in screenshot_files if "token_streaming_enabled" in f.name]
        if settings_screenshots:
            text = self.extract_text_from_image(settings_screenshots[0])
            if "Live Token Streaming" in text or "token streaming" in text.lower():
                self.analysis_results["token_streaming"]["enabled"] = True
                self.analysis_results["token_streaming"]["evidence"].append("Token streaming checkbox found in settings")
        
        # Check generation progress screenshots for streaming indicators
        progress_screenshots = [f for f in screenshot_files if "generation_progress" in f.name]
        streaming_indicators = [
            "Generating", "tokens", "streaming", "thinking", "processing",
            "...", "Loading", "AI is", "model is", "generating"
        ]
        
        streaming_evidence = []
        for screenshot in progress_screenshots[:5]:  # Check first 5 progress screenshots
            text = self.extract_text_from_image(screenshot)
            for indicator in streaming_indicators:
                if indicator.lower() in text.lower():
                    streaming_evidence.append(f"Found '{indicator}' in {screenshot.name}")
        
        if streaming_evidence:
            self.analysis_results["token_streaming"]["working"] = True
            self.analysis_results["token_streaming"]["evidence"].extend(streaming_evidence)
        else:
            self.analysis_results["issues_found"].append("❌ Token streaming not visible during generation")
    
    def analyze_question_type(self, screenshot_files):
        """Analyze if numerical question type was respected"""
        print("🔢 Analyzing question type adherence...")
        
        # Check settings screenshot
        settings_screenshots = [f for f in screenshot_files if "question_type_set" in f.name]
        if settings_screenshots:
            text = self.extract_text_from_image(settings_screenshots[0])
            if "numerical" in text.lower():
                self.analysis_results["question_type"]["evidence"].append("Numerical type selected in settings")
        
        # Analyze final question content
        final_screenshots = [f for f in screenshot_files if "final" in f.name]
        numerical_indicators = [
            "calculate", "equation", "formula", "number", "value", "solve",
            "=", "+", "-", "×", "÷", "²", "³", "mol", "kg", "m/s", "%"
        ]
        
        question_analysis = []
        for screenshot in final_screenshots:
            text = self.extract_text_from_image(screenshot)
            
            # Look for actual question content
            if "?" in text or "What" in text or "How" in text or "Calculate" in text:
                question_found = True
                numerical_count = sum(1 for indicator in numerical_indicators if indicator in text)
                
                if numerical_count >= 2:
                    self.analysis_results["question_type"]["actual"] = "numerical"
                    question_analysis.append(f"Numerical question detected in {screenshot.name}")
                else:
                    self.analysis_results["question_type"]["actual"] = "conceptual"
                    question_analysis.append(f"Conceptual question detected in {screenshot.name}")
                
                # Store question content
                question_lines = [line.strip() for line in text.split('\n') if '?' in line]
                self.analysis_results["question_content"]["questions"].extend(question_lines)
        
        if question_analysis:
            self.analysis_results["question_type"]["evidence"].extend(question_analysis)
        else:
            self.analysis_results["issues_found"].append("❌ No clear question content detected in final screenshots")
        
        # Check if requested type matches actual type
        if (self.analysis_results["question_type"]["requested"] != 
            self.analysis_results["question_type"]["actual"]):
            self.analysis_results["issues_found"].append(
                f"❌ Question type mismatch: requested {self.analysis_results['question_type']['requested']}, "
                f"got {self.analysis_results['question_type']['actual']}"
            )
    
    def analyze_difficulty_level(self, screenshot_files):
        """Analyze if expert difficulty was respected"""
        print("🎓 Analyzing difficulty level...")
        
        # Check settings screenshot
        settings_screenshots = [f for f in screenshot_files if "difficulty_set" in f.name]
        if settings_screenshots:
            text = self.extract_text_from_image(settings_screenshots[0])
            if "expert" in text.lower():
                self.analysis_results["difficulty"]["evidence"].append("Expert difficulty selected in settings")
        
        # Analyze question complexity in final screenshots
        final_screenshots = [f for f in screenshot_files if "final" in f.name]
        expert_indicators = [
            "PhD", "advanced", "complex", "sophisticated", "theoretical",
            "quantum", "molecular", "thermodynamic", "kinetic", "equilibrium",
            "derivative", "integral", "matrix", "tensor", "statistical"
        ]
        
        difficulty_analysis = []
        for screenshot in final_screenshots:
            text = self.extract_text_from_image(screenshot)
            expert_count = sum(1 for indicator in expert_indicators if indicator.lower() in text.lower())
            
            if expert_count >= 1:
                self.analysis_results["difficulty"]["actual"] = "expert"
                difficulty_analysis.append(f"Expert-level content detected in {screenshot.name}")
            elif len(text) > 200:  # Long questions might indicate higher difficulty
                self.analysis_results["difficulty"]["actual"] = "medium-hard"
                difficulty_analysis.append(f"Medium-hard difficulty inferred from question length in {screenshot.name}")
            else:
                self.analysis_results["difficulty"]["actual"] = "easy-medium"
                difficulty_analysis.append(f"Easy-medium difficulty inferred in {screenshot.name}")
        
        if difficulty_analysis:
            self.analysis_results["difficulty"]["evidence"].extend(difficulty_analysis)
        
        # Check if requested difficulty matches actual
        if (self.analysis_results["difficulty"]["requested"] != 
            self.analysis_results["difficulty"]["actual"]):
            self.analysis_results["issues_found"].append(
                f"❌ Difficulty mismatch: requested {self.analysis_results['difficulty']['requested']}, "
                f"got {self.analysis_results['difficulty']['actual']}"
            )
    
    def check_duplicate_questions(self):
        """Check for duplicate questions"""
        print("🔄 Checking for duplicate questions...")
        
        questions = self.analysis_results["question_content"]["questions"]
        if len(questions) > 1:
            unique_questions = set(questions)
            if len(unique_questions) < len(questions):
                self.analysis_results["question_content"]["duplicates"] = True
                self.analysis_results["issues_found"].append("❌ Duplicate questions detected")
    
    def analyze_all_screenshots(self):
        """Run complete analysis on all screenshots"""
        print("🔍 STARTING AUTOMATIC SCREENSHOT ANALYSIS")
        print("=" * 60)
        
        if not self.screenshots_dir.exists():
            print(f"❌ Screenshots directory not found: {self.screenshots_dir}")
            return
        
        # Get all screenshot files
        screenshot_files = list(self.screenshots_dir.glob("*.png"))
        screenshot_files.sort()
        
        if not screenshot_files:
            print(f"❌ No screenshots found in {self.screenshots_dir}")
            return
        
        print(f"📸 Found {len(screenshot_files)} screenshots to analyze")
        self.analysis_results["ui_state"]["screenshots_analyzed"] = len(screenshot_files)
        
        # Run all analyses
        self.analyze_token_streaming(screenshot_files)
        self.analyze_question_type(screenshot_files)
        self.analyze_difficulty_level(screenshot_files)
        self.check_duplicate_questions()
        
        # Generate report
        self.generate_report()
    
    def generate_report(self):
        """Generate analysis report"""
        print("\n" + "=" * 60)
        print("📊 AUTOMATIC ANALYSIS RESULTS")
        print("=" * 60)
        
        # Summary
        total_issues = len(self.analysis_results["issues_found"])
        if total_issues == 0:
            print("✅ No issues detected - everything working correctly!")
        else:
            print(f"❌ {total_issues} issues detected:")
            for issue in self.analysis_results["issues_found"]:
                print(f"  {issue}")
        
        print(f"\n🌊 TOKEN STREAMING:")
        print(f"  Enabled: {self.analysis_results['token_streaming']['enabled']}")
        print(f"  Working: {self.analysis_results['token_streaming']['working']}")
        
        print(f"\n🔢 QUESTION TYPE:")
        print(f"  Requested: {self.analysis_results['question_type']['requested']}")
        print(f"  Actual: {self.analysis_results['question_type']['actual']}")
        
        print(f"\n🎓 DIFFICULTY:")
        print(f"  Requested: {self.analysis_results['difficulty']['requested']}")
        print(f"  Actual: {self.analysis_results['difficulty']['actual']}")
        
        print(f"\n📝 QUESTIONS:")
        questions = self.analysis_results['question_content']['questions']
        if questions:
            print(f"  Found {len(questions)} questions:")
            for i, q in enumerate(questions[:3], 1):  # Show first 3
                print(f"    {i}. {q[:100]}...")
        else:
            print("  No questions detected")
        
        print(f"  Duplicates: {self.analysis_results['question_content']['duplicates']}")
        
        # Save detailed results
        results_file = self.screenshots_dir / "analysis_results.json"
        with open(results_file, 'w') as f:
            json.dump(self.analysis_results, f, indent=2)
        print(f"\n💾 Detailed results saved to: {results_file}")

def main():
    """Main analysis function"""
    print("🔍 AUTOMATIC SCREENSHOT ANALYSIS TOOL")
    print("=" * 60)
    print("This will analyze the test screenshots to detect:")
    print("1. 🌊 Token streaming issues")
    print("2. 🔢 Question type adherence (numerical vs conceptual)")
    print("3. 🎓 Difficulty level compliance (expert)")
    print("4. 🔄 Duplicate questions")
    print("=" * 60)
    
    analyzer = ScreenshotAnalyzer()
    analyzer.analyze_all_screenshots()

if __name__ == "__main__":
    main()
