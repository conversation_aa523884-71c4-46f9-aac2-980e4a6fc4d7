#!/usr/bin/env python3
"""
🧠 Test Intelligent Semantic Mapping
Verifies that the Phi model can properly map abbreviations like CBT to conceptual
"""

import sys
import os
import time
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_cbt_mapping():
    """Test that CBT maps to conceptual correctly"""
    print("🧠 TESTING CBT SEMANTIC MAPPING")
    print("=" * 50)
    
    try:
        from knowledge_app.core.intelligent_semantic_mapper import get_semantic_mapper
        
        mapper = get_semantic_mapper()
        
        # Test CBT specifically
        print("🧪 Testing 'CBT' mapping...")
        result = mapper.map_topic_semantically("CBT")
        
        print(f"✅ Results for 'CBT':")
        print(f"   Original: {result.original_input}")
        print(f"   Expanded: {result.expanded_topic}")
        print(f"   Type: {result.question_type}")
        print(f"   Confidence: {result.confidence:.2f}")
        print(f"   Reasoning: {result.reasoning}")
        print(f"   Is abbreviation: {result.is_abbreviation}")
        print(f"   Full form: {result.full_form}")
        
        # Check if it correctly identified CBT as conceptual
        if result.question_type.lower() == "conceptual":
            print("🎉 SUCCESS: CBT correctly mapped to CONCEPTUAL!")
            return True
        else:
            print(f"❌ FAILED: CBT mapped to {result.question_type}, expected CONCEPTUAL")
            return False
            
    except Exception as e:
        print(f"❌ Error testing CBT mapping: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_various_abbreviations():
    """Test various abbreviations and topics"""
    print("\n🔤 TESTING VARIOUS ABBREVIATIONS")
    print("=" * 50)
    
    test_cases = [
        ("AI", "mixed"),  # Artificial Intelligence - can be both
        ("ML", "mixed"),  # Machine Learning - can be both
        ("DNA", "conceptual"),  # Biology concept
        ("GDP", "numerical"),  # Economic calculation
        ("HTTP", "conceptual"),  # Computer science concept
        ("SQL", "mixed"),  # Can be both conceptual and practical
        ("calculus", "numerical"),  # Math
        ("psychology", "conceptual"),  # Social science
        ("physics", "mixed"),  # Can be both
    ]
    
    try:
        from knowledge_app.core.intelligent_semantic_mapper import get_semantic_mapper
        
        mapper = get_semantic_mapper()
        success_count = 0
        
        for input_text, expected_type in test_cases:
            print(f"\n🧪 Testing '{input_text}'...")
            
            start_time = time.time()
            result = mapper.map_topic_semantically(input_text)
            end_time = time.time()
            
            print(f"   Expanded: {result.expanded_topic}")
            print(f"   Type: {result.question_type} (expected: {expected_type})")
            print(f"   Confidence: {result.confidence:.2f}")
            print(f"   Time: {end_time - start_time:.2f}s")
            
            # Check if result is reasonable (not strict matching since AI can be subjective)
            if result.question_type.lower() == expected_type.lower():
                print("   ✅ EXACT MATCH")
                success_count += 1
            elif result.question_type.lower() == "mixed":
                print("   ✅ MIXED (acceptable for most topics)")
                success_count += 1
            else:
                print(f"   ⚠️ DIFFERENT: got {result.question_type}, expected {expected_type}")
        
        print(f"\n📊 Results: {success_count}/{len(test_cases)} successful mappings")
        return success_count >= len(test_cases) * 0.7  # 70% success rate is good
        
    except Exception as e:
        print(f"❌ Error testing abbreviations: {e}")
        return False

def test_integration_with_topic_analyzer():
    """Test integration with the existing TopicAnalyzer"""
    print("\n🔗 TESTING TOPIC ANALYZER INTEGRATION")
    print("=" * 50)
    
    try:
        from knowledge_app.core.topic_analyzer import TopicAnalyzer
        
        # Create analyzer with semantic mapping enabled
        analyzer = TopicAnalyzer(use_semantic_mapping=True)
        
        # Test CBT with the full system
        print("🧪 Testing CBT with TopicAnalyzer...")
        profile = analyzer.get_topic_profile("CBT")
        
        print(f"✅ TopicAnalyzer results for 'CBT':")
        print(f"   Detected type: {profile.get('detected_type')}")
        print(f"   Confidence: {profile.get('confidence')}")
        print(f"   Conceptual possible: {profile.get('is_conceptual_possible')}")
        print(f"   Numerical possible: {profile.get('is_numerical_possible')}")
        print(f"   Expanded topic: {profile.get('expanded_topic', 'N/A')}")
        print(f"   Is abbreviation: {profile.get('is_abbreviation', 'N/A')}")
        print(f"   Full form: {profile.get('full_form', 'N/A')}")
        
        # Check UI recommendations
        ui_recs = profile.get('ui_recommendations', {})
        print(f"   UI recommendations:")
        print(f"     - Highlight conceptual: {ui_recs.get('highlight_conceptual')}")
        print(f"     - Highlight numerical: {ui_recs.get('highlight_numerical')}")
        print(f"     - Disable numerical: {ui_recs.get('disable_numerical')}")
        print(f"     - Show expansion: {ui_recs.get('show_expansion')}")
        
        # Check if CBT is properly handled
        if profile.get('detected_type') == 'conceptual' and profile.get('is_abbreviation'):
            print("🎉 SUCCESS: CBT properly handled by TopicAnalyzer!")
            return True
        else:
            print("⚠️ CBT handling could be improved")
            return False
            
    except Exception as e:
        print(f"❌ Error testing integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """Test performance of semantic mapping"""
    print("\n⚡ TESTING PERFORMANCE")
    print("=" * 50)
    
    try:
        from knowledge_app.core.intelligent_semantic_mapper import get_semantic_mapper
        
        mapper = get_semantic_mapper()
        
        # Test multiple calls to check caching
        test_inputs = ["CBT", "AI", "physics", "CBT", "AI"]  # Repeat some for cache test
        
        total_time = 0
        cache_hits = 0
        
        for i, input_text in enumerate(test_inputs):
            print(f"🧪 Call {i+1}: '{input_text}'")
            
            start_time = time.time()
            result = mapper.map_topic_semantically(input_text)
            end_time = time.time()
            
            call_time = end_time - start_time
            total_time += call_time
            
            print(f"   Time: {call_time:.2f}s")
            print(f"   Result: {result.question_type}")
            
            # Check if this was likely a cache hit (very fast)
            if call_time < 0.1 and i > 0:  # First call can't be cache hit
                cache_hits += 1
                print("   💨 Likely cache hit!")
        
        avg_time = total_time / len(test_inputs)
        print(f"\n📊 Performance Summary:")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Average time: {avg_time:.2f}s")
        print(f"   Cache hits: {cache_hits}")
        
        # Good performance is under 2 seconds average
        return avg_time < 2.0
        
    except Exception as e:
        print(f"❌ Error testing performance: {e}")
        return False

def main():
    """Main test function"""
    print("🧠 INTELLIGENT SEMANTIC MAPPING TEST")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # Test 1: CBT mapping
    if test_cbt_mapping():
        success_count += 1
        print("✅ Test 1/4: CBT mapping successful")
    else:
        print("❌ Test 1/4: CBT mapping failed")
    
    # Test 2: Various abbreviations
    if test_various_abbreviations():
        success_count += 1
        print("✅ Test 2/4: Various abbreviations successful")
    else:
        print("❌ Test 2/4: Various abbreviations failed")
    
    # Test 3: Integration
    if test_integration_with_topic_analyzer():
        success_count += 1
        print("✅ Test 3/4: TopicAnalyzer integration successful")
    else:
        print("❌ Test 3/4: TopicAnalyzer integration failed")
    
    # Test 4: Performance
    if test_performance():
        success_count += 1
        print("✅ Test 4/4: Performance acceptable")
    else:
        print("❌ Test 4/4: Performance issues")
    
    print(f"\n🎯 SEMANTIC MAPPING TEST RESULTS: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 SEMANTIC MAPPING WORKING PERFECTLY!")
        print("   CBT and other abbreviations should now map correctly")
    elif success_count >= 3:
        print("✅ MOSTLY WORKING")
        print("   Semantic mapping is functional with minor issues")
    else:
        print("❌ MAJOR ISSUES")
        print("   Semantic mapping needs debugging")
    
    print("\n💡 NEXT STEPS:")
    print("   1. Try typing 'CBT' in the app - it should suggest conceptual")
    print("   2. Test other abbreviations like 'AI', 'ML', 'DNA'")
    print("   3. Check that the UI shows expanded forms for abbreviations")

if __name__ == "__main__":
    main()
