#!/usr/bin/env python3
"""
🔍 AUTOMATED SCREENSHOT ANALYZER
Uses OCR to extract text from screenshots and analyze UI issues automatically.
"""

import os
import sys
from pathlib import Path
import json
from datetime import datetime

def install_required_packages():
    """Install required packages for OCR"""
    try:
        import pytesseract
        from PIL import Image
        print("✅ OCR packages already installed")
        return True
    except ImportError:
        print("📦 Installing OCR packages...")
        try:
            os.system("pip install pytesseract pillow")
            print("✅ OCR packages installed successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to install OCR packages: {e}")
            return False

def setup_tesseract():
    """Setup Tesseract OCR"""
    try:
        import pytesseract
        
        # Try to find Tesseract executable on Windows
        possible_paths = [
            r"C:\Program Files\Tesseract-OCR\tesseract.exe",
            r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
            r"C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME', '')),
            "tesseract"  # If in PATH
        ]
        
        for path in possible_paths:
            if os.path.exists(path) or path == "tesseract":
                try:
                    if path != "tesseract":
                        pytesseract.pytesseract.tesseract_cmd = path
                    
                    # Test if it works
                    from PIL import Image
                    import numpy as np
                    
                    # Create a simple test image
                    test_img = Image.new('RGB', (100, 30), color='white')
                    pytesseract.image_to_string(test_img)
                    
                    print(f"✅ Tesseract found and working: {path}")
                    return True
                except:
                    continue
        
        print("❌ Tesseract not found. Please install Tesseract OCR:")
        print("   Download from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("   Or install via: winget install UB-Mannheim.TesseractOCR")
        return False
        
    except ImportError:
        return False

def extract_text_from_image(image_path):
    """Extract text from image using OCR"""
    try:
        import pytesseract
        from PIL import Image
        
        # Open and process image
        image = Image.open(image_path)
        
        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Extract text with better configuration for UI text
        custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,!?:;()[]{}+-=×÷<>%$@#&*/_|\\~`"\'\ \t\n'
        
        text = pytesseract.image_to_string(image, config=custom_config)
        
        return text.strip()
        
    except Exception as e:
        print(f"❌ Error extracting text from {image_path}: {e}")
        return ""

def analyze_screenshot_text(filename, text):
    """Analyze extracted text for specific issues"""
    analysis = {
        'filename': filename,
        'text_length': len(text),
        'issues': [],
        'findings': [],
        'text_preview': text[:200] + '...' if len(text) > 200 else text
    }
    
    # Convert to lowercase for analysis
    text_lower = text.lower()
    
    # Check for key UI elements
    if 'generation in progress' in filename.lower():
        analysis['type'] = 'GENERATION_IN_PROGRESS'
        
        # Check for token streaming indicators
        if any(word in text_lower for word in ['token', 'streaming', 'generating', 'thinking']):
            analysis['findings'].append('✅ Token streaming UI text detected')
        else:
            analysis['issues'].append('❌ No token streaming UI text found')
            
        # Check for progress indicators
        if any(word in text_lower for word in ['progress', '%', 'loading', 'processing']):
            analysis['findings'].append('✅ Progress indicators detected')
        else:
            analysis['issues'].append('❌ No progress indicators found')
            
    elif 'question_loaded_final' in filename.lower():
        analysis['type'] = 'FINAL_QUESTION'
        
        # Check if question is numerical
        if any(word in text_lower for word in ['calculate', 'number', 'atoms', 'moles', '×', '÷', '=', 'avogadro']):
            analysis['findings'].append('✅ Numerical question content detected')
        else:
            analysis['issues'].append('❌ Question appears to be conceptual, not numerical')
            
        # Check for expert-level content
        if any(word in text_lower for word in ['quantum', 'molecular', 'atomic', 'electron', 'orbital', 'bond']):
            analysis['findings'].append('✅ Expert-level terminology detected')
        else:
            analysis['issues'].append('❌ No expert-level terminology found')
            
        # Check for atoms topic
        if any(word in text_lower for word in ['atom', 'atomic', 'electron', 'proton', 'neutron', 'nucleus']):
            analysis['findings'].append('✅ Atoms topic content detected')
        else:
            analysis['issues'].append('❌ Content not clearly about atoms')
            
        # Check for multiple choice options
        if any(pattern in text_lower for pattern in ['a)', 'b)', 'c)', 'd)', 'a.', 'b.', 'c.', 'd.']):
            analysis['findings'].append('✅ Multiple choice options detected')
        else:
            analysis['issues'].append('❌ No multiple choice options found')
            
    else:
        analysis['type'] = 'OTHER'
        
        # General UI checks
        if any(word in text_lower for word in ['quiz', 'question', 'start', 'topic', 'difficulty']):
            analysis['findings'].append('✅ Quiz UI elements detected')
        
        if 'error' in text_lower or 'failed' in text_lower:
            analysis['issues'].append('❌ Error messages detected in UI')
    
    return analysis

def main():
    """Main function"""
    print("🔍 AUTOMATED SCREENSHOT ANALYZER")
    print("=" * 50)
    
    # Check if OCR packages are available
    if not install_required_packages():
        return
        
    if not setup_tesseract():
        return
    
    # Find screenshots directory
    screenshots_dir = Path("test_screenshots")
    if not screenshots_dir.exists():
        print("❌ Screenshots directory not found")
        return
    
    # Get all image files
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        image_files.extend(screenshots_dir.glob(ext))
    
    image_files.sort()
    
    if not image_files:
        print("❌ No image files found")
        return
    
    print(f"📸 Found {len(image_files)} screenshots to analyze")
    
    # Analyze each screenshot
    all_analyses = []
    
    for image_file in image_files:
        print(f"\n🔍 Analyzing: {image_file.name}")
        
        # Extract text from image
        extracted_text = extract_text_from_image(image_file)
        
        if extracted_text:
            print(f"📝 Extracted {len(extracted_text)} characters of text")
            
            # Analyze the text
            analysis = analyze_screenshot_text(image_file.name, extracted_text)
            all_analyses.append(analysis)
            
            # Print key findings
            if analysis['findings']:
                for finding in analysis['findings']:
                    print(f"  {finding}")
            
            if analysis['issues']:
                for issue in analysis['issues']:
                    print(f"  {issue}")
                    
        else:
            print("❌ No text extracted from image")
    
    # Generate comprehensive report
    print("\n" + "=" * 50)
    print("📊 COMPREHENSIVE ANALYSIS REPORT")
    print("=" * 50)
    
    # Save detailed analysis to file
    report_file = screenshots_dir / f"ocr_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'total_screenshots': len(image_files),
        'analyses': all_analyses,
        'summary': {
            'total_issues': sum(len(a['issues']) for a in all_analyses),
            'total_findings': sum(len(a['findings']) for a in all_analyses),
        }
    }
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"📄 Detailed report saved to: {report_file}")
    
    # Print summary
    generation_analyses = [a for a in all_analyses if a['type'] == 'GENERATION_IN_PROGRESS']
    question_analyses = [a for a in all_analyses if a['type'] == 'FINAL_QUESTION']
    
    if generation_analyses:
        print(f"\n🌊 TOKEN STREAMING ANALYSIS ({len(generation_analyses)} screenshots):")
        for analysis in generation_analyses:
            print(f"  📸 {analysis['filename']}")
            for issue in analysis['issues']:
                print(f"    {issue}")
            for finding in analysis['findings']:
                print(f"    {finding}")
    
    if question_analyses:
        print(f"\n🔢 QUESTION QUALITY ANALYSIS ({len(question_analyses)} screenshots):")
        for analysis in question_analyses:
            print(f"  📸 {analysis['filename']}")
            for issue in analysis['issues']:
                print(f"    {issue}")
            for finding in analysis['findings']:
                print(f"    {finding}")
    
    # Provide actionable recommendations
    print(f"\n🎯 ACTIONABLE RECOMMENDATIONS:")
    
    total_issues = sum(len(a['issues']) for a in all_analyses)
    if total_issues == 0:
        print("✅ No major issues detected! The app appears to be working correctly.")
    else:
        print(f"❌ Found {total_issues} issues that need attention:")
        
        # Token streaming issues
        streaming_issues = [issue for a in generation_analyses for issue in a['issues']]
        if streaming_issues:
            print("  🌊 Token Streaming Fixes Needed:")
            for issue in set(streaming_issues):
                print(f"    - {issue}")
        
        # Question quality issues  
        question_issues = [issue for a in question_analyses for issue in a['issues']]
        if question_issues:
            print("  🔢 Question Quality Fixes Needed:")
            for issue in set(question_issues):
                print(f"    - {issue}")
    
    print(f"\n✅ Analysis complete! Check {report_file} for full details.")

if __name__ == "__main__":
    main()
