"""
MCQ Manager - Unified interface for online and offline MCQ generation
🔥 FIRE: Now uses UnifiedInferenceManager for all AI operations
Eliminates race conditions and provides consistent error handling
🧠 ENHANCED: Now includes semantic topic mapping with Phi model
"""

import logging
import threading
import time
from typing import Dict, List, Any, Optional
import asyncio

from .unified_inference_manager import (
    get_unified_inference_manager,
    generate_mcq_unified,
    initialize_unified_inference,
    get_inference_status
)
from .mcq_coherence_monitor import get_coherence_monitor

logger = logging.getLogger(__name__)

class MCQManager:
    """
    🔥 FIRE Unified MCQ Manager - Single Source of Truth for MCQ Generation
    
    Now uses the UnifiedInferenceManager for all AI operations:
    - Eliminates race conditions between multiple model managers
    - Provides consistent error handling
    - Thread-safe operation with proper timeout handling
    - Centralized resource management
    """
    
    def __init__(self, config=None):
        self.config = config

        # 🔥 FIRE: Use unified inference manager instead of fragmented generators
        self._unified_manager = get_unified_inference_manager()
        self._initialized = False
        self._initialization_lock = threading.RLock()

        # 🧠 SEMANTIC MAPPING: Initialize topic analyzer with semantic mapping
        self._topic_analyzer = None
        self._semantic_mapper = None
        self._initialize_semantic_processing()

        # 🚀 GOLDEN PATH: Initialize RAG Engine for grounded generation
        self._rag_engine = None
        self._initialize_rag_engine()

        # MCQ quality enhancement
        self._coherence_monitor = get_coherence_monitor()

        # Content filtering
        self._inappropriate_topics = {
            'violence', 'nsfw', 'illegal', 'harmful', 'explicit',
            'gore', 'terrorism', 'drugs', 'weapons', 'hate'
        }

        logger.info("✅ MCQManager created with UnifiedInferenceManager, Semantic Mapping, and RAG Engine")

    def generate_quiz(self, quiz_params: Dict[str, Any]):
        """
        🚀 SYNC WRAPPER: Generate quiz synchronously for backward compatibility

        This method runs the async generation in a thread pool to prevent UI blocking.
        """
        try:
            # Run async generation in thread pool
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(self.generate_quiz_async(quiz_params))
                return result
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"❌ Sync quiz generation failed: {e}")
            return None

    def _initialize_semantic_processing(self):
        """Initialize semantic topic processing with Phi model"""
        try:
            # Initialize semantic mapper (uses Phi model)
            from .intelligent_semantic_mapper import get_semantic_mapper
            self._semantic_mapper = get_semantic_mapper()
            logger.info("🧠 Semantic mapper initialized with Phi model")

            # Initialize topic analyzer with semantic mapping enabled
            from .topic_analyzer import TopicAnalyzer
            self._topic_analyzer = TopicAnalyzer(use_semantic_mapping=True)
            logger.info("🎯 Topic analyzer initialized with semantic mapping enabled")

        except Exception as e:
            logger.warning(f"⚠️ Semantic processing initialization failed: {e}")
            logger.warning("🔄 Falling back to basic topic processing")
            self._semantic_mapper = None
            self._topic_analyzer = None

    def get_available_lora_adapters(self) -> List[str]:
        """Get list of available LoRA adapters for UI selection - DISABLED"""
        # 🚫 LORA DISABLED: Use normal Ollama models instead
        logger.info("📋 LoRA adapters disabled - using normal Ollama models")
        return []
    
    def _initialize_rag_engine(self):
        """Initialize the RAG engine for grounded MCQ generation - OPTIMIZED FOR SPEED"""
        try:
            # 🚀 SPEED OPTIMIZATION: Skip RAG initialization during startup to prevent 5+ minute hangs
            logger.info("⚡ Skipping RAG Engine initialization for faster startup")
            logger.info("💡 RAG Engine will be lazy-loaded when first needed")
            self._rag_engine = None
            
            # Previous slow initialization code commented out:
            # from .rag_engine import RAGEngine
            # self._rag_engine = RAGEngine()
            # Build index if documents are available - THIS CAUSES THE HANG!
            # self._rag_engine.build_index()
            # logger.info("🔍 RAG Engine initialized for grounded generation")
            
        except Exception as e:
            logger.warning(f"⚠️ RAG Engine initialization failed: {e}")
            self._rag_engine = None
    
    def _get_rag_context(self, topic: str) -> str:
        """Get RAG context for a topic - lazy initialize RAG engine if needed"""
        try:
            # Lazy initialization when first needed
            if self._rag_engine is None:
                logger.info("🔄 Lazy-loading RAG Engine...")
                from ..rag_engine import RAGEngine
                self._rag_engine = RAGEngine()
                # Skip document processing for now - use simple context
                logger.info("✅ RAG Engine lazy-loaded (without document processing)")
            
            # For now, return simple context to avoid document processing delays
            return f"Context for {topic}: Use your knowledge base to create relevant questions."
            
        except Exception as e:
            logger.warning(f"⚠️ RAG context generation failed: {e}")
            return f"Use your knowledge about {topic} to create questions."

    def _preprocess_topic_with_semantic_mapping(self, raw_topic: str) -> Dict[str, Any]:
        """
        🧠 SEMANTIC PREPROCESSING: Process topic through Phi model for intelligent analysis

        This is the missing piece that ensures topics are properly analyzed before generation.

        Args:
            raw_topic: Raw user input topic

        Returns:
            Dict with processed topic info and semantic analysis
        """
        try:
            logger.info(f"🧠 SEMANTIC PREPROCESSING: Processing topic '{raw_topic}'")

            # Step 1: Use semantic mapper for intelligent analysis
            if self._semantic_mapper:
                semantic_result = self._semantic_mapper.map_topic_semantically(raw_topic)
                logger.info(f"🎯 Semantic mapping: '{raw_topic}' → '{semantic_result.expanded_topic}' ({semantic_result.question_type}, confidence: {semantic_result.confidence:.2f})")

                # Step 2: Use topic analyzer for additional analysis
                if self._topic_analyzer:
                    topic_profile = self._topic_analyzer.get_topic_profile(semantic_result.expanded_topic)
                    logger.info(f"📊 Topic analysis: {topic_profile.get('detected_type', 'unknown')} (confidence: {topic_profile.get('confidence', 'unknown')})")

                    # Combine semantic mapping and topic analysis results
                    return {
                        "original_topic": raw_topic,
                        "processed_topic": semantic_result.expanded_topic,
                        "semantic_analysis": {
                            "question_type": semantic_result.question_type,
                            "confidence": semantic_result.confidence,
                            "reasoning": semantic_result.reasoning,
                            "is_abbreviation": semantic_result.is_abbreviation,
                            "full_form": semantic_result.full_form
                        },
                        "topic_profile": topic_profile,
                        "recommended_question_type": semantic_result.question_type,
                        "processing_method": "semantic_mapping"
                    }
                else:
                    # Only semantic mapping available
                    return {
                        "original_topic": raw_topic,
                        "processed_topic": semantic_result.expanded_topic,
                        "semantic_analysis": {
                            "question_type": semantic_result.question_type,
                            "confidence": semantic_result.confidence,
                            "reasoning": semantic_result.reasoning,
                            "is_abbreviation": semantic_result.is_abbreviation,
                            "full_form": semantic_result.full_form
                        },
                        "recommended_question_type": semantic_result.question_type,
                        "processing_method": "semantic_only"
                    }
            else:
                logger.warning("⚠️ Semantic mapper not available, using basic processing")
                return {
                    "original_topic": raw_topic,
                    "processed_topic": raw_topic,
                    "recommended_question_type": "mixed",
                    "processing_method": "basic"
                }

        except Exception as e:
            logger.error(f"❌ Semantic preprocessing failed for '{raw_topic}': {e}")
            return {
                "original_topic": raw_topic,
                "processed_topic": raw_topic,
                "recommended_question_type": "mixed",
                "processing_method": "error_recovery",
                "error": str(e)
            }

    def _ensure_initialized(self):
        """Ensure the unified inference manager is initialized - PREVENTS RE-INITIALIZATION"""
        if self._initialized:
            return True
            
        with self._initialization_lock:
            if self._initialized:
                return True
            
            # 🔥 CRITICAL FIX: Check if UnifiedInferenceManager is already ready
            current_status = get_inference_status()
            if current_status.get("state") == "ready":
                logger.info("🚀 UnifiedInferenceManager already ready - skipping re-initialization")
                self._initialized = True
                return True
            
            # 🚀 GOLDEN PATH FIX: Always ensure UnifiedInferenceManager is properly initialized
            logger.info("🔄 Initializing UnifiedInferenceManager for Golden Path MCQ generation...")
            success = initialize_unified_inference(self.config)
            
            if success:
                self._initialized = True
                logger.info("🚀 UnifiedInferenceManager ready for Golden Path generation")
                
                # Verify Ollama is actually detected
                final_status = get_inference_status()
                logger.info(f"🔍 Final UIM status: {final_status}")
                return True
            else:
                logger.error("❌ Failed to initialize UnifiedInferenceManager")
                return False
    
    def _ensure_generators_initialized(self):
        """Ensure generators are initialized (legacy compatibility method)"""
        return self._ensure_initialized()
    
    async def generate_quiz_async(self, quiz_params: Dict[str, Any]):
        """
        🚀 ASYNC GOLDEN PATH: Generate grounded MCQ using RAG + Semantic Mapping

        Follows the "Grounded Scholar" pipeline ASYNCHRONOUSLY:
        1. Semantic preprocessing with Phi model (ASYNC)
        2. Retrieve relevant context from user's documents using RAG
        3. Generate MCQ grounded in retrieved context (ASYNC)
        4. Validate and post-process for quality
        """
        try:
            # 🎯 COMPREHENSIVE GENERATION LOGGING - START
            logger.info("="*80)
            logger.info("🎯 QUESTION GENERATION SESSION STARTED")
            logger.info("="*80)
            
            # Extract and validate parameters
            topic = quiz_params.get('topic', 'General Knowledge')
            difficulty = quiz_params.get('difficulty', 'medium')
            question_type = quiz_params.get('submode', 'mixed')
            # 🚫 LORA DISABLED: No adapter needed - use normal Ollama models
            adapter_name = None
            mode = quiz_params.get('mode', 'auto')  # 🔥 FIX: Extract mode from parameters
            num_questions = quiz_params.get('num_questions', 1)
            game_mode = quiz_params.get('game_mode', 'casual')
            timer_setting = quiz_params.get('timer', '30s')

            # 🧠 SEMANTIC PREPROCESSING: Process topic through Phi model FIRST (ASYNC)
            logger.info("🧠 STEP 1: SEMANTIC TOPIC PREPROCESSING (ASYNC)")

            # Run semantic preprocessing in thread pool to avoid blocking UI
            loop = asyncio.get_event_loop()
            topic_analysis = await loop.run_in_executor(
                None,
                self._preprocess_topic_with_semantic_mapping,
                topic
            )

            # Update topic and question_type based on semantic analysis
            original_topic = topic
            processed_topic = topic_analysis.get('processed_topic', topic)
            recommended_question_type = topic_analysis.get('recommended_question_type', question_type)

            # Use processed topic for generation
            topic = processed_topic

            # Override question_type if semantic analysis provides better recommendation
            if question_type == 'mixed' and recommended_question_type != 'mixed':
                logger.info(f"🎯 Semantic analysis recommends '{recommended_question_type}' over '{question_type}'")
                question_type = recommended_question_type

            # Log semantic analysis results
            logger.info("🧠 SEMANTIC ANALYSIS RESULTS:")
            logger.info(f"   • ORIGINAL_TOPIC: '{original_topic}'")
            logger.info(f"   • PROCESSED_TOPIC: '{processed_topic}'")
            logger.info(f"   • PROCESSING_METHOD: {topic_analysis.get('processing_method', 'unknown')}")
            if 'semantic_analysis' in topic_analysis:
                semantic = topic_analysis['semantic_analysis']
                logger.info(f"   • SEMANTIC_CONFIDENCE: {semantic.get('confidence', 'unknown')}")
                logger.info(f"   • SEMANTIC_REASONING: {semantic.get('reasoning', 'unknown')}")
                logger.info(f"   • IS_ABBREVIATION: {semantic.get('is_abbreviation', False)}")
                if semantic.get('full_form'):
                    logger.info(f"   • FULL_FORM: {semantic.get('full_form')}")

            # 🧠 EXPERT MODE: Auto-detect DeepSeek usage for expert difficulty
            use_deepseek = (difficulty == 'expert')
            if use_deepseek:
                logger.info("🧠 EXPERT DIFFICULTY DETECTED - Using DeepSeek pipeline automatically")

            # 🎯 LOG ALL GENERATION PARAMETERS (UPDATED)
            logger.info("📋 FINAL GENERATION PARAMETERS:")
            logger.info(f"   • ORIGINAL_TOPIC: '{original_topic}'")
            logger.info(f"   • PROCESSED_TOPIC: '{topic}'")
            logger.info(f"   • DIFFICULTY: '{difficulty}'")
            logger.info(f"   • QUESTION_TYPE: '{question_type}' {'(SEMANTIC OVERRIDE)' if question_type != quiz_params.get('submode', 'mixed') else ''}")
            logger.info(f"   • MODE: '{mode}'")
            logger.info(f"   • NUM_QUESTIONS: {num_questions}")
            logger.info(f"   • GAME_MODE: '{game_mode}'")
            logger.info(f"   • TIMER_SETTING: '{timer_setting}'")
            logger.info(f"   • LORA_ADAPTER: '{adapter_name}' {'(ENABLED)' if adapter_name else '(NONE)'}")
            
            # 🎯 LOG SYSTEM AVAILABILITY STATUS
            logger.info("🔍 SYSTEM AVAILABILITY CHECK:")
            system_status = self.get_system_status()
            logger.info(f"   • UNIFIED_MANAGER_STATUS: {system_status.get('status', 'unknown')}")
            logger.info(f"   • OFFLINE_AVAILABLE: {self.is_offline_available()}")
            logger.info(f"   • ONLINE_AVAILABLE: {self.is_online_available()}")
            
            # Ensure unified manager is initialized
            if not self._ensure_initialized():
                logger.error("❌ UnifiedInferenceManager not available")
                logger.error("="*80)
                return None
            
            # 🎯 DETERMINE AND LOG GENERATOR SELECTION
            logger.info("🚀 GENERATOR SELECTION LOGIC:")
            
            inference_status = get_inference_status()
            local_available = inference_status.get("local_available", False)
            cloud_available = inference_status.get("cloud_available", False)
            
            logger.info(f"   • LOCAL_MODELS_AVAILABLE: {local_available}")
            logger.info(f"   • CLOUD_APIS_AVAILABLE: {cloud_available}")
            
            # Determine which generator will be used based on mode and availability
            selected_generator = "unknown"
            generator_reason = "unknown"
            
            if mode == "offline":
                if local_available:
                    selected_generator = "OFFLINE (Local Models)"
                    generator_reason = "User selected offline mode and local models available"
                else:
                    selected_generator = "NONE - OFFLINE REQUESTED BUT UNAVAILABLE"
                    generator_reason = "User requested offline but no local models available"
            elif mode == "online":
                if cloud_available:
                    selected_generator = "ONLINE (Cloud APIs)"
                    generator_reason = "User selected online mode and cloud APIs available"
                else:
                    selected_generator = "NONE - ONLINE REQUESTED BUT UNAVAILABLE" 
                    generator_reason = "User requested online but no cloud APIs available"
            elif mode == "auto":
                if local_available and cloud_available:
                    selected_generator = "AUTO (Both Available - Will Auto-Select)"
                    generator_reason = "Auto mode with both local and cloud available"
                elif local_available:
                    selected_generator = "AUTO -> OFFLINE (Only Local Available)"
                    generator_reason = "Auto mode defaulting to local models"
                elif cloud_available:
                    selected_generator = "AUTO -> ONLINE (Only Cloud Available)"
                    generator_reason = "Auto mode defaulting to cloud APIs"
                else:
                    selected_generator = "NONE - NO GENERATORS AVAILABLE"
                    generator_reason = "No local models or cloud APIs available"
            
            logger.info(f"🎯 SELECTED_GENERATOR: {selected_generator}")
            logger.info(f"🎯 SELECTION_REASON: {generator_reason}")
            
            # 🔥 CRITICAL FIX: Set the inference mode based on user selection
            self._unified_manager.set_inference_mode(mode)
            logger.info(f"🎯 Mode set to: {mode} for MCQ generation")

            # Apply content filtering
            filtered_topic = self._filter_inappropriate_topic(topic)
            if filtered_topic != topic:
                logger.info(f"🛡️ Content filter: '{topic}' → '{filtered_topic}'")
                topic = filtered_topic

            logger.info(f"🎯 GOLDEN PATH: Generating MCQ: topic='{topic}', difficulty='{difficulty}', adapter='{adapter_name}'")

            # GOLDEN PATH DECISION TREE 🌟
            context_for_generation = None
            generation_method = "raw_model"

            # STEP 1: PRIORITIZE LORA ADAPTER (Domain Specialization) 🔗
            if adapter_name:
                logger.info(f"🔗 GOLDEN PATH: Using LoRA adapter '{adapter_name}' for domain specialization")
                generation_method = "lora_specialized"
                # LoRA adapters don't need RAG context - they ARE the domain knowledge
                context_for_generation = None

            # STEP 2: RAG CONTEXT (Document Grounding) 📚
            elif self._rag_engine and self._rag_engine.is_initialized:
                logger.info(f"📚 GOLDEN PATH: No LoRA adapter specified, using RAG context for grounding")
                retrieved_context = self._retrieve_grounded_context(topic)
                if retrieved_context:
                    context_for_generation = "\n\n---\n\n".join(retrieved_context)
                    generation_method = "rag_grounded"
                    logger.info(f"📚 Retrieved {len(retrieved_context)} context chunks ({len(context_for_generation)} chars)")
                else:
                    logger.warning(f"⚠️ No RAG context found for '{topic}'")

            # STEP 3: RAW MODEL GENERATION 🤖
            else:
                logger.info(f"🤖 GOLDEN PATH: Using raw model generation (no LoRA or RAG available)")

            # 🎯 LOG FINAL GENERATION METHOD
            logger.info("🚀 FINAL GENERATION SETUP:")
            logger.info(f"   • GENERATION_METHOD: {generation_method}")
            logger.info(f"   • CONTEXT_PROVIDED: {'YES' if context_for_generation else 'NO'}")
            logger.info(f"   • CONTEXT_LENGTH: {len(context_for_generation) if context_for_generation else 0} chars")
            logger.info(f"   • LORA_ADAPTER: '{adapter_name}' {'(ACTIVE)' if adapter_name else '(NONE)'}")

            # 🧠 EXPERT MODE: Use DeepSeek pipeline for expert difficulty
            if use_deepseek:
                logger.info("🧠 Using DeepSeek two-model pipeline for expert generation...")
                try:
                    from .deepseek_integration import get_deepseek_pipeline

                    deepseek_pipeline = get_deepseek_pipeline()
                    if deepseek_pipeline and deepseek_pipeline.is_ready():
                        logger.info("🧠 DeepSeek pipeline ready - generating expert question...")

                        # 🎯 PASS SEMANTIC GENERATION INSTRUCTIONS TO DEEPSEEK
                        generation_instructions = None
                        if 'semantic_analysis' in topic_analysis:
                            generation_instructions = topic_analysis['semantic_analysis'].get('reasoning', None)
                            logger.info(f"🧠 Using Phi-generated instructions: {generation_instructions}")

                        # 🚀 ASYNC DEEPSEEK: Run in executor to prevent UI blocking
                        loop = asyncio.get_event_loop()
                        deepseek_result = await loop.run_in_executor(
                            None,
                            deepseek_pipeline.generate_expert_question,
                            topic,
                            difficulty,
                            question_type,
                            context_for_generation,
                            None,  # progress_callback
                            generation_instructions  # 🎯 NEW: Pass Phi instructions
                        )

                        if deepseek_result:
                            logger.info("✅ DeepSeek expert question generated successfully")
                            # Convert DeepSeek format to MCQ format
                            result = self._convert_deepseek_to_mcq_format(deepseek_result, topic, quiz_params)
                        else:
                            logger.warning("⚠️ DeepSeek generation failed, falling back to unified inference")
                            result = None
                    else:
                        logger.warning("⚠️ DeepSeek pipeline not ready, falling back to unified inference")
                        result = None

                except Exception as e:
                    logger.error(f"❌ DeepSeek generation failed: {e}")
                    logger.warning("⚠️ Using unified inference instead")
                    result = None
            else:
                result = None

            # Use unified inference if DeepSeek failed or not expert mode
            if not result:
                logger.info(f"🚀 Generating MCQ using {generation_method} method...")
                logger.info("🔄 Calling UnifiedInferenceManager...")

                # 🎯 PASS SEMANTIC GENERATION INSTRUCTIONS TO UNIFIED INFERENCE
                generation_instructions = None
                if 'semantic_analysis' in topic_analysis:
                    generation_instructions = topic_analysis['semantic_analysis'].get('reasoning', None)
                    logger.info(f"🧠 Using Phi-generated instructions: {generation_instructions}")

                # 🚀 ASYNC GENERATION: Use async unified inference to prevent UI blocking
                from .unified_inference_manager import InferenceRequest

                # Create async request
                request = InferenceRequest(
                    request_id=f"mcq_async_{int(time.time() * 1000)}",
                    operation="generate_mcq",
                    params={
                        "topic": topic,
                        "difficulty": difficulty,
                        "question_type": question_type,
                        "context": context_for_generation,
                        "adapter_name": None,  # 🚫 LORA DISABLED
                        "generation_instructions": generation_instructions
                    },
                    timeout=90.0
                )

                # Call async handler directly
                result = await self._unified_manager._handle_mcq_generation(request)

            if result:
                # STEP 4: VALIDATION AND POST-PROCESSING ✅
                # Handle DeepSeek MCQResult objects vs regular dict results
                if isinstance(result, MCQResult):
                    # DeepSeek already returned a formatted MCQResult
                    mcq_result = result
                    logger.info("✅ Using pre-formatted DeepSeek MCQResult")
                else:
                    # Regular dict result needs validation and formatting
                    mcq_result = self._validate_and_format_result(result, topic, quiz_params)

                if mcq_result:
                    # Add Golden Path metadata (update if DeepSeek, set if regular)
                    if not hasattr(mcq_result, 'generation_method') or mcq_result.generation_method == "raw_model":
                        mcq_result.generation_method = generation_method
                    mcq_result.grounded = (generation_method in ["lora_specialized", "rag_grounded", "deepseek_pipeline"])
                    mcq_result.context_chunks = len(context_for_generation.split('\n\n---\n\n')) if context_for_generation else 0
                    mcq_result.adapter_used = adapter_name

                    logger.info(f"✅ GOLDEN PATH: {mcq_result.generation_method} MCQ generated and validated successfully")
                    logger.info("="*80)
                    logger.info("🎯 QUESTION GENERATION SESSION COMPLETED SUCCESSFULLY")
                    logger.info("="*80)

                    # 🔥 CRITICAL FIX: Convert MCQResult to dictionary for UI compatibility
                    return {
                        "question": mcq_result.question,
                        "options": mcq_result.options,
                        "correct_answer": mcq_result.correct_answer,
                        "explanation": mcq_result.explanation,
                        "difficulty": getattr(mcq_result, 'difficulty', 'medium'),
                        "generation_method": getattr(mcq_result, 'generation_method', 'unknown'),
                        "grounded": getattr(mcq_result, 'grounded', False),
                        "context_chunks": getattr(mcq_result, 'context_chunks', 0),
                        "adapter_used": getattr(mcq_result, 'adapter_used', None)
                    }
                else:
                    logger.error("❌ MCQ validation failed - no question generated")
                    logger.error("="*80)
                    return None
            else:
                logger.error("❌ UnifiedInferenceManager returned no result")
                logger.error("="*80)
                return None

        except Exception as e:
            logger.error(f"❌ GOLDEN PATH MCQ generation failed: {e}")
            logger.error("="*80)
            return None
    
    def _retrieve_grounded_context(self, topic: str, top_k: int = 3) -> List[str]:
        """
        STEP 1 of Golden Path: Retrieve relevant context using RAG
        
        Returns:
            List of relevant text chunks from user's documents
        """
        try:
            if not self._rag_engine:
                logger.warning("📚 RAG engine not available for context retrieval")
                return []
            
            logger.info(f"🔍 Retrieving context for topic: '{topic}'")
            contexts = self._rag_engine.retrieve_context(topic, top_k=top_k)
            
            if contexts:
                logger.info(f"✅ Retrieved {len(contexts)} relevant context chunks")
                # Log first chunk preview for debugging
                if len(contexts) > 0:
                    preview = contexts[0][:200] + "..." if len(contexts[0]) > 200 else contexts[0]
                    logger.debug(f"📖 Context preview: {preview}")
                return contexts
            else:
                logger.warning(f"⚠️ No relevant context found for topic: '{topic}'")
                return []
                
        except Exception as e:
            logger.error(f"❌ Context retrieval failed: {e}")
            return []
    
    def _validate_and_format_result(self, result: Dict[str, Any], topic: str, quiz_params: Dict[str, Any]) -> Optional['MCQResult']:
        """Validate and format MCQ result with coherence monitoring"""
        try:
            # Handle both single dict and list formats from different generators
            if isinstance(result, list) and len(result) > 0:
                # Ollama generator returns a list - use first item
                result_dict = result[0]
                logger.debug("✅ Using first item from list result")
            elif isinstance(result, dict):
                # Direct dict format from some generators
                result_dict = result
                logger.debug("✅ Using direct dict result")
            else:
                logger.error(f"❌ Invalid result format: {type(result)}")
                return None
            
            # Extract data from result
            question = result_dict.get('question', '')
            options = result_dict.get('options', [])
            correct_answer = result_dict.get('correct_answer', '')
            explanation = result_dict.get('explanation', 'No explanation available.')
            
            # Basic validation
            if not question or len(options) < 4 or not correct_answer:
                logger.error("❌ MCQ result missing required fields")
                return None
            
            # 🚨 COHERENCE MONITORING - Detect AI spaghetti in real-time!
            is_coherent, issues = self._coherence_monitor.monitor_mcq(
                question=question,
                options=options,
                topic=topic,
                context=f"Difficulty: {quiz_params.get('difficulty', 'medium')}"
            )
            
            if not is_coherent:
                logger.error(f"🚨 SPAGHETTI DETECTED! Issues: {[issue.description for issue in issues]}")
                
                # Check for critical issues that make the question unusable
                critical_issues = [issue for issue in issues if hasattr(issue, 'severity') and issue.severity == 'critical']
                if critical_issues:
                    logger.error("🚨 CRITICAL SPAGHETTI - Question is unusable!")
                    return None
                else:
                    logger.warning("⚠️ Minor coherence issues detected but question is usable")
            else:
                logger.info("✅ MCQ passed coherence monitoring")
            
            # Create MCQResult object
            mcq_result = MCQResult(
                question=question,
                options=options,
                correct_answer=correct_answer,
                explanation=explanation
            )
            
            return mcq_result
            
        except Exception as e:
            logger.error(f"❌ MCQ validation failed: {e}")
            return None
    

    
    def _filter_inappropriate_topic(self, topic: str) -> str:
        """Filter inappropriate topics and return safe alternative"""
        topic_lower = topic.lower()
        
        for inappropriate in self._inappropriate_topics:
            if inappropriate in topic_lower:
                logger.warning(f"🛡️ Inappropriate topic detected: {topic}")
                return "General Knowledge"
        
        return topic
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive Golden Path system status"""
        try:
            if not self._ensure_initialized():
                return {
                    "status": "error",
                    "message": "UnifiedInferenceManager not initialized",
                    "available_methods": []
                }
            
            # Get status from unified inference manager
            inference_status = get_inference_status()
            
            # Check Golden Path capabilities
            available_lora_adapters = self.get_available_lora_adapters()
            rag_available = self._rag_engine and self._rag_engine.is_initialized
            
            return {
                "status": "ready" if inference_status.get("state") == "ready" else "initializing",
                "unified_manager": inference_status,
                "golden_path": {
                    "lora_adapters_available": len(available_lora_adapters),
                    "lora_adapter_names": available_lora_adapters,
                    "rag_engine_ready": rag_available,
                    "generation_hierarchy": ["LoRA Adapter", "RAG Context", "Raw Model"]
                },
                "coherence_monitoring": "enabled",
                "content_filtering": "enabled",
                "error_handling": "active"
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get system status: {e}")
            return {
                "status": "error",
                "message": str(e),
                "available_methods": []
            }

    def _convert_deepseek_to_mcq_format(self, deepseek_result: Dict[str, Any], topic: str, quiz_params: Dict[str, Any]):
        """Convert DeepSeek result format to MCQ format"""
        try:
            # MCQResult is defined in this same file

            # Extract data from DeepSeek result
            question = deepseek_result.get('question', '')
            options_dict = deepseek_result.get('options', {})
            correct_letter = deepseek_result.get('correct', 'A')
            explanation = deepseek_result.get('explanation', 'No explanation available.')

            # Convert options dict to list
            if isinstance(options_dict, dict):
                options = [options_dict.get(letter, '') for letter in ['A', 'B', 'C', 'D']]
                # Find correct answer text
                correct_answer = options_dict.get(correct_letter, options[0] if options else '')
            else:
                options = options_dict if isinstance(options_dict, list) else []
                correct_answer = options[0] if options else ''

            # Create MCQ result with basic parameters including difficulty
            difficulty = quiz_params.get('difficulty', 'medium')
            mcq_result = MCQResult(
                question=question,
                options=options,
                correct_answer=correct_answer,
                explanation=explanation,
                difficulty=difficulty
            )

            # Set DeepSeek metadata
            mcq_result.generation_method = "deepseek_pipeline"
            mcq_result.grounded = True
            mcq_result.context_chunks = 0
            mcq_result.adapter_used = None

            logger.info("✅ DeepSeek result converted to MCQ format")
            return mcq_result

        except Exception as e:
            logger.error(f"❌ Failed to convert DeepSeek result: {e}")
            return None

    # Legacy compatibility methods - these now use the unified system
    def set_offline_mode(self, offline: bool):
        """Set offline mode (legacy compatibility)"""
        # The unified system handles mode switching automatically
        logger.info(f"Mode preference noted: {'offline' if offline else 'online'}")
    
    def is_offline_available(self) -> bool:
        """Check if offline mode is available"""
        try:
            if not self._ensure_initialized():
                return False
            status = get_inference_status()
            return status.get("local_available", False)
        except:
            return False
    
    def is_online_available(self) -> bool:
        """Check if online mode is available"""
        try:
            if not self._ensure_initialized():
                return False
            status = get_inference_status()
            return status.get("cloud_available", False)
        except:
            return False


class MCQResult:
    """MCQ result container with Golden Path metadata"""

    def __init__(self, question: str, options: List[str], correct_answer: str, explanation: str = "No explanation available.", difficulty: str = "medium"):
        self.question = question
        self.options = options
        self.correct_answer = correct_answer
        self.explanation = explanation
        self.difficulty = difficulty  # Preserve difficulty level

        # Golden Path metadata
        self.generation_method = "raw_model"  # Which generation method was used
        self.grounded = False  # Whether specialized knowledge was used (LoRA or RAG)
        self.context_chunks = 0  # Number of context chunks retrieved (RAG only)
        self.adapter_used = None  # LoRA adapter name used (LoRA only)


def get_mcq_manager(config=None) -> MCQManager:
    """
    🔥 FIRE: Get MCQ manager with unified inference system
    
    This is the main function for getting an MCQ manager instance.
    Now uses the UnifiedInferenceManager for all operations.
    """
    return MCQManager(config)