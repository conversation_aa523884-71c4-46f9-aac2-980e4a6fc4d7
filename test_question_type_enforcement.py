#!/usr/bin/env python3
"""
🎯 Test Question Type Enforcement
Verifies that the app properly follows user's question type choices
"""

import sys
import os
import json
import time
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_numerical_question_generation():
    """Test that numerical questions are actually numerical"""
    print("🔢 TESTING NUMERICAL QUESTION GENERATION")
    print("=" * 50)
    
    try:
        from knowledge_app.core.deepseek_integration import get_deepseek_pipeline
        
        pipeline = get_deepseek_pipeline()
        if not pipeline.is_ready():
            print("❌ DeepSeek pipeline not ready")
            return False
        
        # Test numerical question generation
        print("🧪 Generating NUMERICAL question about 'atoms'...")
        
        result = pipeline.generate_expert_question(
            topic="atoms",
            difficulty="expert",
            question_type="numerical"
        )
        
        if result:
            question = result.get('question', '')
            options = result.get('options', {})
            
            print(f"✅ Generated question:")
            print(f"   Question: {question}")
            print(f"   Options: {options}")
            
            # Check if it's actually numerical
            numerical_indicators = [
                'calculate', 'compute', 'number', 'value', 'mass', 'energy',
                'electrons', 'protons', 'neutrons', 'atomic number', 'atomic mass',
                'wavelength', 'frequency', 'joules', 'kg', 'mol', 'nm', 'pm',
                '×', 'x', '*', '/', '+', '-', '=', 'equation', 'formula'
            ]
            
            has_numerical_content = any(
                indicator.lower() in question.lower() or 
                any(indicator.lower() in str(opt).lower() for opt in options.values())
                for indicator in numerical_indicators
            )
            
            # Check for actual numbers in question or options
            import re
            has_numbers = bool(re.search(r'\d+', question)) or any(
                re.search(r'\d+', str(opt)) for opt in options.values()
            )
            
            if has_numerical_content or has_numbers:
                print("✅ CORRECT: Question contains numerical content")
                return True
            else:
                print("❌ WRONG: Question is conceptual, not numerical")
                print("   This indicates the prompt is not being followed properly")
                return False
        else:
            print("❌ Failed to generate question")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_conceptual_question_generation():
    """Test that conceptual questions are actually conceptual"""
    print("\n🧠 TESTING CONCEPTUAL QUESTION GENERATION")
    print("=" * 50)
    
    try:
        from knowledge_app.core.deepseek_integration import get_deepseek_pipeline
        
        pipeline = get_deepseek_pipeline()
        
        # Test conceptual question generation
        print("🧪 Generating CONCEPTUAL question about 'atoms'...")
        
        result = pipeline.generate_expert_question(
            topic="atoms",
            difficulty="expert",
            question_type="conceptual"
        )
        
        if result:
            question = result.get('question', '')
            options = result.get('options', {})
            
            print(f"✅ Generated question:")
            print(f"   Question: {question}")
            print(f"   Options: {options}")
            
            # Check if it's actually conceptual
            conceptual_indicators = [
                'why', 'how', 'what', 'which', 'explain', 'describe', 'compare',
                'principle', 'theory', 'concept', 'model', 'behavior', 'property',
                'characteristic', 'nature', 'structure', 'arrangement', 'interaction',
                'relationship', 'influence', 'affect', 'cause', 'result', 'due to'
            ]
            
            has_conceptual_content = any(
                indicator.lower() in question.lower() or 
                any(indicator.lower() in str(opt).lower() for opt in options.values())
                for indicator in conceptual_indicators
            )
            
            # Check that it doesn't have numerical calculations
            import re
            has_calculations = bool(re.search(r'\d+\s*[×x*/+\-=]\s*\d+', question)) or any(
                re.search(r'\d+\s*[×x*/+\-=]\s*\d+', str(opt)) for opt in options.values()
            )
            
            if has_conceptual_content and not has_calculations:
                print("✅ CORRECT: Question is conceptual, not numerical")
                return True
            else:
                print("❌ WRONG: Question contains numerical calculations or lacks conceptual focus")
                return False
        else:
            print("❌ Failed to generate question")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_mcq_manager_integration():
    """Test that MCQ manager properly passes question type to DeepSeek"""
    print("\n🔗 TESTING MCQ MANAGER INTEGRATION")
    print("=" * 50)
    
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        mcq_manager = get_mcq_manager()
        
        # Test with numerical expert mode
        quiz_params = {
            "topic": "atoms",
            "difficulty": "expert",
            "submode": "numerical",  # This should be passed as question_type
            "game_mode": "serious",
            "num_questions": 1,
            "mode": "auto"
        }
        
        print(f"🧪 Testing MCQ manager with params: {quiz_params}")
        
        result = mcq_manager.generate_quiz(quiz_params)
        
        if result:
            print("✅ MCQ manager generated question successfully")
            print(f"   Question: {result.question[:100]}...")
            print(f"   Options: {len(result.options)}")
            
            # Check if the question type was respected
            question_text = result.question.lower()
            
            # Look for numerical indicators
            import re
            has_numbers = bool(re.search(r'\d+', result.question))
            has_calculations = bool(re.search(r'calculate|compute|find|determine', question_text))
            
            if has_numbers or has_calculations:
                print("✅ CORRECT: MCQ manager respected numerical question type")
                return True
            else:
                print("⚠️ UNCLEAR: Question may not be clearly numerical")
                print("   This could indicate the question type is still not being passed correctly")
                return False
        else:
            print("❌ MCQ manager failed to generate question")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🎯 QUESTION TYPE ENFORCEMENT TEST")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Numerical question generation
    if test_numerical_question_generation():
        success_count += 1
        print("✅ Test 1/3: Numerical question generation working")
    else:
        print("❌ Test 1/3: Numerical question generation failed")
    
    # Test 2: Conceptual question generation
    if test_conceptual_question_generation():
        success_count += 1
        print("✅ Test 2/3: Conceptual question generation working")
    else:
        print("❌ Test 2/3: Conceptual question generation failed")
    
    # Test 3: MCQ manager integration
    if test_mcq_manager_integration():
        success_count += 1
        print("✅ Test 3/3: MCQ manager integration working")
    else:
        print("❌ Test 3/3: MCQ manager integration failed")
    
    print(f"\n🎯 QUESTION TYPE ENFORCEMENT RESULTS: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 QUESTION TYPE ENFORCEMENT WORKING PERFECTLY!")
        print("   The app should now properly follow your question type choices")
    elif success_count >= 2:
        print("✅ MOSTLY WORKING")
        print("   Question type enforcement is mostly functional")
    else:
        print("❌ MAJOR ISSUES DETECTED")
        print("   Question type enforcement is not working properly")
    
    print("\n💡 NEXT STEPS:")
    print("   1. Restart the app to ensure all changes take effect")
    print("   2. Try selecting 'numerical' + 'expert' in the UI")
    print("   3. Verify you get numerical questions with calculations")
    print("   4. Try 'conceptual' + 'expert' and verify you get theory questions")

if __name__ == "__main__":
    main()
