#!/usr/bin/env python3
"""
🎯 Test MBT Semantic Mapping
Quick test to verify MBT works with semantic mapping
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_mbt_direct():
    """Test MBT directly with semantic mapper"""
    print("🎯 TESTING MBT WITH SEMANTIC MAPPER")
    print("=" * 50)
    
    try:
        from knowledge_app.core.intelligent_semantic_mapper import get_semantic_mapper
        
        mapper = get_semantic_mapper()
        
        print("🧪 Testing 'MBT' with semantic mapper...")
        result = mapper.map_topic_semantically("MBT")
        
        print(f"✅ Direct semantic mapping results:")
        print(f"   Original: {result.original_input}")
        print(f"   Expanded: {result.expanded_topic}")
        print(f"   Type: {result.question_type}")
        print(f"   Confidence: {result.confidence:.2f}")
        print(f"   Reasoning: {result.reasoning}")
        print(f"   Is abbreviation: {result.is_abbreviation}")
        print(f"   Full form: {result.full_form}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mbt_through_analyzer():
    """Test MBT through the TopicAnalyzer"""
    print("\n🔗 TESTING MBT THROUGH TOPIC ANALYZER")
    print("=" * 50)
    
    try:
        from knowledge_app.core.topic_analyzer import get_topic_analyzer
        
        analyzer = get_topic_analyzer()
        
        print("🧪 Testing 'MBT' through TopicAnalyzer...")
        profile = analyzer.get_topic_profile("MBT")
        
        print(f"✅ TopicAnalyzer results:")
        for key, value in profile.items():
            print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_various_abbreviations():
    """Test various abbreviations to see what works"""
    print("\n🔤 TESTING VARIOUS ABBREVIATIONS")
    print("=" * 50)
    
    test_cases = ["MBT", "CBT", "AI", "ML", "DNA", "GDP", "HTTP", "SQL"]
    
    try:
        from knowledge_app.core.topic_analyzer import get_topic_analyzer
        
        analyzer = get_topic_analyzer()
        
        for abbrev in test_cases:
            print(f"\n🧪 Testing '{abbrev}':")
            
            try:
                profile = analyzer.get_topic_profile(abbrev)
                
                print(f"   Type: {profile.get('detected_type')}")
                print(f"   Expanded: {profile.get('expanded_topic', 'N/A')}")
                print(f"   Confidence: {profile.get('confidence')}")
                print(f"   Semantic: {profile.get('semantic_analysis', False)}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🎯 MBT SEMANTIC MAPPING TEST")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Direct semantic mapping
    if test_mbt_direct():
        success_count += 1
        print("✅ Test 1/3: Direct semantic mapping working")
    else:
        print("❌ Test 1/3: Direct semantic mapping failed")
    
    # Test 2: Through TopicAnalyzer
    if test_mbt_through_analyzer():
        success_count += 1
        print("✅ Test 2/3: TopicAnalyzer integration working")
    else:
        print("❌ Test 2/3: TopicAnalyzer integration failed")
    
    # Test 3: Various abbreviations
    if test_various_abbreviations():
        success_count += 1
        print("✅ Test 3/3: Various abbreviations tested")
    else:
        print("❌ Test 3/3: Various abbreviations failed")
    
    print(f"\n🎯 MBT TEST RESULTS: {success_count}/{total_tests} tests passed")
    
    if success_count >= 2:
        print("🎉 MBT SEMANTIC MAPPING IS WORKING!")
        print("   The issue might be in the UI connection or app restart needed")
    else:
        print("❌ MBT SEMANTIC MAPPING HAS ISSUES")
    
    print("\n💡 TROUBLESHOOTING:")
    print("   1. Make sure you restart the app completely")
    print("   2. Check browser console for any JavaScript errors")
    print("   3. Try typing slowly to trigger the debounced analysis")
    print("   4. Check if the topic field has the correct ID 'quiz-topic'")

if __name__ == "__main__":
    main()
