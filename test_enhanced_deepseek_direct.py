#!/usr/bin/env python3
"""
Direct test of the enhanced DeepSeek two-model pipeline
"""

import asyncio
import time
from datetime import datetime

async def test_enhanced_deepseek_direct():
    """Test the enhanced DeepSeek pipeline directly"""
    print("🧠 TESTING ENHANCED DEEPSEEK PIPELINE DIRECTLY")
    print("=" * 60)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Import the enhanced DeepSeek pipeline
        from src.knowledge_app.core.deepseek_integration import DeepSeekTwoModelPipeline
        
        print("\n🔧 Initializing enhanced DeepSeek pipeline...")
        pipeline = DeepSeekTwoModelPipeline()
        
        print(f"✅ Pipeline initialized")
        print(f"   Thinking model: {pipeline.thinking_model}")
        print(f"   JSON model: {pipeline.json_model}")
        print(f"   Available models: {pipeline.available_models}")
        
        # Test topics that should generate PhD-level content
        test_topics = [
            "quantum field theory",
            "machine learning theory", 
            "organic synthesis mechanisms",
            "protein folding dynamics",
            "distributed consensus protocols"
        ]
        
        success_count = 0
        
        for i, topic in enumerate(test_topics, 1):
            print(f"\n🎓 Direct Test {i}/5: {topic}")
            print("   🧠 Calling enhanced DeepSeek pipeline directly...")
            
            start_time = time.time()
            
            # Call the enhanced pipeline directly
            result = pipeline.generate_expert_question(
                topic=topic,
                difficulty="expert",  # This should trigger PhD-level prompts
                question_type="mixed",
                context=None,
                progress_callback=None,
                generation_instructions=None
            )
            
            duration = time.time() - start_time
            
            if result:
                question = result.get('question', '')
                options = result.get('options', {})
                explanation = result.get('explanation', '')
                
                print(f"   📝 QUESTION ({len(question)} chars):")
                print(f"      {question}")
                print(f"   📋 OPTIONS:")
                if isinstance(options, dict):
                    for key, value in options.items():
                        print(f"      {key}) {value}")
                else:
                    for j, opt in enumerate(options):
                        print(f"      {chr(65+j)}) {opt}")
                
                if explanation:
                    print(f"   💡 EXPLANATION ({len(explanation)} chars):")
                    print(f"      {explanation[:150]}{'...' if len(explanation) > 150 else ''}")
                
                # Quick PhD-level assessment
                is_advanced = assess_phd_quality(question, options, explanation)
                
                print(f"   ⏱️ TIME: {duration:.1f}s")
                
                if is_advanced:
                    print(f"   ✅ SUCCESS - This appears to be PhD-level!")
                    success_count += 1
                else:
                    print(f"   ❌ STILL BASIC - Not PhD-level despite enhancements")
            else:
                print(f"   ❌ GENERATION FAILED - No result after {duration:.1f}s")
        
        success_rate = (success_count / len(test_topics)) * 100
        
        print("\n" + "=" * 60)
        print("🏆 ENHANCED DEEPSEEK DIRECT TEST RESULTS")
        print("=" * 60)
        
        print(f"📊 Performance:")
        print(f"   PhD-Level Success: {success_count}/{len(test_topics)} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print(f"\n🎉 EXCELLENT: Enhanced pipeline working!")
            return True
        elif success_rate >= 60:
            print(f"\n👏 GOOD: Enhanced pipeline showing improvement!")
            return True
        elif success_rate >= 40:
            print(f"\n📊 PROGRESS: Some improvement, needs more work")
            return False
        else:
            print(f"\n❌ FAILED: Enhanced pipeline not working")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def assess_phd_quality(question, options, explanation):
    """Quick assessment of PhD-level quality"""
    if not question or len(question) < 100:
        return False
    
    question_lower = question.lower()
    
    # Check for advanced indicators
    advanced_terms = [
        'mechanism', 'theoretical', 'framework', 'methodology', 'analysis',
        'synthesis', 'optimization', 'algorithm', 'parameter', 'coefficient'
    ]
    
    advanced_count = sum(1 for term in advanced_terms if term in question_lower)
    
    # Check for basic patterns (should be avoided)
    basic_patterns = ['what is', 'define', 'which of the following is']
    has_basic_patterns = any(pattern in question_lower for pattern in basic_patterns)
    
    # Check options quality
    if isinstance(options, dict):
        option_values = list(options.values())
    else:
        option_values = options
    
    substantial_options = sum(1 for opt in option_values if len(str(opt).strip()) >= 20)
    
    # Check explanation depth
    explanation_adequate = len(explanation) >= 100 if explanation else False
    
    # Overall assessment
    return (advanced_count >= 2 and 
            not has_basic_patterns and 
            substantial_options >= 3 and 
            explanation_adequate)

async def main():
    """Main test runner"""
    try:
        success = await test_enhanced_deepseek_direct()
        return 0 if success else 1
    except Exception as e:
        print(f"❌ Test crashed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
