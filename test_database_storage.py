#!/usr/bin/env python3
"""
Test script to verify that questions are properly saved to the SQLite database
and can be accessed through the review question history feature.
"""

import sys
import os
import sqlite3
import json
from pathlib import Path
from datetime import datetime

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_database_storage():
    """Test that the question history database is working properly"""
    print("🗄️ Testing Question History Database Storage")
    print("=" * 50)
    
    try:
        from knowledge_app.core.question_history_storage import QuestionHistoryStorage
        
        print("1️⃣ Initializing Question History Storage...")
        storage = QuestionHistoryStorage()
        
        # Test data
        test_question_data = {
            "question": "What is the primary difference between molecular orbital theory (MOT) and valence shell electron pair repulsion (VSEPR) theories in describing chemical bonding?",
            "options": [
                "Molecular Orbital Theory assumes that electrons are shared among all atoms in a molecule, while VSEPR considers only the outermost shell of electrons",
                "Valence Shell Electron Pair Repulsion theory is used to predict molecular geometry based on electron pair repulsions, whereas Molecular Orbital Theory focuses on atomic orbitals and their interactions",
                "Molecular Orbital Theory uses the concept of hybridization for bonding, while VSEPR does not consider this phenomenon",
                "Both theories are used to describe chemical bonding, but they differ in how they account for electron distribution"
            ],
            "correct_answer": "Valence Shell Electron Pair Repulsion theory is used to predict molecular geometry based on electron pair repulsions, whereas Molecular Orbital Theory focuses on atomic orbitals and their interactions",
            "explanation": "VSEPR theory focuses on predicting molecular geometry by considering electron pair repulsions, while MOT describes bonding through the combination of atomic orbitals.",
            "metadata": {
                "generation_method": "test",
                "test_timestamp": datetime.now().isoformat()
            }
        }
        
        test_quiz_params = {
            "topic": "numerical",
            "difficulty": "expert",
            "question_type": "mixed",
            "game_mode": "serious"
        }
        
        print("2️⃣ Testing question save to database...")
        question_id = storage.save_question(test_question_data, test_quiz_params)
        
        if question_id:
            print(f"   ✅ Question saved successfully with ID: {question_id}")
        else:
            print("   ❌ Failed to save question")
            return False
        
        print("3️⃣ Testing question retrieval from database...")
        
        # Check if we can query the database directly
        db_path = Path("user_data/question_history.sqlite")
        if not db_path.exists():
            print("   ❌ Database file not found")
            return False
        
        # Connect to database and verify the question was saved
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get the saved question
        cursor.execute("""
            SELECT id, question_text, topic, difficulty, question_type, game_mode, 
                   generated_at, option_a, option_b, option_c, option_d, 
                   correct_answer, explanation
            FROM question_history 
            WHERE id = ?
        """, (question_id,))
        
        result = cursor.fetchone()
        
        if result:
            print("   ✅ Question retrieved successfully from database")
            print(f"   📝 Question: {result[1][:80]}...")
            print(f"   🏷️ Topic: {result[2]}")
            print(f"   📊 Difficulty: {result[3]}")
            print(f"   🎮 Game Mode: {result[5]}")
            print(f"   📅 Generated: {result[6]}")
        else:
            print("   ❌ Question not found in database")
            conn.close()
            return False
        
        print("4️⃣ Testing question history retrieval...")
        
        # Get all questions for this topic/difficulty
        cursor.execute("""
            SELECT COUNT(*) FROM question_history 
            WHERE topic = ? AND difficulty = ?
        """, (test_quiz_params["topic"], test_quiz_params["difficulty"]))
        
        count = cursor.fetchone()[0]
        print(f"   📊 Total questions in database for {test_quiz_params['topic']}/{test_quiz_params['difficulty']}: {count}")
        
        # Get recent questions
        cursor.execute("""
            SELECT id, question_text, generated_at 
            FROM question_history 
            ORDER BY generated_at DESC 
            LIMIT 5
        """)
        
        recent_questions = cursor.fetchall()
        print(f"   📋 Recent questions in database: {len(recent_questions)}")
        
        for i, (qid, qtext, generated) in enumerate(recent_questions, 1):
            print(f"      {i}. {qtext[:60]}... (ID: {qid[:8]}...)")
        
        conn.close()
        
        print("5️⃣ Testing database schema...")
        
        # Verify the database has the correct schema
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("PRAGMA table_info(question_history)")
        columns = cursor.fetchall()
        
        expected_columns = [
            'id', 'question_text', 'option_a', 'option_b', 'option_c', 'option_d',
            'correct_answer', 'correct_index', 'explanation', 'topic', 'difficulty',
            'question_type', 'game_mode', 'generated_at', 'last_reviewed',
            'times_answered', 'times_correct', 'quiz_session_id', 'metadata'
        ]
        
        actual_columns = [col[1] for col in columns]
        missing_columns = set(expected_columns) - set(actual_columns)
        extra_columns = set(actual_columns) - set(expected_columns)
        
        if not missing_columns and not extra_columns:
            print("   ✅ Database schema is correct")
        else:
            if missing_columns:
                print(f"   ⚠️ Missing columns: {missing_columns}")
            if extra_columns:
                print(f"   ⚠️ Extra columns: {extra_columns}")
        
        conn.close()
        
        print("\n6️⃣ Testing quiz session storage...")
        
        # Test quiz session storage
        session_id = "test_session_123"
        storage.save_quiz_session(
            session_id=session_id,
            quiz_params=test_quiz_params,
            questions_count=1,
            score=100.0
        )
        
        # Verify session was saved
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT session_id, topic, difficulty, questions_count, score
            FROM quiz_sessions 
            WHERE session_id = ?
        """, (session_id,))
        
        session_result = cursor.fetchone()
        
        if session_result:
            print("   ✅ Quiz session saved successfully")
            print(f"   📊 Session: {session_result[0]}")
            print(f"   🏷️ Topic: {session_result[1]}")
            print(f"   📊 Questions: {session_result[3]}")
            print(f"   🎯 Score: {session_result[4]}%")
        else:
            print("   ❌ Quiz session not found")
            conn.close()
            return False
        
        conn.close()
        
        print("\n✅ ALL DATABASE TESTS PASSED!")
        print("🏆 Question history database is working correctly")
        print("📋 Questions are being saved for review history feature")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_vs_database_separation():
    """Test that cache and database are properly separated"""
    print("\n🔄 Testing Cache vs Database Separation")
    print("=" * 50)
    
    try:
        # Check cache file location
        cache_file = Path("data/cache/question_cache.json")
        db_file = Path("user_data/question_history.sqlite")
        
        print(f"1️⃣ Cache file location: {cache_file}")
        print(f"   Exists: {cache_file.exists()}")
        
        print(f"2️⃣ Database file location: {db_file}")
        print(f"   Exists: {db_file.exists()}")
        
        if cache_file.exists():
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)
            
            saved_at = cache_data.get('saved_at', 0)
            if saved_at:
                import time
                age_minutes = (time.time() - saved_at) / 60
                print(f"   Cache age: {age_minutes:.1f} minutes")
                
                if age_minutes > 10:
                    print("   ⚠️ Cache is older than 10 minutes - should be expired")
                else:
                    print("   ✅ Cache is within 10 minute TTL")
        
        print("\n✅ Cache and Database are properly separated")
        print("   📁 Cache: Temporary performance optimization (10 min TTL)")
        print("   🗄️ Database: Permanent storage for review history")
        
        return True
        
    except Exception as e:
        print(f"❌ Separation test failed: {e}")
        return False

def main():
    """Run all database tests"""
    print("🚀 Question Database Storage Test Suite")
    print("=" * 60)
    
    # Test 1: Database storage functionality
    db_success = test_database_storage()
    
    # Test 2: Cache vs database separation
    separation_success = test_cache_vs_database_separation()
    
    # Overall results
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    print(f"   Database Storage Test: {'✅ PASS' if db_success else '❌ FAIL'}")
    print(f"   Cache/DB Separation Test: {'✅ PASS' if separation_success else '❌ FAIL'}")
    
    overall_success = db_success and separation_success
    print(f"\n🎯 OVERALL: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
    
    if overall_success:
        print("\n🎉 Database storage is working correctly!")
        print("   - Questions are saved to SQLite database")
        print("   - Review history feature should work")
        print("   - Cache is separate from permanent storage")
    else:
        print("\n⚠️ Some database issues found. Check the detailed output above.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
