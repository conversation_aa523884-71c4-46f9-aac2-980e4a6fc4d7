"""
Cloud Inference module for Knowledge App
"""

from .async_converter import async_time_sleep


import os
import logging
import asyncio
import hashlib
import json
import time
from typing import Optional, List, Dict, Any, Tuple
from functools import lru_cache
from datetime import datetime, timedelta
import threading
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
import backoff
import groq
from ..config import AppConfig
from .memory_consolidation import get_consolidated_resource_manager as MemoryManager

# Import local inference capabilities
try:
    from .local_question_generator import LocalQuestionGenerator, get_local_question_generator

    LOCAL_INFERENCE_AVAILABLE = True
except ImportError:
    LOCAL_INFERENCE_AVAILABLE = False
    # Note: logger not available yet, will log later

logger = logging.getLogger(__name__)


class GroqError(Exception):
    """Base exception for Groq API errors"""

    pass


class GroqTimeout(GroqError):
    """Timeout error for Groq API"""

    pass


class ResponseCache:
    """Cache for inference responses with TTL"""

    def __init__(self, ttl_seconds: int = 3600):
        self._cache: Dict[str, Tuple[Any, datetime]] = {}
        self._ttl = timedelta(seconds=ttl_seconds)
        self._lock = threading.Lock()

    def get(self, key: str) -> Optional[Any]:
        """Get item from cache if not expired"""
        with self._lock:
            if key in self._cache:
                item, timestamp = self._cache[key]
                if datetime.now() - timestamp < self._ttl:
                    return item
                else:
                    del self._cache[key]
            return None

    def set(self, key: str, value: Any) -> None:
        """Set item in cache with current timestamp"""
        with self._lock:
            self._cache[key] = (value, datetime.now())

    def clear(self) -> None:
        """Clear expired items from cache"""
        with self._lock:
            now = datetime.now()
            expired = [k for k, (_, ts) in self._cache.items() if now - ts >= self._ttl]
            for k in expired:
                del self._cache[k]


class CloudInference:
    """Handles cloud-based and local inference"""

    def __init__(self, config: AppConfig):
        self.config = config
        self.client = None
        self.initialize_client()
        self.model = "mixtral-8x7b-32768"
        self.memory_manager = MemoryManager()

        # Initialize local inference (lazy loading - only when needed)
        self.local_generator = None
        self.use_local_inference = getattr(config, "use_local_inference", True)
        self.local_model_path = getattr(config, "local_model_path", None)
        self.local_base_model = getattr(config, "local_base_model", None)
        self._local_inference_initialized = False

        # Initialize caches
        self._response_cache = ResponseCache(ttl_seconds=3600)  # 1 hour TTL
        self._quiz_cache = ResponseCache(ttl_seconds=7200)  # 2 hour TTL

        # Initialize thread pool
        self._thread_pool = ThreadPoolExecutor(
            max_workers=min(4, (ThreadPoolExecutor()._max_workers))
        )

        # Setup periodic cache cleanup
        self._setup_cache_cleanup()

        # Don't initialize local inference during startup - only when needed
        logger.info("🏠 Local inference ready for lazy initialization...")

    def initialize_client(self) -> None:
        """Initialize the Groq client"""
        try:
            # Try to get API key from config, environment, or use a default
            api_key = (
                getattr(self.config, "cloud_inference_api_key", None)
                or os.environ.get("GROQ_API_KEY", "")
                or "default_key_placeholder"
            )

            # If no API key is available, log a warning and skip client initialization
            if not api_key or api_key == "default_key_placeholder":
                logger.warning("No Groq API key found. Cloud inference will be disabled.")
                self.client = None
                return

            self.client = groq.Client(api_key=api_key)
            logger.info("Groq client initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Groq client: {e}")
            self.client = None

    def _initialize_local_inference(self) -> None:
        """Initialize local inference if available"""
        if not LOCAL_INFERENCE_AVAILABLE:
            logger.info("Local inference not available")
            return

        if not self.use_local_inference:
            logger.info("Local inference disabled in config")
            return

        try:
            logger.info("🔄 Initializing local inference...")
            self.local_generator = get_local_question_generator(
                self.local_model_path, self.local_base_model
            )

            # Try to initialize with available models
            available_models = self.local_generator.find_available_models()
            if available_models:
                logger.info(f"Found {len(available_models)} available local models")

                # Use the first available model if no specific path provided
                if not self.local_model_path and available_models:
                    best_model = available_models[0]
                    logger.info(f"Using model: {best_model['name']} ({best_model['type']})")
                    self.local_generator.model_path = best_model["path"]
                    if best_model["type"] == "LoRA Adapter":
                        self.local_generator.base_model = best_model["base_model"]

                # Initialize the generator
                if self.local_generator.initialize():
                    logger.info("✅ Local inference initialized successfully!")
                else:
                    logger.warning("❌ Failed to initialize local inference")
                    self.local_generator = None
            else:
                logger.info("No trained models found. Local inference will use base model.")
                # Try to initialize with base model only
                if self.local_generator.initialize():
                    logger.info("✅ Local inference initialized with base model!")
                else:
                    logger.warning("❌ Failed to initialize local inference with base model")
                    self.local_generator = None

        except Exception as e:
            logger.error(f"Error initializing local inference: {e}")
            self.local_generator = None

    def _setup_cache_cleanup(self) -> None:
        """Setup periodic cache cleanup"""

        async def cleanup():
            while True:
                try:
                    self._response_cache.clear()
                    self._quiz_cache.clear()
                    self.memory_manager.collect_garbage()
                except Exception as e:
                    logger.warning(f"Error during cache cleanup: {e}")
                finally:
                    # 🚀 CRITICAL FIX: Use async sleep to prevent UI blocking
                    await async_time_sleep(300)  # Sleep for 5 minutes without blocking

        # Run async cleanup in a separate thread with event loop
        def run_async_cleanup():
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(cleanup())
            finally:
                loop.close()

        thread = threading.Thread(target=run_async_cleanup, daemon=True)
        thread.start()

    def _get_cache_key(self, prompt: str, **kwargs) -> str:
        """Generate cache key from prompt and parameters"""
        key_data = {"prompt": prompt, **kwargs}
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_str.encode()).hexdigest()

    @backoff.on_exception(backoff.expo, (GroqError, Exception), max_tries=3)
    async def generate_quiz_async(self, context: str, difficulty: str = "medium") -> Dict[str, Any]:
        """Generate a quiz question asynchronously using MCQ manager"""

        try:
            # Use the unified MCQ manager
            from .mcq_manager import get_mcq_manager

            mcq_manager = get_mcq_manager(self.config)

            # Generate using current mode (online/offline)
            result = await mcq_manager.generate_quiz_async(context, difficulty)

            if result and result.get("question"):
                logger.info("✅ MCQ generation successful")
                return result
            else:
                logger.error("❌ MCQ generation returned empty result - no fallback available")
                raise Exception(f"MCQ generation returned empty result for context: {context[:50]}...")

        except Exception as e:
            logger.error(f"❌ MCQ generation failed: {e} - no fallback available")
            raise Exception(f"MCQ generation failed: {str(e)}")

        # Check cloud inference availability
        if not self.client:
            logger.error("❌ No cloud or local inference available - no fallback available")
            raise Exception(f"No cloud or local inference available for context: {context[:50]}...")

        try:
            logger.info("☁️ Using cloud inference for quiz generation")

            # Create prompt
            prompt = f"""
            Based on the following context, generate a multiple-choice quiz question.
            The difficulty level should be {difficulty}.

            Context:
            {context}

            Generate a question with 4 options (A, B, C, D), one correct answer, and an explanation.
            Format the response as a JSON object with the following structure:
            {{
                "question": "The question text",
                "options": {{
                    "A": "First option",
                    "B": "Second option",
                    "C": "Third option",
                    "D": "Fourth option"
                }},
                "correct": "The correct option letter (A, B, C, or D)",
                "explanation": "Explanation of why the answer is correct"
            }}
            """

            # Call Groq API
            response = await self.client.chat.completions.create(
                model="mixtral-8x7b-32768",
                messages=[
                    {"role": "system", "content": "You are a helpful quiz generator."},
                    {"role": "user", "content": prompt},
                ],
                temperature=0.7,
                max_tokens=1000,
                response_format={"type": "json_object"},
            )

            # Parse response
            result = json.loads(response.choices[0].message.content)

            # Validate response format
            required_fields = ["question", "options", "correct", "explanation"]
            if not all(field in result for field in required_fields):
                raise ValueError("Invalid response format from API")

            if not isinstance(result["options"], dict):
                raise ValueError("Invalid options format in API response")

            if not all(opt in result["options"] for opt in "ABCD"):
                raise ValueError("Missing options in API response")

            if result["correct"] not in "ABCD":
                raise ValueError("Invalid correct answer in API response")

            logger.info("✅ Cloud quiz generation successful")
            return result

        except Exception as e:
            logger.error(f"❌ Error generating quiz with cloud inference: {e}")
            raise Exception(f"Cloud inference failed: {str(e)}")

    def generate_quiz(self, context: str, difficulty: str = "medium") -> Dict[str, Any]:
        """Synchronous wrapper for generate_quiz_async"""
        # 🚨 CRITICAL FIX: Non-blocking async execution to prevent UI freeze
        import concurrent.futures
        
        def run_async_in_thread():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(
                    asyncio.wait_for(
                        self.generate_quiz_async(context, difficulty),
                        timeout=15.0  # Aggressive timeout
                    )
                )
            except asyncio.TimeoutError:
                logger.error("❌ Inference generation timed out")
                return None
            except Exception as e:
                logger.error(f"❌ Inference generation failed: {e}")
                return None
            finally:
                try:
                    loop.close()
                except:
                    pass
        
        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(run_async_in_thread)
                # 🚀 CONVERTED: Use async pattern instead of blocking .result()
                return future# 🚀 CONVERTED: Use async pattern instead of blocking .result()  # Total timeout with overhead
        except concurrent.futures.TimeoutError:
            logger.error("❌ Inference executor timed out")
            return None
        except Exception as e:
            logger.error(f"❌ Inference execution failed: {e}")
            return None

    def cleanup(self) -> None:
        """Clean up resources"""
        try:
            # Clear caches
            self._response_cache.clear()
            self._quiz_cache.clear()

            # Shutdown thread pool
            self._thread_pool.shutdown(wait=True)

            # Clean up memory
            self.memory_manager.collect_garbage()

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
        finally:
            # Ensure thread pool is shut down
            if hasattr(self, "_thread_pool"):
                try:
                    self._thread_pool.shutdown(wait=False)
                except:
                    pass

    def generate_quiz_question(
        self, category: str = None, difficulty: str = "medium", topic: str = None
    ) -> Dict[str, Any]:
        """
        Generate a quiz question using local or cloud inference

        Args:
            category: Optional category for the question
            difficulty: Difficulty level of the question
            topic: Optional specific topic for the question

        Returns:
            Dictionary containing question details
        """
        # 🚀 CRITICAL FIX: Include topic in cache key to prevent question repetition
        # This ensures different topics generate different questions
        cache_key = f"{category}_{difficulty}_{topic or 'general'}"

        # 🚀 CRITICAL FIX: Disable caching for expert mode to ensure fresh questions
        use_cache = difficulty != "expert"

        if use_cache:
            cached_question = self._quiz_cache.get(cache_key)
            if cached_question:
                logger.info(f"📋 Using cached question for {cache_key}")
                return cached_question
        else:
            logger.info(f"🚫 Cache disabled for expert mode - generating fresh question for topic: {topic}")
            # Clear any existing cache to ensure freshness for expert mode
            self._quiz_cache.clear()

        # 🚀 CRITICAL FIX: Clear cache for different topics to prevent repetition
        import time
        current_time = time.time()
        if not hasattr(self, '_last_topic_time'):
            self._last_topic_time = {}
        if not hasattr(self, '_last_topic'):
            self._last_topic = None

        topic_key = topic or category or 'general'

        # If topic changed, clear cache immediately for variety
        if self._last_topic and self._last_topic != topic_key:
            logger.info(f"🔄 Topic changed from '{self._last_topic}' to '{topic_key}' - clearing cache for variety")
            self._quiz_cache.clear()
            self._response_cache.clear()

        # Also clear if same topic requested too quickly (prevents spam)
        if topic_key in self._last_topic_time:
            time_since_last = current_time - self._last_topic_time[topic_key]
            if time_since_last < 5:  # If same topic within 5 seconds, clear cache
                logger.info(f"🔄 Same topic '{topic_key}' requested within 5s - clearing cache for variety")
                self._quiz_cache.clear()
                self._response_cache.clear()

        # Update tracking
        self._last_topic = topic_key
        self._last_topic_time[topic_key] = current_time

        self._last_topic_time[topic_key] = current_time

        # Try local inference first if available and initialized
        if self.use_local_inference and not self._local_inference_initialized:
            # Initialize local inference on demand
            self._initialize_local_inference()
            self._local_inference_initialized = True

        if self.local_generator and self.local_generator.is_initialized:
            try:
                logger.info("🧠 Using local model for quiz question generation")
                question_data = self.local_generator.generate_quiz_question(category, difficulty)
                if question_data and question_data.get("question"):
                    # Cache the question only if caching is enabled
                    if use_cache:
                        self._quiz_cache.set(cache_key, question_data)
                        logger.info("✅ Local quiz question generation successful (cached)")
                    else:
                        logger.info("✅ Local quiz question generation successful (not cached - expert mode)")
                    return question_data
                else:
                    logger.warning("Local quiz question generation returned empty result")
            except Exception as e:
                logger.warning(f"Local quiz question generation failed: {e}")

        # Fall back to cloud inference
        if not self.client:
            logger.warning("No cloud or local inference available. Using REAL topic-specific fallback.")
            return self._generate_fallback_question(category, difficulty)

        # Cloud inference logic
        try:
            logger.info("☁️ Using cloud inference for quiz question generation")

            # Generate question via cloud inference
            prompt = self._build_quiz_prompt(category, difficulty, topic)
            response = self.client.chat.completions.create(
                model=self.model, messages=[{"role": "user", "content": prompt}]
            )

            # Parse and validate response
            question_data = self._parse_quiz_response(response.choices[0].message.content)

            # Cache the question only if caching is enabled
            if use_cache:
                self._quiz_cache.set(cache_key, question_data)
                logger.info("✅ Cloud quiz question generation successful (cached)")
            else:
                logger.info("✅ Cloud quiz question generation successful (not cached - expert mode)")

            return question_data

        except Exception as e:
            logger.error(f"Cloud inference error: {e}")
            return self._generate_fallback_question(category, difficulty)



    def _build_quiz_prompt(self, category: str = None, difficulty: str = "medium", topic: str = None) -> str:
        """Build prompt for quiz generation"""
        prompt = "Generate a multiple-choice quiz question"

        # Use topic if provided, otherwise fall back to category
        subject = topic or category
        if subject:
            prompt += f" about {subject}"

        prompt += f" with {difficulty} difficulty level."
        prompt += """
        Format the response as a JSON object with the following structure:
        {
            "question": "The question text",
            "options": ["Option A", "Option B", "Option C", "Option D"],
            "correct_answer": "The correct option (must be one of the options)",
            "explanation": "Explanation of why the answer is correct",
            "category": "Subject category",
            "difficulty": "Difficulty level"
        }
        """
        return prompt

    def _parse_quiz_response(self, response_text: str) -> Dict[str, Any]:
        """Parse and validate quiz response"""
        try:
            # Parse JSON response
            data = json.loads(response_text)

            # Validate required fields
            required_fields = ["question", "options", "correct_answer", "explanation"]
            if not all(field in data for field in required_fields):
                raise ValueError("Missing required fields in response")

            # Validate options
            if not isinstance(data["options"], list) or len(data["options"]) != 4:
                raise ValueError("Options must be a list of 4 items")

            # Validate correct answer
            if data["correct_answer"] not in data["options"]:
                raise ValueError("Correct answer must be one of the options")

            return data

        except json.JSONDecodeError:
            logger.error("Failed to parse JSON response")
            return self._generate_fallback_question()
        except Exception as e:
            logger.error(f"Error parsing quiz response: {e}")
            return self._generate_fallback_question()

    def get_available_local_models(self) -> List[Dict[str, str]]:
        """Get list of available local models"""
        if not LOCAL_INFERENCE_AVAILABLE:
            return []

        try:
            if not self.local_generator:
                temp_generator = LocalQuestionGenerator()
                return temp_generator.find_available_models()
            else:
                return self.local_generator.find_available_models()
        except Exception as e:
            logger.error(f"Error getting available local models: {e}")
            return []

    def switch_local_model(self, model_path: str, base_model: str = None) -> bool:
        """Switch to a different local model"""
        if not LOCAL_INFERENCE_AVAILABLE:
            logger.warning("Local inference not available")
            return False

        try:
            if not self.local_generator:
                self.local_generator = get_local_question_generator(model_path, base_model)
                return self.local_generator.initialize()
            else:
                return self.local_generator.switch_model(model_path, base_model)
        except Exception as e:
            logger.error(f"Error switching local model: {e}")
            return False

    def get_local_model_info(self) -> Dict[str, Any]:
        """Get information about the current local model"""
        if not self.local_generator:
            return {"status": "not_available"}

        return self.local_generator.get_model_info()

    def set_inference_mode(self, use_local: bool = True):
        """Set whether to prefer local or cloud inference"""
        self.use_local_inference = use_local
        logger.info(f"Inference mode set to: {'local' if use_local else 'cloud'}")

    def is_local_inference_available(self) -> bool:
        """Check if local inference is available and working"""
        return (
            LOCAL_INFERENCE_AVAILABLE
            and self.local_generator is not None
            and self.local_generator.is_initialized
        )

    def __del__(self):
        """Cleanup when object is destroyed"""
        try:
            if hasattr(self, "_thread_pool"):
                self._thread_pool.shutdown(wait=False)
            if hasattr(self, "local_generator") and self.local_generator:
                self.local_generator.cleanup()
        except Exception:
            pass