#!/usr/bin/env python3
"""
Debug the failing cases to see what the model is generating
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging to see what's happening
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

def debug_failing_case(topic):
    """Debug a specific failing case"""
    print(f"🔍 Debugging failing case: '{topic}'")
    
    try:
        from knowledge_app.core.intelligent_prompt_generator import get_intelligent_prompt_generator
        import requests
        
        # Get the intelligent prompt
        prompt_generator = get_intelligent_prompt_generator()
        result = prompt_generator.generate_intelligent_prompt(
            raw_input=topic,
            difficulty="medium",
            question_type="mixed"
        )
        
        prompt = result.get('prompt', '')
        print(f"📝 Intelligent prompt for '{topic}':")
        print("-" * 50)
        print(prompt)
        print("-" * 50)
        
        # Test with Ollama directly
        payload = {
            "model": "mathstral:latest",
            "prompt": prompt,
            "format": "json",
            "stream": False,
            "options": {
                "temperature": 0.4,
                "num_predict": 1800
            }
        }
        
        print(f"🚀 Sending to Ollama...")
        response = requests.post("http://localhost:11434/api/generate", json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            raw_response = result.get('response', '')
            
            print(f"📄 Raw response from Ollama:")
            print("-" * 50)
            print(raw_response)
            print("-" * 50)
            
            # Try to parse it
            try:
                parsed = json.loads(raw_response)
                print(f"✅ JSON parsing successful!")
                print(f"📋 Parsed data: {parsed}")
                
                # Test our validation
                from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator
                generator = OllamaJSONGenerator()
                
                is_valid = generator._is_valid_question(parsed)
                print(f"🔍 Our validation result: {'✅' if is_valid else '❌'}")
                
                if is_valid:
                    normalized = generator._normalize_question(parsed)
                    print(f"🔧 Normalized: {normalized}")
                else:
                    print(f"❌ Validation failed - checking why...")
                    print(f"   Has question: {'question' in parsed}")
                    print(f"   Has options: {'options' in parsed}")
                    print(f"   Has correct field: {'correct_answer' in parsed or 'correct' in parsed}")
                    if 'options' in parsed:
                        print(f"   Options count: {len(parsed.get('options', []))}")
                        print(f"   Options type: {type(parsed.get('options', []))}")
                    if 'question' in parsed:
                        print(f"   Question ends with ?: {parsed['question'].strip().endswith('?')}")
                
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                
                # Try to extract JSON manually
                import re
                json_match = re.search(r'\{.*\}', raw_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    print(f"🔍 Extracted JSON: {json_str}")
                    try:
                        parsed = json.loads(json_str)
                        print(f"✅ Extracted JSON parsing successful!")
                        print(f"📋 Parsed data: {parsed}")
                        return True
                    except:
                        print(f"❌ Extracted JSON also failed")
                
                return False
        else:
            print(f"❌ Ollama request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🧠 Debug Failing Cases")
    print("=" * 60)
    
    # Test the failing cases from our previous test
    failing_cases = [
        "dfs",
        "magnetism", 
        "chemistry",
        "computer science"
    ]
    
    for case in failing_cases:
        print(f"\n{'='*60}")
        success = debug_failing_case(case)
        print(f"Result for '{case}': {'✅' if success else '❌'}")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
