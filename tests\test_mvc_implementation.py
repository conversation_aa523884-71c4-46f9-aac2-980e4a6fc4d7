"""
Pytest tests for MVC implementation

This module contains pytest-compatible tests for the Model-View-Controller
implementation of the quiz system.
"""

import pytest
import sys
import os
from pathlib import Path
from unittest.mock import Mock, MagicMock

# Add src to path
project_root = Path(__file__).parent.parent
src_dir = project_root / "src"
if src_dir.exists():
    sys.path.insert(0, str(src_dir))

@pytest.mark.mvc
@pytest.mark.unit
class TestQuizController:
    """Test suite for QuizController"""
    
    def test_controller_initialization(self):
        """Test QuizController initialization"""
        from knowledge_app.ui.controllers.quiz_controller import QuizController, QuizState
        
        # Test controller creation
        controller = QuizController()
        assert controller is not None, "Controller creation failed"
        
        # Test state initialization
        assert hasattr(controller, 'state'), "Controller missing state"
        assert isinstance(controller.state, QuizState), "Invalid state type"
        
        # Test initial state values
        assert controller.state.current_question is None, "Initial question should be None"
        assert controller.state.selected_option_index == -1, "Initial selection should be -1"
        assert not controller.state.is_answered, "Initial answered state should be False"
        assert not controller.state.timer_active, "Initial timer should be inactive"
    
    def test_controller_state_management(self):
        """Test controller state management"""
        from knowledge_app.ui.controllers.quiz_controller import QuizController
        
        controller = QuizController()
        
        # Test state getter
        initial_state = controller.get_current_state()
        assert isinstance(initial_state, dict), "State should be a dictionary"
        
        required_keys = [
            'has_question', 'selected_option', 'is_answered', 'is_correct',
            'remaining_time', 'timer_active', 'quiz_mode', 'question_count',
            'correct_answers', 'can_submit'
        ]
        
        for key in required_keys:
            assert key in initial_state, f"Missing state key: {key}"
        
        # Test option selection
        controller.select_option(2)
        assert controller.state.selected_option_index == 2, "Option selection failed"
        
        updated_state = controller.get_current_state()
        assert updated_state['selected_option'] == 2, "State not updated correctly"
    
    def test_answer_validation(self):
        """Test answer validation logic"""
        from knowledge_app.ui.controllers.quiz_controller import QuizController
        
        controller = QuizController()
        
        # Mock question data
        mock_question = {
            'question': 'Test question?',
            'options': ['Option A', 'Option B', 'Option C', 'Option D'],
            'correct_option_letter': 'B',
            'explanation': 'Test explanation'
        }
        
        controller.state.current_question = mock_question
        
        # Test correct answer
        controller.state.selected_option_index = 1  # B = index 1
        is_correct = controller._check_answer()
        assert is_correct, "Should recognize correct answer"
        
        # Test incorrect answer
        controller.state.selected_option_index = 0  # A = index 0
        is_correct = controller._check_answer()
        assert not is_correct, "Should recognize incorrect answer"
        
        # Test feedback generation
        correct_feedback = controller._generate_feedback(True)
        assert "Correct" in correct_feedback, "Correct feedback should contain 'Correct'"
        
        incorrect_feedback = controller._generate_feedback(False)
        assert "Incorrect" in incorrect_feedback, "Incorrect feedback should contain 'Incorrect'"

@pytest.mark.mvc
@pytest.mark.ui
class TestQuizScreenMVC:
    """Test suite for QuizScreenMVC"""
    
    def test_mvc_architecture(self):
        """Test overall MVC architecture compliance"""
        from knowledge_app.ui.controllers.quiz_controller import QuizController
        
        # Test that controller is independent of view
        controller = QuizController()
        state = controller.get_current_state()
        assert isinstance(state, dict), "Controller should work independently"
    
    def test_controller_signals(self):
        """Test controller signal definitions"""
        from knowledge_app.ui.controllers.quiz_controller import QuizController
        from PyQt5.QtWidgets import QApplication
        
        # Ensure QApplication exists
        if not QApplication.instance():
            app = QApplication([])
        
        controller = QuizController()
        
        # Test signal existence
        required_signals = [
            'question_loaded', 'answer_checked', 'timer_updated',
            'timer_expired', 'quiz_completed', 'state_changed'
        ]
        
        for signal_name in required_signals:
            assert hasattr(controller, signal_name), f"Missing signal: {signal_name}"
            signal = getattr(controller, signal_name)
            assert hasattr(signal, 'emit'), f"Invalid signal: {signal_name}"

@pytest.mark.styling
@pytest.mark.enterprise
@pytest.mark.unit
class TestMainWindowStyling:
    """Test suite for main window styling migration"""
    
    def test_enterprise_imports(self):
        """Test that enterprise styling imports are present and working"""
        # Test enterprise style manager import
        from knowledge_app.ui.enterprise_style_manager import get_style_manager
        style_manager = get_style_manager()
        assert style_manager is not None, "Style manager not available"
        
        # Test enterprise design system import
        from knowledge_app.ui.enterprise_design_system import get_design_system, Theme
        design_system = get_design_system()
        assert design_system is not None, "Design system not available"
        
        # Test that Theme enum is available
        assert hasattr(Theme, 'DARK'), "Theme.DARK not available"
        assert hasattr(Theme, 'LIGHT'), "Theme.LIGHT not available"
    
    def test_theme_switching(self):
        """Test theme switching functionality"""
        from knowledge_app.ui.enterprise_design_system import get_design_system, Theme
        
        design_system = get_design_system()
        
        # Test theme switching
        original_theme = design_system.current_theme
        
        # Switch to dark theme
        design_system.set_theme(Theme.DARK)
        dark_bg = design_system.color('bg_primary')
        
        # Switch to light theme
        design_system.set_theme(Theme.LIGHT)
        light_bg = design_system.color('bg_primary')
        
        # Verify themes are different
        assert dark_bg != light_bg, "Theme colors should be different"
        
        # Restore original theme
        design_system.set_theme(original_theme)
    
    def test_fallback_styling(self):
        """Test fallback styling behavior"""
        from knowledge_app.ui.enterprise_design_system import get_design_system
        
        design_system = get_design_system()
        
        # Test that design system provides fallback colors
        bg_color = design_system.color('bg_primary')
        text_color = design_system.color('text_primary')
        
        assert bg_color is not None, "Background color should be available"
        assert text_color is not None, "Text color should be available"
        assert bg_color.startswith('#'), "Background color should be hex format"
        assert text_color.startswith('#'), "Text color should be hex format"

@pytest.mark.enterprise
@pytest.mark.unit
class TestEnterpriseRefactoring:
    """Test suite for enterprise refactoring validation"""
    
    def test_safe_pyqt_compatibility(self):
        """Test that PyQt compatibility is safe (no monkey patching)"""
        # Test that PyQt compatibility module exists and is safe
        from knowledge_app.utils.pyqt_compat import setup_pyqt_compatibility
        
        # Should not raise any exceptions
        setup_pyqt_compatibility()
        
        # Test that no dangerous file modifications are present
        import knowledge_app.utils.pyqt_compat as compat_module
        import inspect
        
        source = inspect.getsource(compat_module)
        
        # Should not contain file system modifications
        dangerous_patterns = ['open(', 'write(', '__init__.py', 'site-packages']
        for pattern in dangerous_patterns:
            assert pattern not in source, f"Dangerous pattern found: {pattern}"
    
    def test_unified_real7b_config(self):
        """Test that Real7BConfig is unified"""
        from knowledge_app.core.real_7b_config import Real7BConfig
        
        # Test that config class exists and is importable
        config = Real7BConfig(model_name="test_model")
        assert config is not None, "Real7BConfig should be importable"
        
        # Test that it has required attributes
        required_attrs = ['model_name', 'lora_r', 'lora_alpha', 'lora_target_modules']
        for attr in required_attrs:
            assert hasattr(config, attr), f"Missing required attribute: {attr}"
    
    def test_enterprise_di_container(self):
        """Test enterprise dependency injection container"""
        from knowledge_app.core.enterprise_di_container import EnterpriseDIContainer
        
        container = EnterpriseDIContainer()
        assert container is not None, "DI container should be available"
        
        # Test basic functionality
        assert hasattr(container, 'register_singleton'), "Missing register_singleton method"
        assert hasattr(container, 'resolve'), "Missing resolve method"
        assert hasattr(container, 'is_registered'), "Missing is_registered method"

# Test discovery functions for the old test runners
def test_mvc_quiz_implementation():
    """Compatibility function for old test runner"""
    # Run MVC tests
    pytest.main([__file__ + "::TestQuizController", "-v"])

def test_main_window_styling_migration():
    """Compatibility function for old test runner"""
    # Run styling tests
    pytest.main([__file__ + "::TestMainWindowStyling", "-v"])

def test_enterprise_refactoring():
    """Compatibility function for old test runner"""
    # Run enterprise tests
    pytest.main([__file__ + "::TestEnterpriseRefactoring", "-v"])

if __name__ == "__main__":
    # Run all tests when executed directly
    pytest.main([__file__, "-v"])
