#!/usr/bin/env python3
"""
🚀 Test Optimized Expert Mode Performance
Verifies that all optimizations are working correctly
"""

import sys
import os
import time
import psutil
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_expert_mode_performance():
    """Test expert mode performance with all optimizations"""
    print("🚀 TESTING OPTIMIZED EXPERT MODE PERFORMANCE")
    print("=" * 60)
    
    # Baseline measurements
    baseline_cpu = psutil.cpu_percent(interval=1)
    baseline_memory = psutil.virtual_memory().percent
    
    print(f"Baseline - CPU: {baseline_cpu}%, Memory: {baseline_memory}%")
    
    try:
        from knowledge_app.core.deepseek_integration import get_deepseek_pipeline
        
        # Get the optimized pipeline
        pipeline = get_deepseek_pipeline()
        
        if not pipeline.is_ready():
            print("❌ DeepSeek pipeline not ready")
            return False
        
        print("✅ DeepSeek pipeline ready with optimized models")
        print(f"   Thinking model: {pipeline.thinking_model}")
        print(f"   JSON model: {pipeline.json_model}")
        
        # Test multiple topics to get average performance
        test_topics = [
            ("quantum mechanics", "expert"),
            ("machine learning", "expert"),
            ("thermodynamics", "expert")
        ]
        
        total_time = 0
        successful_generations = 0
        
        for topic, difficulty in test_topics:
            print(f"\n🧪 Testing: {topic} ({difficulty})")
            
            start_time = time.time()
            
            try:
                result = pipeline.generate_expert_question(
                    topic=topic,
                    difficulty=difficulty
                )
                
                end_time = time.time()
                generation_time = end_time - start_time
                total_time += generation_time
                
                if result:
                    successful_generations += 1
                    print(f"✅ Generated in {generation_time:.2f}s")
                    print(f"   Question: {result.get('question', 'N/A')[:80]}...")
                else:
                    print(f"❌ Failed to generate question")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
        
        # Calculate performance metrics
        if successful_generations > 0:
            avg_time = total_time / successful_generations
            print(f"\n📊 PERFORMANCE SUMMARY:")
            print(f"   Successful generations: {successful_generations}/{len(test_topics)}")
            print(f"   Average generation time: {avg_time:.2f}s")
            print(f"   Total time: {total_time:.2f}s")
            
            # Performance analysis
            if avg_time < 30:
                print("🎉 EXCELLENT PERFORMANCE! Expert mode is highly optimized")
            elif avg_time < 60:
                print("✅ GOOD PERFORMANCE! Expert mode is well optimized")
            elif avg_time < 90:
                print("⚠️ MODERATE PERFORMANCE! Some optimization still needed")
            else:
                print("❌ POOR PERFORMANCE! Further optimization required")
                
            return avg_time < 90
        else:
            print("❌ No successful generations")
            return False
            
    except Exception as e:
        print(f"❌ Error testing expert mode: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_utilization():
    """Test system resource utilization during expert mode"""
    print("\n💪 TESTING SYSTEM UTILIZATION")
    print("=" * 50)
    
    try:
        from knowledge_app.core.deepseek_integration import get_deepseek_pipeline
        
        pipeline = get_deepseek_pipeline()
        
        if not pipeline.is_ready():
            print("❌ Pipeline not ready")
            return False
        
        # Monitor resource usage during generation
        print("🔍 Monitoring resource usage during generation...")
        
        # Start monitoring
        start_cpu = psutil.cpu_percent(interval=None)
        start_memory = psutil.virtual_memory().percent
        
        # Generate question while monitoring
        start_time = time.time()
        
        result = pipeline.generate_expert_question(
            topic="artificial intelligence",
            difficulty="expert"
        )
        
        end_time = time.time()
        
        # Get peak usage
        peak_cpu = psutil.cpu_percent(interval=1)
        peak_memory = psutil.virtual_memory().percent
        
        generation_time = end_time - start_time
        
        print(f"\n📈 RESOURCE UTILIZATION ANALYSIS:")
        print(f"   Generation time: {generation_time:.2f}s")
        print(f"   Peak CPU usage: {peak_cpu}%")
        print(f"   Peak memory usage: {peak_memory}%")
        
        # Analyze utilization efficiency
        if peak_cpu > 50:
            print("✅ GOOD CPU UTILIZATION - System is being used effectively")
        elif peak_cpu > 20:
            print("⚠️ MODERATE CPU UTILIZATION - Some optimization possible")
        else:
            print("❌ LOW CPU UTILIZATION - System not fully utilized")
        
        if result:
            print("✅ Question generated successfully")
            return True
        else:
            print("❌ Question generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing utilization: {e}")
        return False

def test_configuration_applied():
    """Test that all configuration optimizations are applied"""
    print("\n⚙️ TESTING CONFIGURATION OPTIMIZATIONS")
    print("=" * 50)
    
    try:
        # Check DeepSeek config
        from knowledge_app.core.deepseek_integration import DeepSeekConfig
        
        config = DeepSeekConfig()
        
        print("🧠 DeepSeek Configuration:")
        print(f"   Thinking model: {config.thinking_model}")
        print(f"   JSON model: {config.json_model}")
        print(f"   GPU layers: {config.gpu_layers}")
        print(f"   Threads: {config.num_threads}")
        print(f"   Parallel: {config.num_parallel}")
        print(f"   Max thinking tokens: {config.max_thinking_tokens}")
        print(f"   Max JSON tokens: {config.max_json_tokens}")
        print(f"   Timeout: {config.timeout}s")
        print(f"   Low VRAM: {config.low_vram}")
        
        # Verify optimized models are being used
        if "-optimized" in config.thinking_model and "-optimized" in config.json_model:
            print("✅ Using optimized models")
        else:
            print("⚠️ Not using optimized models")
        
        # Verify GPU settings
        if config.gpu_layers > 0 and not config.low_vram:
            print("✅ GPU optimization enabled")
        else:
            print("⚠️ GPU optimization not fully enabled")
        
        # Verify thread settings
        if config.num_threads >= 8 and config.num_parallel >= 2:
            print("✅ Multi-threading optimized")
        else:
            print("⚠️ Multi-threading not optimized")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        return False

def main():
    """Main test function"""
    print("🔥 OPTIMIZED EXPERT MODE PERFORMANCE TEST")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Configuration
    if test_configuration_applied():
        success_count += 1
        print("✅ Test 1/3: Configuration optimizations applied")
    else:
        print("❌ Test 1/3: Configuration issues detected")
    
    # Test 2: System utilization
    if test_system_utilization():
        success_count += 1
        print("✅ Test 2/3: System utilization improved")
    else:
        print("❌ Test 2/3: System utilization issues")
    
    # Test 3: Performance
    if test_expert_mode_performance():
        success_count += 1
        print("✅ Test 3/3: Expert mode performance acceptable")
    else:
        print("❌ Test 3/3: Expert mode performance issues")
    
    print(f"\n🎯 OPTIMIZATION TEST RESULTS: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 ALL OPTIMIZATIONS WORKING PERFECTLY!")
        print("   Expert mode should now utilize your system much more effectively")
    elif success_count >= 2:
        print("✅ MOST OPTIMIZATIONS WORKING")
        print("   Expert mode performance should be significantly improved")
    else:
        print("❌ OPTIMIZATION ISSUES DETECTED")
        print("   Expert mode may still have performance problems")
    
    print("\n💡 RECOMMENDATIONS:")
    print("   1. Restart the app to ensure all changes take effect")
    print("   2. Try expert mode in the UI to see the improvements")
    print("   3. Monitor system resources during expert mode usage")

if __name__ == "__main__":
    main()
