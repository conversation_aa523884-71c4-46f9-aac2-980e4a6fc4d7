#!/usr/bin/env python3
"""
🔍 SIMPLE IMAGE ANALYZER
Since I can't read JPG/PNG directly, this creates a simple HTML viewer
to display the screenshots so I can analyze them visually.
"""

import os
from pathlib import Path
import webbrowser
from datetime import datetime

def create_image_analysis_html():
    """Create an HTML file to display all screenshots for analysis"""
    
    screenshots_dir = Path("test_screenshots")
    if not screenshots_dir.exists():
        print("❌ Screenshots directory not found")
        return
    
    # Get all image files
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        image_files.extend(screenshots_dir.glob(ext))
    
    image_files.sort()
    
    if not image_files:
        print("❌ No image files found")
        return
    
    print(f"📸 Found {len(image_files)} screenshots to analyze")
    
    # Create HTML content
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>🔍 Screenshot Analysis - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
                background: #f5f5f5;
            }}
            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
                text-align: center;
            }}
            .screenshot-container {{
                background: white;
                margin: 20px 0;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .screenshot-title {{
                font-size: 18px;
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
                padding: 10px;
                background: #e3f2fd;
                border-radius: 5px;
                border-left: 4px solid #2196F3;
            }}
            .screenshot-image {{
                max-width: 100%;
                border: 2px solid #ddd;
                border-radius: 8px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }}
            .analysis-section {{
                margin-top: 15px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 5px;
                border-left: 4px solid #28a745;
            }}
            .key-screenshot {{
                border-left: 4px solid #dc3545 !important;
                background: #fff5f5 !important;
            }}
            .issue-checklist {{
                margin: 10px 0;
            }}
            .issue-checklist li {{
                margin: 5px 0;
                padding: 5px;
                background: #fff3cd;
                border-radius: 3px;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🔍 Knowledge App Screenshot Analysis</h1>
            <p>Analyzing {len(image_files)} screenshots for token streaming, question quality, and expert difficulty issues</p>
        </div>
    """
    
    # Add each screenshot
    for i, image_file in enumerate(image_files, 1):
        filename = image_file.name
        is_key_screenshot = any(keyword in filename.lower() for keyword in ['generation_in_progress', 'question_loaded_final'])
        
        # Determine screenshot purpose
        if 'app_loaded' in filename:
            purpose = "🚀 App Launch"
            analysis = "Check if app loads correctly and UI is responsive"
        elif 'quiz_screen' in filename:
            purpose = "🎯 Quiz Navigation"
            analysis = "Verify quiz section opens properly"
        elif 'topic_entered' in filename:
            purpose = "📝 Topic Input"
            analysis = "Confirm 'atoms' topic was entered correctly"
        elif 'difficulty_set' in filename:
            purpose = "🎓 Difficulty Setting"
            analysis = "Verify 'expert' difficulty was selected"
        elif 'question_type_set' in filename:
            purpose = "🔢 Question Type"
            analysis = "Check if 'numerical' type was selected"
        elif 'token_streaming_enabled' in filename:
            purpose = "🌊 Token Streaming"
            analysis = "Verify token streaming checkbox is enabled"
        elif 'quiz_started' in filename:
            purpose = "🚀 Quiz Start"
            analysis = "Check if quiz generation begins properly"
        elif 'generation_in_progress' in filename:
            purpose = "⚡ GENERATION IN PROGRESS (KEY SCREENSHOT)"
            analysis = """
            <div class="issue-checklist">
                <h4>🔍 Key Issues to Check:</h4>
                <ul>
                    <li>🌊 Is token streaming UI visible? (Should show live tokens appearing)</li>
                    <li>📊 Are there progress indicators or streaming stats?</li>
                    <li>🎯 Is there a streaming overlay or dialog box?</li>
                    <li>⚡ Can you see tokens being generated in real-time?</li>
                    <li>🔄 Is there any loading or generation feedback?</li>
                </ul>
            </div>
            """
        elif 'question_loaded_final' in filename:
            purpose = "✅ FINAL QUESTION (KEY SCREENSHOT)"
            analysis = """
            <div class="issue-checklist">
                <h4>🔍 Key Issues to Check:</h4>
                <ul>
                    <li>🔢 Is the question actually NUMERICAL? (Contains numbers, calculations, formulas)</li>
                    <li>🎓 Is it PhD/EXPERT level? (Complex, advanced terminology, theoretical)</li>
                    <li>⚛️ Is it about ATOMS? (Atomic structure, chemistry, physics)</li>
                    <li>📝 Is the question text clear and complete?</li>
                    <li>🎯 Are there multiple choice options visible?</li>
                    <li>🔄 Is this a unique question or repeated from before?</li>
                </ul>
            </div>
            """
        else:
            purpose = f"📸 Screenshot {i}"
            analysis = "General screenshot for reference"
        
        container_class = "screenshot-container"
        if is_key_screenshot:
            container_class += " key-screenshot"
        
        html_content += f"""
        <div class="{container_class}">
            <div class="screenshot-title">
                {i}. {purpose}
            </div>
            <img src="{image_file.name}" alt="{filename}" class="screenshot-image">
            <div class="analysis-section">
                <h4>🔍 Analysis Focus:</h4>
                <p>{analysis}</p>
                <p><strong>File:</strong> {filename}</p>
            </div>
        </div>
        """
    
    html_content += """
        <div class="header" style="margin-top: 40px;">
            <h2>🎯 SUMMARY OF ISSUES TO LOOK FOR</h2>
            <div style="text-align: left; max-width: 800px; margin: 0 auto;">
                <h3>🌊 Token Streaming Issues:</h3>
                <ul>
                    <li>No visible streaming UI during generation</li>
                    <li>Missing progress indicators or token counters</li>
                    <li>No real-time token display</li>
                </ul>
                
                <h3>🔢 Question Type Issues:</h3>
                <ul>
                    <li>Question is conceptual instead of numerical</li>
                    <li>No numbers, calculations, or formulas present</li>
                    <li>Missing units or quantitative elements</li>
                </ul>
                
                <h3>🎓 Expert Difficulty Issues:</h3>
                <ul>
                    <li>Question is too simple or basic</li>
                    <li>Missing advanced terminology</li>
                    <li>Not PhD-level complexity</li>
                </ul>
                
                <h3>⚛️ Topic Adherence Issues:</h3>
                <ul>
                    <li>Question not about atoms/atomic structure</li>
                    <li>Wrong subject matter</li>
                    <li>Generic chemistry instead of atomic focus</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    """
    
    # Save HTML file
    html_file = screenshots_dir / "screenshot_analysis.html"
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Created analysis HTML: {html_file}")
    
    # Open in browser
    try:
        webbrowser.open(f"file://{html_file.absolute()}")
        print("🌐 Opened analysis in browser")
    except:
        print("⚠️ Could not open browser automatically")
        print(f"📂 Please open: {html_file.absolute()}")
    
    return str(html_file)

def main():
    """Main function"""
    print("🔍 SIMPLE IMAGE ANALYZER")
    print("=" * 50)
    print("Creating HTML viewer for screenshot analysis...")
    
    html_file = create_image_analysis_html()
    
    if html_file:
        print("\n✅ ANALYSIS READY!")
        print("🔍 Review the screenshots in your browser to identify:")
        print("  1. 🌊 Token streaming visibility issues")
        print("  2. 🔢 Question type adherence (numerical vs conceptual)")
        print("  3. 🎓 Expert difficulty compliance")
        print("  4. ⚛️ Topic focus (atoms)")
        print("\n📝 Based on your visual analysis, I can then fix the specific issues!")

if __name__ == "__main__":
    main()
