"""
Unified question generation and management module for Knowledge App
"""

from .async_converter import async_time_sleep


import os
import logging
import asyncio
import concurrent.futures
from typing import Dict, Any, Optional, List, Tuple
from functools import lru_cache
from datetime import datetime, timedelta
import weakref
import threading
import json
from dataclasses import dataclass, asdict
from pathlib import Path

from .inference import CloudInference
from .memory_consolidation import get_consolidated_resource_manager as MemoryManager
from .interfaces import IQuestionService, Question, Quiz

logger = logging.getLogger(__name__)


@dataclass
class QuizQuestion:
    """Question data model"""

    id: str
    text: str
    options: Dict[str, str]
    correct_answer: str
    category: str
    difficulty: str
    explanation: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            "id": self.id,
            "question": self.text,
            "options": self.options,
            "correct": self.correct_answer,
            "category": self.category,
            "difficulty": self.difficulty,
            "explanation": self.explanation,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "QuizQuestion":
        """Create from dictionary format"""
        return cls(
            id=data.get("id", ""),
            text=data.get("question", ""),
            options=data.get("options", {}),
            correct_answer=data.get("correct", ""),
            category=data.get("category", ""),
            difficulty=data.get("difficulty", "medium"),
            explanation=data.get("explanation", ""),
        )


class QuestionCache:
    """
    TEMPORARY PERFORMANCE CACHE for quiz questions with TTL

    This is a SHORT-TERM cache for performance optimization - NOT permanent storage!

    Purpose:
    - Avoid redundant API calls within a few minutes
    - Improve response time for similar requests
    - Reduce GPU/AI model usage for identical parameters

    Retention: Short TTL (5-10 minutes)
    Storage: In-memory with automatic expiration

    NOTE: Questions are saved to DATABASE separately for permanent review history.
    This cache is only for performance and should NOT interfere with question diversity.
    """

    def __init__(self, ttl_seconds: int = 3600):
        self._cache: Dict[str, Tuple[Any, datetime]] = {}
        self._ttl = timedelta(seconds=ttl_seconds)
        self._lock = threading.Lock()

    def get(self, key: str) -> Optional[Any]:
        """Get item from cache if not expired"""
        with self._lock:
            if key in self._cache:
                item, timestamp = self._cache[key]
                if datetime.now() - timestamp < self._ttl:
                    return item
                else:
                    del self._cache[key]
            return None

    def set(self, key: str, value: Any) -> None:
        """Set item in cache with current timestamp"""
        with self._lock:
            self._cache[key] = (value, datetime.now())

    def clear(self) -> None:
        """Clear expired items from cache"""
        with self._lock:
            now = datetime.now()
            expired = [k for k, (_, ts) in self._cache.items() if now - ts >= self._ttl]
            for k in expired:
                del self._cache[k]


class QuestionGenerator(IQuestionService):
    """Unified question generation and management"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize question generator and manager"""
        self.config = config
        self.questions_dir = Path(
            config.get("questions_dir", Path.home() / ".knowledge_app" / "questions")
        )
        self.questions_dir.mkdir(parents=True, exist_ok=True)
        self.questions: List[QuizQuestion] = []
        self.memory_manager = MemoryManager(config)
        self.cloud_inference = CloudInference(config)

        # Load existing questions
        self.load_questions()

        # Setup caching
        self._question_cache = QuestionCache(ttl_seconds=3600)  # 1 hour TTL
        self._response_cache = QuestionCache(ttl_seconds=300)  # 5 minutes TTL

        # Setup storage
        storage_dir = config.get("storage", {}).get("questions_dir", "quiz_storage")
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)

        # Setup threading
        self._thread_pool = concurrent.futures.ThreadPoolExecutor(
            max_workers=min(4, (concurrent.futures.ThreadPoolExecutor()._max_workers))
        )
        self._lock = threading.Lock()

        # State
        self.current_difficulty = "medium"
        self.current_question_id = None
        self._cleanup_task = None
        self._cache = {}  # For storing generated questions

    def load_questions(self) -> None:
        """Load questions from JSON files"""
        try:
            for file in self.questions_dir.glob("*.json"):
                with open(file, "r", encoding="utf-8") as f:
                    questions_data = json.load(f)
                    for q_data in questions_data:
                        self.questions.append(QuizQuestion(**q_data))
        except Exception as e:
            logger.error(f"Error loading questions: {e}")

    def save_questions(self) -> None:
        """Save questions to JSON file"""
        try:
            questions_data = [asdict(q) for q in self.questions]
            with open(self.questions_dir / "questions.json", "w", encoding="utf-8") as f:
                json.dump(questions_data, f, indent=4, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving questions: {e}")

    def get_questions(
        self,
        category: Optional[str] = None,
        difficulty: Optional[str] = None,
        count: Optional[int] = None,
    ) -> List[QuizQuestion]:
        """Get filtered questions"""
        filtered = self.questions
        if category:
            filtered = [q for q in filtered if q.category == category]
        if difficulty:
            filtered = [q for q in filtered if q.difficulty == difficulty]
        if count:
            filtered = filtered[:count]
        return filtered

    def add_question(self, question_data: Dict[str, Any]) -> bool:
        """Add a new question"""
        try:
            question = QuizQuestion(**question_data)
            self.questions.append(question)
            self.save_questions()
            return True
        except Exception as e:
            logger.error(f"Error adding question: {e}")
            return False

    async def generate_questions(self, text: str, count: int = 5) -> List[QuizQuestion]:
        """Generate questions from text using AI"""
        try:
            # Use cloud inference to generate questions
            questions = await self.cloud_inference.generate_questions(text, count)

            # Add generated questions to the collection
            for q in questions:
                self.add_question(asdict(q))

            return questions

        except Exception as e:
            logger.error(f"Error generating questions: {e}")
            return []

    def create_quiz(
        self,
        category: Optional[str] = None,
        difficulty: Optional[str] = None,
        question_count: int = 10,
    ) -> Quiz:
        """Create a quiz from available questions"""
        questions = self.get_questions(category, difficulty, question_count)
        return Quiz(questions=questions)

    async def initialize(self) -> None:
        """Initialize async components"""
        await self._setup_cache_cleanup()

    async def _setup_cache_cleanup(self) -> None:
        """Setup periodic cache cleanup"""
        cleanup_interval = self.config.get("cache", {}).get("cleanup_interval", 3600)

        async def cleanup_task():
            while True:
                await async_time_sleep(cleanup_interval)
                await self._cleanup_cache()

        self._cleanup_task = asyncio.create_task(cleanup_task())

    def cleanup(self) -> None:
        """Clean up resources"""
        try:
            # 🚨 CRITICAL FIX: Non-blocking cleanup to prevent UI freeze
            import concurrent.futures
            
            def cleanup_in_thread():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(self.cleanup_async())
                except Exception as e:
                    logger.error(f"❌ Cleanup failed: {e}")
                finally:
                    try:
                        loop.close()
                    except:
                        pass
            
            try:
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(cleanup_in_thread)
                    future.result(timeout=5.0)  # Short timeout for cleanup
            except concurrent.futures.TimeoutError:
                logger.warning("⚠️ Cleanup timed out - forcing exit")
            except Exception as e:
                logger.error(f"❌ Cleanup executor failed: {e}")

            # Clear caches
            self._question_cache.clear()
            self._response_cache.clear()

            # Clear LRU caches
            self._get_difficulty_params.cache_clear()
            self.get_reviewed_questions.cache_clear()

            # Shutdown thread pool
            self._thread_pool.shutdown(wait=True)

            # Clean up inference
            if hasattr(self.cloud_inference, "cleanup"):
                self.cloud_inference.cleanup()

            # Clean up memory
            self.memory_manager.collect_garbage()

        except Exception as e:
            logger.error(f"Error during quiz generator cleanup: {e}")
        finally:
            # Ensure thread pool is shut down
            if hasattr(self, "_thread_pool"):
                try:
                    self._thread_pool.shutdown(wait=False)
                except:
                    pass

    async def _generate_single_question(self, content: str) -> Question:
        """Generate a single question from content"""
        try:
            # Generate question using cloud inference
            raw_question = await self.cloud_inference.generate_quiz_async(
                context=content, difficulty=self.current_difficulty
            )

            # Validate and convert to QuizQuestion
            if not self._validate_question_format(raw_question):
                raise ValueError("Generated question has invalid format")

            question = QuizQuestion(
                id=str(len(self._cache) + 1),
                text=raw_question["question"],
                options=raw_question["options"],
                correct_answer=raw_question["correct"],
                category="general",  # TODO: Add category detection
                difficulty=self.current_difficulty,
                explanation=raw_question.get("explanation", ""),
            )

            # Store in cache
            self._cache[question.id] = question
            self.current_question_id = question.id

            return question

        except Exception as e:
            logger.error(f"Error generating single question: {e}")
            raise

    def _validate_question_format(self, question: Dict[str, Any]) -> bool:
        """Validate question format"""
        if not isinstance(question, dict):
            return False

        required_fields = {"question", "options", "correct", "explanation"}
        if not all(field in question for field in required_fields):
            return False

        if not isinstance(question["options"], dict):
            return False

        if not all(opt in question["options"] for opt in ["A", "B", "C", "D"]):
            return False

        if question["correct"] not in ["A", "B", "C", "D"]:
            return False

        if not question["question"].strip():
            return False

        if not question["explanation"].strip():
            return False

        return True

    def _save_quiz(self, quiz: Quiz) -> None:
        """Save quiz to storage"""
        try:
            quiz_path = self.storage_dir / f"{quiz.name.lower().replace(' ', '_')}.json"
            with open(quiz_path, "w") as f:
                json.dump(
                    {
                        "name": quiz.name,
                        "description": quiz.description,
                        "questions": [q.to_dict() for q in quiz.questions],
                        "created_at": datetime.now().isoformat(),
                    },
                    f,
                    indent=2,
                )
        except Exception as e:
            logger.error(f"Error saving quiz: {e}")

    def evaluate_answer(self, question: Question, user_answer: str) -> Tuple[bool, float, str]:
        """Evaluate a user's answer"""
        try:
            is_correct = user_answer.upper() == question.correct_answer.upper()
            confidence = 1.0 if is_correct else 0.0
            feedback = (
                question.explanation
                if question.explanation
                else (
                    "Correct!"
                    if is_correct
                    else f"Incorrect. The correct answer is {question.correct_answer}."
                )
            )
            return is_correct, confidence, feedback
        except Exception as e:
            logger.error(f"Error evaluating answer: {e}")
            return False, 0.0, f"Error evaluating answer: {str(e)}"

    @lru_cache(maxsize=16)
    def _get_difficulty_params(self, difficulty: str) -> Dict[str, Any]:
        """Get model parameters for difficulty level"""
        params = {
            "easy": {"temperature": 0.3, "max_length": 150},
            "medium": {"temperature": 0.5, "max_length": 200},
            "hard": {"temperature": 0.7, "max_length": 250},
        }
        return params.get(difficulty.lower(), params["medium"])

    async def _cleanup_cache(self) -> None:
        """Clean up caches"""
        try:
            # Clear expired items from caches
            self._question_cache.clear()
            self._response_cache.clear()

            # Clear main cache if it exceeds size limit
            max_size = self.config.get("cache", {}).get("max_size", 1000000)
            current_size = sum(len(str(q)) for q in self._cache.values())

            if current_size > max_size:
                self._cache.clear()

            # Clean up memory
            self.memory_manager.collect_garbage()

        except Exception as e:
            logger.error(f"Error during cache cleanup: {e}")