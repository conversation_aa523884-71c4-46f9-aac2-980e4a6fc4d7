"""Startup Performance Profiler"""

import time
import psutil
import logging
from typing import Dict, List
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class StartupMetric:
    name: str
    start_time: float
    end_time: float = None
    memory_start: float = 0.0
    memory_end: float = 0.0

    @property
    def duration(self) -> float:
        return (self.end_time or time.time()) - self.start_time

    @property
    def memory_delta(self) -> float:
        return self.memory_end - self.memory_start


class StartupProfiler:
    def __init__(self):
        self.metrics: List[StartupMetric] = []
        self.active_metrics: Dict[str, StartupMetric] = {}
        self.start_time = time.time()

    def start_phase(self, name: str):
        """Start timing a phase"""
        memory = psutil.Process().memory_info().rss / 1024 / 1024
        metric = StartupMetric(name=name, start_time=time.time(), memory_start=memory)
        self.active_metrics[name] = metric
        logger.info(f"📊 Started phase: {name}")

    def end_phase(self, name: str):
        """End timing a phase"""
        if name in self.active_metrics:
            metric = self.active_metrics.pop(name)
            metric.end_time = time.time()
            metric.memory_end = psutil.Process().memory_info().rss / 1024 / 1024
            self.metrics.append(metric)

            logger.info(f"✅ Phase {name}: {metric.duration:.3f}s (+{metric.memory_delta:.1f}MB)")

    def get_summary(self) -> Dict:
        """Get performance summary"""
        total_time = time.time() - self.start_time
        total_memory = sum(m.memory_delta for m in self.metrics)

        return {
            "total_time": total_time,
            "total_memory_delta": total_memory,
            "phases": [
                {"name": m.name, "duration": m.duration, "memory_delta": m.memory_delta}
                for m in self.metrics
            ],
        }


# Global profiler instance
_profiler = None


def get_profiler() -> StartupProfiler:
    global _profiler
    if _profiler is None:
        _profiler = StartupProfiler()
    return _profiler


def start_phase(name: str):
    get_profiler().start_phase(name)


def end_phase(name: str):
    get_profiler().end_phase(name)


def get_summary():
    return get_profiler().get_summary()