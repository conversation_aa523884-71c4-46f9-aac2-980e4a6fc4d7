#!/usr/bin/env python3
"""
Final working test - Direct integration that actually works
"""

import sys
import os
import time
import json
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, Qt

def main():
    print("🎉 FINAL WORKING TEST - Direct Question Generation")
    print("=" * 60)

    # Fix Qt WebEngine initialization
    QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
    app = QApplication([])
    
    # Create the app
    from knowledge_app.webengine_app import KnowledgeAppWebEngine
    knowledge_app = KnowledgeAppWebEngine()
    knowledge_app.show()
    
    # Wait for initialization
    time.sleep(3)
    
    def generate_and_display_question():
        try:
            print("🚀 Generating question directly...")
            
            # Initialize unified manager
            from knowledge_app.core.unified_inference_manager import initialize_unified_inference, UnifiedInferenceManager
            
            success = initialize_unified_inference()
            if not success:
                print("❌ Failed to initialize unified manager")
                return
                
            manager = UnifiedInferenceManager()
            
            # Generate question with explicit numerical emphasis
            print("🔢 Requesting NUMERICAL question about atoms...")

            # Add debugging to see which generator is being used
            print(f"🔍 Manager mode: {manager._mode}")
            print(f"🔍 Local available: {manager._local_available}")
            print(f"🔍 Cloud available: {manager._cloud_available}")

            result = manager.generate_mcq_sync(
                topic="atomic calculations and quantitative properties",
                difficulty="expert",
                question_type="numerical"
            )
            
            if result:
                print("✅ Question generated successfully!")
                question_text = result.get('question', 'N/A')
                options = result.get('options', 'N/A')

                print(f"📝 Question: {question_text}")
                print(f"🔢 Options: {options}")
                print(f"✅ Correct: {result.get('correct', 'N/A')}")

                # Check if it's actually numerical
                import re
                has_numbers = bool(re.search(r'\d+', question_text)) or any(
                    re.search(r'\d+', str(opt)) for opt in (options if isinstance(options, list) else options.values() if isinstance(options, dict) else [])
                )

                numerical_keywords = ['calculate', 'compute', 'mass', 'energy', 'electrons', 'protons', 'neutrons', 'atomic number', 'wavelength', 'frequency']
                has_numerical_keywords = any(keyword in question_text.lower() for keyword in numerical_keywords)

                if has_numbers or has_numerical_keywords:
                    print("✅ CORRECT: This is a numerical question!")
                else:
                    print("❌ WRONG: This is conceptual, not numerical!")
                    print("🔧 The question_type parameter is being ignored!")

                # Format for UI
                formatted_question = {
                    "question": result.get('question', ''),
                    "options": result.get('options', []),
                    "correct_answer": result.get('correct', ''),
                    "explanation": result.get('explanation', ''),
                    "question_number": 1,
                    "total_questions": 1,
                    "is_loading": False
                }

                # Emit directly to UI
                print("📡 Emitting question to UI...")
                knowledge_app.bridge.questionReceived.emit(formatted_question)

                print("🎉 Question should now be displayed in UI!")
                
            else:
                print("❌ No question generated")
                
        except Exception as e:
            print(f"❌ Generation failed: {e}")
    
    # Generate question after app is ready
    QTimer.singleShot(5000, generate_and_display_question)
    
    # Keep app running for 30 seconds to see the result
    def close_app():
        print("🔚 Closing app...")
        app.quit()
    
    QTimer.singleShot(30000, close_app)
    
    print("⏳ App running... Check the UI for the question!")
    app.exec_()

if __name__ == "__main__":
    main()
