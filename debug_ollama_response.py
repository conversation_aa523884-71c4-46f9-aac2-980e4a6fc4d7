#!/usr/bin/env python3
"""
Debug what Ollama is actually returning
"""

import sys
import os
import requests
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_ollama_raw_response():
    """Test raw Ollama response to see what we're getting"""
    print("🔍 Testing Raw Ollama Response")
    print("=" * 50)
    
    try:
        # Simple JSON prompt
        prompt = """Create a multiple choice question about physics.

Generate a JSON response with this exact format:
{
  "question": "Your question here?",
  "options": ["Option A", "Option B", "Option C", "Option D"],
  "correct": "A",
  "explanation": "Brief explanation"
}

CRITICAL: You MUST respond with ONLY valid JSON. No explanations, no text before or after the JSON.

Start your response with { and end with }. Nothing else."""
        
        payload = {
            "model": "llama3.1:8b",
            "prompt": prompt,
            "stream": False,
            "format": "json",
            "options": {
                "temperature": 0.1,
                "top_p": 0.9,
                "num_predict": 500
            }
        }
        
        print("🚀 Calling Ollama...")
        response = requests.post("http://localhost:11434/api/generate", 
                               json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('response', '')
            
            print(f"✅ Ollama responded ({len(content)} chars)")
            print(f"\n📝 Raw Response:")
            print("=" * 50)
            print(content)
            print("=" * 50)
            
            # Try to parse as JSON
            try:
                parsed = json.loads(content)
                print(f"\n✅ JSON parsing successful!")
                print(f"   Question: {parsed.get('question', 'N/A')}")
                print(f"   Options: {len(parsed.get('options', []))} choices")
                print(f"   Correct: {parsed.get('correct', 'N/A')}")
                return True
            except json.JSONDecodeError as e:
                print(f"\n❌ JSON parsing failed: {e}")
                
                # Try to find JSON in the response
                import re
                json_match = re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', content)
                if json_match:
                    json_str = json_match.group()
                    print(f"\n🔍 Found JSON pattern: {json_str[:100]}...")
                    try:
                        parsed = json.loads(json_str)
                        print(f"✅ Extracted JSON parsing successful!")
                        print(f"   Question: {parsed.get('question', 'N/A')}")
                        return True
                    except json.JSONDecodeError as e2:
                        print(f"❌ Extracted JSON also failed: {e2}")
                else:
                    print("❌ No JSON pattern found in response")
                
                return False
        else:
            print(f"❌ Ollama request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_intelligent_system():
    """Test our intelligent system directly"""
    print("\n🧠 Testing Intelligent System")
    print("=" * 50)
    
    try:
        from knowledge_app.core.intelligent_prompt_generator import get_intelligent_prompt_generator
        
        generator = get_intelligent_prompt_generator()
        result = generator.generate_intelligent_prompt("dfs", "medium", "mixed")
        
        print(f"✅ Resolved 'dfs' → '{result['metadata']['resolved_topic']}'")
        print(f"📝 Confidence: {result['confidence']:.2f}")
        print(f"\n📝 Generated Prompt Preview:")
        print(result['prompt'][:300] + "...")
        
        return True
        
    except Exception as e:
        print(f"❌ Intelligent system test failed: {e}")
        return False

def main():
    print("🔍 Debugging Ollama Integration")
    print("=" * 60)
    
    # Test 1: Raw Ollama response
    ollama_success = test_ollama_raw_response()
    
    # Test 2: Intelligent system
    intelligent_success = test_intelligent_system()
    
    print("\n" + "=" * 60)
    print("📊 Debug Results:")
    print(f"   {'✅ PASS' if ollama_success else '❌ FAIL'} - Raw Ollama JSON")
    print(f"   {'✅ PASS' if intelligent_success else '❌ FAIL'} - Intelligent System")
    
    if ollama_success and intelligent_success:
        print("\n🎉 Both systems work! The issue might be in the integration.")
    elif ollama_success:
        print("\n💡 Ollama works but intelligent system has issues.")
    elif intelligent_success:
        print("\n💡 Intelligent system works but Ollama JSON has issues.")
    else:
        print("\n❌ Both systems have issues.")
    
    return 0 if (ollama_success or intelligent_success) else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
