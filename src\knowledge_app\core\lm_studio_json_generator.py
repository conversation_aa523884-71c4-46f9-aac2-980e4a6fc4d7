#!/usr/bin/env python3
"""
🚀 LM Studio JSON Generator - OpenAI Compatible API
Superior JSON handling without DeepSeek's reasoning complications
Optimized for reliable MCQ generation with structured output
"""

import logging
import json
import time
from typing import Dict, List, Any, Optional
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

from .mcq_generator import MCQGenerator

logger = logging.getLogger(__name__)


class LMStudioJSONGenerator(MCQGenerator):
    """
    🚀 LM Studio JSON MCQ Generator - OpenAI Compatible
    Uses LM Studio's OpenAI-compatible API for reliable JSON generation
    No reasoning complications, just clean JSON output
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        
        # LM Studio Configuration
        self.base_url = 'http://127.0.0.1:1234'
        self.api_endpoint = f"{self.base_url}/v1/chat/completions"
        self.models_endpoint = f"{self.base_url}/v1/models"
        
        # Available models and active model
        self.available_models = []
        self.active_model = None
        self.is_initialized = False
        
        # HTTP session with optimized settings
        self.session = requests.Session()
        retry_strategy = Retry(
            total=2,
            status_forcelist=[429, 500, 502, 503, 504],
            backoff_factor=0.5
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 🚀 LM STUDIO OPTIMIZED PARAMETERS
        self.generation_params = {
            'temperature': 0.4,         # Balanced creativity and consistency
            'top_p': 0.95,              # High quality diverse output
            'max_tokens': 1500,         # Sufficient for detailed questions
            'presence_penalty': 0.1,    # Encourage diverse content
            'frequency_penalty': 0.1    # Reduce repetition
        }
        
        # Statistics tracking
        self.generation_stats = {
            "total_generated": 0,
            "json_success_rate": 0,
            "avg_time": 0,
            "active_model": None,
            "api_type": "lm_studio_openai_compatible"
        }

    def initialize(self) -> bool:
        """Initialize the LM Studio JSON generator"""
        try:
            logger.info("🚀 Initializing LM Studio JSON Generator...")
            logger.info(f"🔗 Connecting to LM Studio: {self.base_url}")
            
            # Check if LM Studio is running
            if not self._check_connection():
                logger.error("❌ Cannot connect to LM Studio server")
                logger.error("💡 Make sure LM Studio is running and serving at http://127.0.0.1:1234")
                return False
            
            logger.info("✅ Successfully connected to LM Studio")
            
            # Get available models
            self.available_models = self._get_available_models()
            if not self.available_models:
                logger.error("❌ No models available in LM Studio")
                logger.error("💡 Load a model in LM Studio interface")
                return False
            
            logger.info(f"📋 Found {len(self.available_models)} models: {', '.join(self.available_models)}")
            
            # Use the first available model (LM Studio typically loads one at a time)
            self.active_model = self.available_models[0]
            
            # Test the selected model
            logger.info(f"🧪 Testing JSON generation with: {self.active_model}")
            if not self._test_json_generation():
                logger.warning(f"⚠️ JSON generation test failed, but continuing...")
            
            logger.info(f"🚀 LM Studio JSON Generator initialized!")
            logger.info(f"🎯 Active model: {self.active_model}")
            logger.info(f"📊 Generation settings: temp={self.generation_params['temperature']}, max_tokens={self.generation_params['max_tokens']}")
            
            self.is_initialized = True
            self.generation_stats["active_model"] = self.active_model
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize LM Studio JSON generator: {e}")
            return False

    def _check_connection(self) -> bool:
        """Check if LM Studio server is accessible"""
        try:
            response = self.session.get(f"{self.base_url}/v1/models", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.debug(f"❌ LM Studio connection failed: {e}")
            return False

    def _get_available_models(self) -> List[str]:
        """Get list of available models from LM Studio"""
        try:
            response = self.session.get(self.models_endpoint, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            models = [model["id"] for model in data.get("data", [])]
            
            logger.info(f"📋 Found {len(models)} models in LM Studio")
            return models
            
        except Exception as e:
            logger.error(f"❌ Failed to get models from LM Studio: {e}")
            return []

    def _test_json_generation(self) -> bool:
        """Test JSON generation with selected model"""
        try:
            test_response = self._generate_single_question("test topic", "easy", "multiple_choice")
            return test_response is not None
            
        except Exception as e:
            logger.error(f"JSON generation test failed: {e}")
            return False

    def generate_mcq(self, topic: str, context: str = "", num_questions: int = 1, difficulty: str = "medium", game_mode: str = "casual", question_type: str = "mixed") -> List[Dict[str, Any]]:
        """🚀 LM Studio MCQ Generation - Clean and Reliable"""
        if not self.is_available():
            logger.error("❌ LM Studio JSON generator not available")
            return []

        try:
            logger.info(f"🚀 LM Studio generating {num_questions} questions about: {topic}")
            
            results = []
            for i in range(num_questions):
                logger.info(f"🚀 Generating question {i+1}/{num_questions}")
                
                question = self._generate_single_question(topic, difficulty, question_type)
                
                if question:
                    results.append(question)
                    logger.info(f"✅ Question {i+1} successful")
                else:
                    logger.warning(f"⚠️ Question {i+1} failed")
            
            logger.info(f"🚀 LM Studio generation complete: {len(results)}/{num_questions} successful")
            return results
                
        except Exception as e:
            logger.error(f"❌ LM Studio generation failed: {e}")
            return []

    def _generate_single_question(self, topic: str, difficulty: str, question_type: str) -> Optional[Dict[str, Any]]:
        """🚀 Generate single MCQ using LM Studio OpenAI API"""
        
        # Create system prompt for JSON mode
        system_prompt = """You are an expert MCQ generator. You MUST respond with valid JSON ONLY. 

Use this exact format:
{
  "question": "Your question text ending with ?",
  "options": ["Option A", "Option B", "Option C", "Option D"],
  "correct_answer": "A",
  "explanation": "Brief explanation of the correct answer"
}

Rules:
- Question MUST end with ?
- Exactly 4 options
- correct_answer must be A, B, C, or D (matching the correct option)
- No additional text outside JSON"""

        user_prompt = f"""Create a {difficulty} difficulty multiple choice question about: {topic}

The question should test understanding of key concepts in {topic}.
Make the incorrect options plausible but clearly wrong.
Ensure the correct answer is unambiguous.

Respond with JSON only:"""

        try:
            start_time = time.time()
            
            payload = {
                "model": self.active_model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": self.generation_params['temperature'],
                "top_p": self.generation_params['top_p'],
                "max_tokens": self.generation_params['max_tokens'],
                "presence_penalty": self.generation_params['presence_penalty'],
                "frequency_penalty": self.generation_params['frequency_penalty']
            }
            
            response = self.session.post(
                self.api_endpoint,
                json=payload,
                timeout=60
            )
            
            api_time = time.time() - start_time
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content'].strip()
            
            logger.info(f"🚀 LM Studio API call completed in {api_time:.1f}s")
            logger.debug(f"🚀 Response content: {content[:100]}...")
            
            # Parse JSON response
            parsed_json = self._parse_json_response(content)
            
            if parsed_json:
                # Validate and normalize
                return self._validate_and_normalize_question(parsed_json, topic, difficulty)
            
            return None
            
        except Exception as e:
            logger.error(f"❌ LM Studio API error: {e}")
            return None

    def _parse_json_response(self, content: str) -> Optional[Dict[str, Any]]:
        """🚀 Clean JSON parsing for LM Studio responses"""
        
        try:
            content = content.strip()
            
            # Remove any markdown code blocks
            if content.startswith('```json'):
                content = content[7:]
            if content.endswith('```'):
                content = content[:-3]
            
            content = content.strip()
            
            # Try direct JSON parse
            parsed = json.loads(content)
            logger.info("🚀 Successfully parsed JSON from LM Studio")
            return parsed
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON parsing failed: {e}")
            logger.debug(f"❌ Failed content: {content[:200]}...")
            
            # Try to extract JSON object from text
            try:
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    parsed = json.loads(json_str)
                    logger.info("🚀 Extracted JSON using regex")
                    return parsed
            except:
                pass
            
            return None
        except Exception as e:
            logger.error(f"❌ Unexpected parsing error: {e}")
            return None

    def _validate_and_normalize_question(self, data: Dict[str, Any], topic: str, difficulty: str) -> Optional[Dict[str, Any]]:
        """🚀 Validate and normalize question data"""
        
        try:
            # Required fields check
            required_fields = ['question', 'options', 'correct_answer']
            if not all(field in data for field in required_fields):
                logger.warning(f"❌ Missing required fields: {[f for f in required_fields if f not in data]}")
                return None
            
            question = data['question'].strip()
            options = data['options']
            correct_answer = data['correct_answer'].strip()
            explanation = data.get('explanation', '').strip()
            
            # Validation checks
            if not question.endswith('?'):
                question += '?'  # Auto-fix question mark
            
            if not isinstance(options, list) or len(options) != 4:
                logger.warning(f"❌ Invalid options: expected 4, got {len(options) if isinstance(options, list) else 'non-list'}")
                return None
            
            if correct_answer not in ['A', 'B', 'C', 'D']:
                logger.warning(f"❌ Invalid correct_answer: {correct_answer}")
                return None
            
            if not all(opt.strip() for opt in options):
                logger.warning("❌ Empty options detected")
                return None
            
            # Normalize the response
            normalized = {
                'question': question,
                'options': [opt.strip() for opt in options],
                'correct_answer': correct_answer,
                'explanation': explanation if explanation else f"This tests understanding of {topic}.",
                'difficulty': difficulty,
                'generated_by': self.active_model,
                'api_type': 'lm_studio'
            }
            
            logger.info("🚀 Question validation successful")
            return normalized
            
        except Exception as e:
            logger.error(f"❌ Validation error: {e}")
            return None

    def is_available(self) -> bool:
        """Check if the generator is available and initialized"""
        return self.is_initialized and self.active_model is not None

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            **self.generation_stats,
            "api_type": "lm_studio_openai_compatible",
            "endpoint": self.api_endpoint
        }

    def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, 'session'):
            self.session.close()
        logger.info("🚀 LM Studio JSON Generator cleaned up")