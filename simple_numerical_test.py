#!/usr/bin/env python3
"""
Simple test to generate a numerical question and display it in the UI
"""

import sys
import os
import time
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, Qt

def create_fallback_question():
    """Create a hardcoded numerical question as fallback"""
    return {
        "question": "Calculate the energy of an electron in the n=3 shell of hydrogen. Given that the ground state energy (n=1) is -13.6 eV, what is the energy for n=3?",
        "options": [
            "-1.51 eV",
            "-3.40 eV",
            "-6.04 eV",
            "-13.6 eV"
        ],
        "correct_answer": "A",
        "explanation": "Using the formula E_n = -13.6/n², for n=3: E_3 = -13.6/(3²) = -13.6/9 = -1.51 eV",
        "question_number": 1,
        "total_questions": 1,
        "is_loading": False
    }

def main():
    print("🔢 SIMPLE NUMERICAL QUESTION TEST")
    print("=" * 50)
    
    # Fix Qt WebEngine initialization
    QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
    app = QApplication([])
    
    # Create the app
    from knowledge_app.webengine_app import KnowledgeAppWebEngine
    knowledge_app = KnowledgeAppWebEngine()
    knowledge_app.show()
    
    # Wait for initialization
    time.sleep(3)
    
    def generate_simple_numerical_question():
        try:
            print("🔢 Generating REAL numerical question using working generator...")

            # DIRECT FIX: Force the UI to use DeepSeek pipeline by modifying the unified manager
            print("🔧 Applying direct UI fix for DeepSeek integration...")

            # Patch the unified manager to force DeepSeek usage
            if hasattr(knowledge_app, 'unified_manager'):
                # Force expert mode to use DeepSeek
                original_generate = knowledge_app.unified_manager.generate_mcq_sync

                def force_deepseek_generate(*args, **kwargs):
                    print("🧠 FORCING DeepSeek pipeline for expert mode...")

                    # Override difficulty to expert to trigger DeepSeek
                    kwargs['difficulty'] = 'expert'
                    kwargs['question_type'] = 'numerical'

                    try:
                        result = original_generate(*args, **kwargs)
                        if result:
                            print("✅ DeepSeek pipeline generated question!")
                            return result
                        else:
                            print("❌ DeepSeek pipeline failed, using fallback")
                            return None
                    except Exception as e:
                        print(f"❌ DeepSeek error: {e}, using fallback")
                        return None

                # Apply the patch
                knowledge_app.unified_manager.generate_mcq_sync = force_deepseek_generate

                # Generate using the patched manager
                result = knowledge_app.unified_manager.generate_mcq_sync(
                    topic="atoms",
                    difficulty="expert",
                    question_type="numerical"
                )

                if result:
                    numerical_question = {
                        "question": result.get('question', 'Generated question'),
                        "options": result.get('options', ['A', 'B', 'C', 'D']),
                        "correct_answer": result.get('correct_answer', result.get('correct', 'A')),
                        "explanation": result.get('explanation', 'Generated explanation'),
                        "question_number": 1,
                        "total_questions": 1,
                        "is_loading": False
                    }
                else:
                    print("❌ Patched manager failed, using fallback")
                    numerical_question = create_fallback_question()
            else:
                print("❌ No unified manager found, using fallback")
                numerical_question = create_fallback_question()
            
            print(f"✅ Created numerical question:")
            print(f"   Question: {numerical_question['question']}")
            print(f"   Options: {numerical_question['options']}")
            print(f"   Correct: {numerical_question['correct_answer']}")
            
            # Emit directly to UI via bridge
            print("📡 Emitting question to UI...")
            knowledge_app.bridge.questionReceived.emit(numerical_question)
            
            print("🎉 Numerical question should now be displayed!")
            
        except Exception as e:
            print(f"❌ Failed to create question: {e}")
    
    # Generate question after app is ready
    QTimer.singleShot(5000, generate_simple_numerical_question)
    
    # Keep app running for 30 seconds to see the result
    def close_app():
        print("🔚 Closing app...")
        app.quit()
    
    QTimer.singleShot(35000, close_app)
    
    print("⏳ App running... Check the UI for the numerical question!")
    app.exec_()

if __name__ == "__main__":
    main()
