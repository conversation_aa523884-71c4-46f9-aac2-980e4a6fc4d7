#!/usr/bin/env python3
"""
🤖 FULLY AUTOMATIC UI TEST
Uses actual HTML element IDs from the codebase for precise automation
"""

import sys
import os
import time
import subprocess
import threading
from pathlib import Path
from datetime import datetime

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    import pyautogui
    import winsound
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import QTimer, QThread, pyqtSignal, QObject
    from knowledge_app.webengine_app import KnowledgeAppWebEngine
except ImportError:
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyautogui", "pillow", "PyQt5"])
    import pyautogui
    import winsound
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import QTimer, QThread, pyqtSignal, QObject
    from knowledge_app.webengine_app import KnowledgeAppWebEngine

class FullyAutomaticUITester(QObject):
    """Fully automatic UI tester using actual HTML element IDs with REAL async operations"""

    test_completed = pyqtSignal(bool, str)

    def __init__(self, app_window):
        super().__init__()
        self.app_window = app_window
        self.screenshot_dir = Path("test_screenshots")
        self.current_step = 0
        self.generation_screenshots = []

        # 🗑️ CLEAR SCREENSHOTS DIRECTORY ON EACH RUN
        if self.screenshot_dir.exists():
            import shutil
            shutil.rmtree(self.screenshot_dir)
            print("🗑️ Cleared previous screenshots")

        self.screenshot_dir.mkdir(exist_ok=True)
        print(f"📁 Screenshots will be saved to: {self.screenshot_dir.absolute()}")

        # Configure pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1  # Faster automation
        
    def take_screenshot(self, step_name):
        """Take a screenshot in JPG format for better AI analysis"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{step_name}_{timestamp}.jpg"
        filepath = self.screenshot_dir / filename

        # Take screenshot of the app window
        pixmap = self.app_window.grab()
        # Save as JPG for better AI readability
        pixmap.save(str(filepath), "JPG", quality=95)

        print(f"📸 Screenshot saved: {filepath}")
        return str(filepath)
    
    def execute_js(self, js_code):
        """Execute JavaScript in the web view asynchronously"""
        def js_callback(result):
            print(f"✅ JS executed: {result}")

        self.app_window.page().runJavaScript(js_code, js_callback)
        QApplication.processEvents()  # Process events to prevent UI freeze
        time.sleep(0.3)  # Give time for execution

    def wait_for_element(self, selector, timeout=10):
        """Wait for an element to appear and return if it exists"""
        js_code = f"""
        (function() {{
            var element = document.querySelector('{selector}');
            return element !== null;
        }})();
        """

        start_time = time.time()
        while time.time() - start_time < timeout:
            QApplication.processEvents()  # Keep UI responsive
            time.sleep(0.3)
        return True
    
    def click_element(self, selector, description="element"):
        """Click an element using JavaScript with proper escaping"""
        # Escape quotes properly for JavaScript
        escaped_selector = selector.replace("'", "\\'")
        js_code = f"""
        (function() {{
            var element = document.querySelector('{escaped_selector}');
            if (element) {{
                element.click();
                return 'clicked';
            }}
            return 'not_found';
        }})();
        """
        self.execute_js(js_code)
        print(f"🖱️ Clicked {description}: {selector}")
        QApplication.processEvents()  # Keep UI responsive

    def set_input_value(self, selector, value, description="input"):
        """Set input value using JavaScript"""
        js_code = f"""
        (function() {{
            var element = document.querySelector('{selector}');
            if (element) {{
                element.value = '{value}';
                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return 'set_value';
            }}
            return 'not_found';
        }})();
        """
        self.execute_js(js_code)
        print(f"⌨️ Set {description} to: {value}")
        QApplication.processEvents()  # Keep UI responsive

    def set_select_value(self, selector, value, description="select"):
        """Set select dropdown value using JavaScript"""
        js_code = f"""
        (function() {{
            var element = document.querySelector('{selector}');
            if (element) {{
                element.value = '{value}';
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return 'set_select';
            }}
            return 'not_found';
        }})();
        """
        self.execute_js(js_code)
        print(f"🔽 Set {description} to: {value}")
        QApplication.processEvents()  # Keep UI responsive

    def check_checkbox(self, selector, description="checkbox"):
        """Check a checkbox using JavaScript"""
        js_code = f"""
        (function() {{
            var element = document.querySelector('{selector}');
            if (element) {{
                element.checked = true;
                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return 'checked';
            }}
            return 'not_found';
        }})();
        """
        self.execute_js(js_code)
        print(f"☑️ Checked {description}: {selector}")
        QApplication.processEvents()  # Keep UI responsive

    def play_completion_sound(self):
        """Play completion sound"""
        try:
            for _ in range(3):
                winsound.Beep(1000, 300)  # 1000Hz for 300ms
                time.sleep(0.2)
            print("🔔 Completion sound played!")
        except Exception as e:
            print(f"⚠️ Could not play sound: {e}")
    
    def start_async_test(self):
        """Start the async test sequence"""
        print("🤖 STARTING FULLY AUTOMATIC UI TEST")
        print("=" * 50)
        self.current_step = 0
        self.next_step()

    def next_step(self):
        """Execute the next test step asynchronously"""
        try:
            if self.current_step == 0:
                print("⏳ Waiting for app to load...")
                self.take_screenshot("01_app_loaded")
                self.current_step += 1
                QTimer.singleShot(2000, self.next_step)  # Wait 2 seconds

            elif self.current_step == 1:
                print("🎯 Clicking Quiz navigation...")
                # Use a simpler selector to avoid quote issues
                self.execute_js("document.querySelector('.nav-item:nth-child(2)').click();")
                self.take_screenshot("02_quiz_screen_opened")
                self.current_step += 1
                QTimer.singleShot(2000, self.next_step)

            elif self.current_step == 2:
                print("📝 Entering topic 'atoms'...")
                self.set_input_value("#quiz-topic", "atoms", "topic input")
                self.take_screenshot("03_topic_entered")
                self.current_step += 1
                QTimer.singleShot(1000, self.next_step)

            elif self.current_step == 3:
                print("🎓 Setting difficulty to expert...")
                self.set_select_value("#quiz-difficulty", "expert", "difficulty dropdown")
                self.take_screenshot("04_difficulty_set")
                self.current_step += 1
                QTimer.singleShot(1000, self.next_step)

            elif self.current_step == 4:
                print("🔢 Setting question type to numerical...")
                self.set_select_value("#quiz-submode", "numerical", "question type dropdown")
                self.take_screenshot("05_question_type_set")
                self.current_step += 1
                QTimer.singleShot(1000, self.next_step)

            elif self.current_step == 5:
                print("🌊 Enabling token streaming...")
                self.check_checkbox("#token-streaming-enabled", "token streaming checkbox")
                self.take_screenshot("06_token_streaming_enabled")
                self.current_step += 1
                QTimer.singleShot(1000, self.next_step)

            elif self.current_step == 6:
                print("🚀 Clicking Start Quiz button...")
                self.execute_js("startCustomQuiz();")  # Direct function call
                self.take_screenshot("07_quiz_started")
                self.current_step += 1
                self.generation_screenshots = []
                QTimer.singleShot(3000, self.next_step)

            elif self.current_step == 7:  # Wait for generation to complete
                print("⏳ Waiting for question generation to complete...")
                print("📸 Taking generation progress screenshot...")
                self.take_screenshot("generation_in_progress")
                self.current_step += 1
                QTimer.singleShot(30000, self.next_step)  # Wait 30 seconds for generation

            elif self.current_step == 8:
                print("📸 Taking final screenshot with question loaded...")
                self.take_screenshot("question_loaded_final")
                print("\n🎉 AUTOMATED TEST COMPLETED SUCCESSFULLY!")
                print("🔔 Playing completion sound...")
                self.play_completion_sound()
                self.test_completed.emit(True, "Generated 2 screenshots for AI analysis")

        except Exception as e:
            print(f"❌ Test failed at step {self.current_step}: {e}")
            self.take_screenshot("ERROR_final")
            self.test_completed.emit(False, f"Test failed: {str(e)}")

    def check_question_loaded(self):
        """Check if a question has been loaded in the UI"""
        js_code = """
        (function() {
            // Check multiple indicators that a question has loaded
            var indicators = [];

            // 1. Check for question text
            var questionText = document.querySelector('#question-text');
            if (questionText && questionText.textContent &&
                questionText.textContent.trim() !== '' &&
                questionText.textContent.trim() !== 'Loading question...' &&
                questionText.textContent.length > 10) {
                indicators.push('question_text_found');
            }

            // 2. Check for quiz game container being visible
            var quizGame = document.querySelector('#quiz-game');
            if (quizGame && quizGame.style.display !== 'none' &&
                getComputedStyle(quizGame).display !== 'none') {
                indicators.push('quiz_game_visible');
            }

            // 3. Check for options container with actual options
            var optionsContainer = document.querySelector('#options-container');
            if (optionsContainer && optionsContainer.children.length > 1) {
                indicators.push('options_found');
            }

            // 4. Check for submit button being visible
            var submitBtn = document.querySelector('#submit-btn');
            if (submitBtn && submitBtn.style.display !== 'none') {
                indicators.push('submit_button_visible');
            }

            // 5. Check if quiz setup is hidden (means quiz started)
            var quizSetup = document.querySelector('#quiz-setup');
            if (quizSetup && quizSetup.style.display === 'none') {
                indicators.push('quiz_setup_hidden');
            }

            if (indicators.length >= 2) {
                return 'question_loaded: ' + indicators.join(', ');
            }

            return 'not_loaded: ' + indicators.join(', ');
        })();
        """

        # We'll use a simple callback to get the result
        self.question_check_result = None

        def js_callback(result):
            self.question_check_result = result

        self.app_window.page().runJavaScript(js_code, js_callback)
        QApplication.processEvents()
        time.sleep(0.3)  # Give more time for callback

        if self.question_check_result:
            print(f"🔍 Question check: {self.question_check_result}")
            if self.question_check_result.startswith('question_loaded'):
                return True
        return False

    def async_sleep(self, seconds):
        """Sleep while keeping UI responsive"""
        end_time = time.time() + seconds
        while time.time() < end_time:
            QApplication.processEvents()  # Keep UI responsive
            time.sleep(0.1)  # Small sleep to prevent busy waiting

class AutomatedTestApp:
    """Main application for running automated tests"""

    def __init__(self):
        self.app = None
        self.window = None
        self.tester = None

    def on_test_completed(self, success, message):
        """Handle test completion"""
        if success:
            print(f"\n✅ {message}")
        else:
            print(f"\n❌ {message}")

        print("🔔 Playing final completion sound...")
        try:
            # Play a distinctive completion sequence
            for freq in [800, 1000, 1200]:
                winsound.Beep(freq, 400)
                time.sleep(0.1)
            print("🔔 Final completion sound played!")
        except:
            print("🔔 *BEEP* *BEEP* *BEEP*")

        print("🔚 Auto-closing app in 3 seconds...")
        # Close app after delay
        QTimer.singleShot(3000, self.close_app)

    def close_app(self):
        """Close the application properly"""
        print("🔚 Closing application automatically...")
        try:
            if self.window:
                self.window.close()
            if self.app:
                self.app.quit()
                print("✅ Application closed successfully!")
        except Exception as e:
            print(f"⚠️ Error closing app: {e}")
            # Force exit if normal close fails
            import sys
            sys.exit(0)

    def run_test(self):
        """Run the automated test"""
        print("🤖 FULLY AUTOMATIC KNOWLEDGE APP TESTER")
        print("=" * 60)
        print("Using EXACT HTML element IDs from the codebase!")
        print("=" * 60)

        # Create QApplication
        self.app = QApplication(sys.argv)

        # Create and show the main window
        print("📱 Launching Knowledge App...")
        self.window = KnowledgeAppWebEngine()
        self.window.show()
        self.window.setWindowTitle("Knowledge App - Automated Test")

        # Create the tester
        self.tester = FullyAutomaticUITester(self.window)
        self.tester.test_completed.connect(self.on_test_completed)

        # Start test after app is fully loaded
        QTimer.singleShot(5000, self.run_automated_test)

        # Run the application
        return self.app.exec_()

    def run_automated_test(self):
        """Run the automated test in main thread"""
        self.tester.start_async_test()

def main():
    """Main entry point"""
    print("🤖 FULLY AUTOMATIC KNOWLEDGE APP TESTER")
    print("=" * 60)
    print("This will AUTOMATICALLY:")
    print("1. 🚀 Launch the Knowledge App")
    print("2. 🎯 Click Quiz navigation button")
    print("3. 📝 Enter 'atoms' in topic input (#quiz-topic)")
    print("4. 🎓 Set difficulty to 'expert' (#quiz-difficulty)")
    print("5. 🔢 Set question type to 'numerical' (#quiz-submode)")
    print("6. 🌊 Enable token streaming (#token-streaming-enabled)")
    print("7. 🚀 Click Start Quiz button")
    print("8. 📸 Take 2 KEY screenshots for AI analysis:")
    print("   - Generation in progress (30s after start)")
    print("   - Final question loaded state")
    print("9. 🔔 Play completion sound and auto-close app")
    print("=" * 60)
    print("\n✅ ADVANTAGES:")
    print("- Uses EXACT HTML element IDs from codebase")
    print("- No guessing coordinates")
    print("- Precise JavaScript automation")
    print("- Captures the REAL user experience")
    print("- CAPTURES only 2 key screenshots for direct AI analysis")
    print("- Auto-closes with completion sound")

    print("\n🚀 Starting fully automatic test in 2 seconds...")
    import time
    time.sleep(2)

    test_app = AutomatedTestApp()
    exit_code = test_app.run_test()

    print(f"\n📁 Screenshots saved in: {Path('test_screenshots').absolute()}")
    print("🔍 Check the screenshots to see exactly what happened!")

    return exit_code

if __name__ == "__main__":
    sys.exit(main())
