"""
app_health.py: Robustness utilities for dependency checking and logging.
"""

import sys
import logging
from logging.handlers import RotatingFileHandler
import importlib
import traceback
import os
from pathlib import Path

print(f"DEBUG: Starting app_health.py. sys.argv: {sys.argv}")
print(f"DEBUG: Current working directory: {os.getcwd()}")

REQUIRED_PACKAGES = [
    ("PyQt5", None),
    ("transformers", None),
    ("torch", "2.0.0"),
    ("torchvision", None),
    ("haystack", None),
    ("faiss", None),
    ("pdfplumber", None),
    ("sentence_transformers", None),
    ("python-dotenv", None),
    ("six", None),
]


def setup_logging():
    """Configure logging for the application

    Returns:
        str: Path to the log file
    """
    # Get the workspace root directory
    workspace_root = Path(__file__).parent.parent.parent

    # Create logs directory
    log_dir = workspace_root / "logs"
    log_dir.mkdir(exist_ok=True)

    # Set up log file path
    log_file_path = log_dir / "knowledge_app.log"

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)

    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create file handler
    file_handler = RotatingFileHandler(
        str(log_file_path), maxBytes=2 * 1024 * 1024, backupCount=3  # 2MB
    )
    file_handler.setLevel(logging.DEBUG)

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Create formatter
    formatter = logging.Formatter("[%(asctime)s] %(levelname)s: %(message)s")
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Add handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    logger.info("Logging system initialized")
    return str(log_file_path)


def check_dependencies():
    """Check if all required packages are installed using modern dependency checker

    Returns:
        tuple: (bool, str) indicating success and message
    """
    logger = logging.getLogger(__name__)
    logger.info("Checking dependencies with modern health checker...")

    try:
        # Use the new dependency health checker
        from knowledge_app.core.dependency_health_checker import check_dependencies as modern_check
        from knowledge_app.core.dependency_health_checker import fix_deprecated_warnings

        # Fix deprecated warnings first
        fix_deprecated_warnings()

        # Perform modern dependency check
        success = modern_check()
        if success:
            return True, "All critical dependencies are available"
        else:
            return False, "Critical dependencies missing - check logs for details"

    except ImportError:
        # Fallback to legacy checking if new checker not available
        logger.warning("Modern dependency checker not available, using legacy method")
        return _legacy_check_dependencies()


def _legacy_check_dependencies():
    """Legacy dependency checking method"""
    logger = logging.getLogger(__name__)

    for pkg, min_version in REQUIRED_PACKAGES:
        try:
            # Handle special case for python-dotenv
            if pkg == "python-dotenv":
                mod = importlib.import_module("dotenv")
            else:
                mod = importlib.import_module(pkg)

            if min_version:
                version = getattr(mod, "__version__", None)
                if version and version < min_version:
                    msg = f"{pkg} version {version} is less than required {min_version}"
                    logger.error(msg)
                    return False, msg

            logger.debug(f"Found {pkg} {getattr(mod, '__version__', 'unknown version')}")

        except ImportError as e:
            msg = f"Missing dependency: {pkg}"
            logger.error(f"{msg}. Error: {e}")
            return False, msg
        except Exception as e:
            msg = f"Error checking {pkg}: {e}"
            logger.error(msg)
            return False, msg

    logger.info("All dependencies are installed")
    return True, "All dependencies are installed."


def get_log_file_path():
    """Get the path to the log file

    Returns:
        str: Path to the log file
    """
    workspace_root = Path(__file__).parent.parent.parent
    return str(workspace_root / "logs" / "knowledge_app.log")


if __name__ == "__main__":
    log_file = setup_logging()
    print(f"Logging to: {log_file}")
    ok, msg = check_dependencies()
    print(f"Dependency check: {msg}")