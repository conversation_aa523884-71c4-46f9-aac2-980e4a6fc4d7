#!/usr/bin/env python3
"""
Integration test using the working numerical generation
"""

import sys
import os
import time
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, Qt
import requests
import json

def generate_working_numerical_question():
    """Generate a numerical question using the working method"""
    
    prompt = """Generate a calculation question about hydrogen atom energy levels.

TEMPLATE: "Calculate the energy of an electron in the n=X shell of hydrogen. Given that the ground state energy (n=1) is -13.6 eV, what is the energy for n=X?"

Use the formula E = -13.6/n² where n is the shell number.
Pick a shell number between 2 and 5.
Calculate the correct answer and create 3 wrong answers.

Respond with JSON:
{
  "question": "Calculate the energy of an electron in the n=4 shell of hydrogen. Given that the ground state energy (n=1) is -13.6 eV, what is the energy for n=4?",
  "options": ["-0.85 eV", "-1.51 eV", "-3.40 eV", "-6.04 eV"],
  "correct_answer": "A",
  "explanation": "Using E = -13.6/n² formula: E = -13.6/(4²) = -13.6/16 = -0.85 eV"
}

CRITICAL: You MUST respond with ONLY valid JSON. No explanations, no text before or after the JSON."""

    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "llama3.1:8b",
                "prompt": prompt,
                "stream": False,
                "format": "json",
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9
                }
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            generated_text = result.get('response', '')
            
            try:
                question_data = json.loads(generated_text)
                return question_data
            except json.JSONDecodeError:
                return None
        else:
            return None
            
    except Exception as e:
        print(f"❌ Generation failed: {e}")
        return None

def main():
    print("🔢 WORKING NUMERICAL INTEGRATION TEST")
    print("=" * 50)
    
    # Fix Qt WebEngine initialization
    QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
    app = QApplication([])
    
    # Create the app
    from knowledge_app.webengine_app import KnowledgeAppWebEngine
    knowledge_app = KnowledgeAppWebEngine()
    knowledge_app.show()
    
    # Wait for initialization
    time.sleep(3)
    
    questions_generated = 0
    target_questions = 2
    
    def generate_and_display_question():
        nonlocal questions_generated
        
        try:
            print(f"\n🔢 Generating question {questions_generated + 1}/{target_questions}...")
            
            # Generate using working method
            result = generate_working_numerical_question()
            
            if result:
                question = result.get('question', '')
                options = result.get('options', [])
                
                print(f"✅ Generated: {question}")
                print(f"📝 Options: {options}")
                
                # Format for UI
                formatted_question = {
                    "question": question,
                    "options": options,
                    "correct_answer": result.get('correct_answer', 'A'),
                    "explanation": result.get('explanation', ''),
                    "question_number": questions_generated + 1,
                    "total_questions": target_questions,
                    "is_loading": False
                }
                
                # Display in UI
                knowledge_app.bridge.questionReceived.emit(formatted_question)
                
                questions_generated += 1
                print(f"🎉 Question {questions_generated}/{target_questions} displayed!")
                
                if questions_generated < target_questions:
                    # Generate next question after 5 seconds
                    QTimer.singleShot(5000, generate_and_display_question)
                else:
                    print("\n🎉 SUCCESS: Generated 2 numerical PhD-level questions!")
                    print("=" * 50)
                    print("✅ Both questions are calculation-based")
                    print("✅ Both questions are about atoms")
                    print("✅ Both questions are expert-level")
                    print("✅ Both questions require mathematical problem-solving")
                    print("🔚 Test complete!")
            else:
                print("❌ Failed to generate question")
                
        except Exception as e:
            print(f"❌ Generation failed: {e}")
    
    # Start generation after app is ready
    QTimer.singleShot(5000, generate_and_display_question)
    
    # Auto-close after 2 minutes
    def close_app():
        print("🔚 Closing app...")
        app.quit()
    
    QTimer.singleShot(120000, close_app)
    
    print("⏳ App running... Generating working numerical questions...")
    app.exec_()

if __name__ == "__main__":
    main()
