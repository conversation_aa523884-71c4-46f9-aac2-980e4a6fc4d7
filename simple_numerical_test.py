#!/usr/bin/env python3
"""
Simple test to generate a numerical question and display it in the UI
"""

import sys
import os
import time
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, Qt

def main():
    print("🔢 SIMPLE NUMERICAL QUESTION TEST")
    print("=" * 50)
    
    # Fix Qt WebEngine initialization
    QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
    app = QApplication([])
    
    # Create the app
    from knowledge_app.webengine_app import KnowledgeAppWebEngine
    knowledge_app = KnowledgeAppWebEngine()
    knowledge_app.show()
    
    # Wait for initialization
    time.sleep(3)
    
    def generate_simple_numerical_question():
        try:
            print("🔢 Creating a simple numerical question directly...")
            
            # Create a hardcoded numerical question to test the UI
            numerical_question = {
                "question": "Calculate the energy of an electron in the n=3 shell of hydrogen. Given that the ground state energy (n=1) is -13.6 eV, what is the energy for n=3?",
                "options": [
                    "-1.51 eV",
                    "-3.40 eV", 
                    "-6.04 eV",
                    "-13.6 eV"
                ],
                "correct_answer": "A",
                "explanation": "Using the formula E_n = -13.6/n², for n=3: E_3 = -13.6/(3²) = -13.6/9 = -1.51 eV",
                "question_number": 1,
                "total_questions": 1,
                "is_loading": False
            }
            
            print(f"✅ Created numerical question:")
            print(f"   Question: {numerical_question['question']}")
            print(f"   Options: {numerical_question['options']}")
            print(f"   Correct: {numerical_question['correct_answer']}")
            
            # Emit directly to UI via bridge
            print("📡 Emitting question to UI...")
            knowledge_app.bridge.questionReceived.emit(numerical_question)
            
            print("🎉 Numerical question should now be displayed!")
            
        except Exception as e:
            print(f"❌ Failed to create question: {e}")
    
    # Generate question after app is ready
    QTimer.singleShot(5000, generate_simple_numerical_question)
    
    # Keep app running for 30 seconds to see the result
    def close_app():
        print("🔚 Closing app...")
        app.quit()
    
    QTimer.singleShot(35000, close_app)
    
    print("⏳ App running... Check the UI for the numerical question!")
    app.exec_()

if __name__ == "__main__":
    main()
