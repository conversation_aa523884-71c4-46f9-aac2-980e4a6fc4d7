#!/usr/bin/env python3
"""
Test exactly what the app does - simulate the thread-safe inference
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_thread_safe_inference():
    """Test the thread-safe inference exactly like the app does"""
    print("🧵 Testing Thread-Safe Inference...")
    
    try:
        from knowledge_app.core.thread_safe_inference import get_thread_safe_inference
        from knowledge_app.core.unified_inference_manager import initialize_unified_inference
        
        # Initialize the system first
        print("🔧 Initializing unified inference system...")
        init_success = initialize_unified_inference()
        if not init_success:
            print("❌ Failed to initialize")
            return False
        
        # Get the thread-safe inference wrapper
        thread_safe_inference = get_thread_safe_inference()
        
        # Test with atoms (the failing case)
        print("🧪 Testing 'atoms' with thread-safe inference...")
        
        # This is exactly what the app does
        operation_id = thread_safe_inference.generate_mcq_async(
            topic="atoms",
            difficulty="medium",
            question_type="mixed",
            mode="auto",
            timeout=30.0
        )
        
        print(f"🚀 Started operation: {operation_id}")
        
        # Wait for result (like the app does)
        import time
        max_wait = 35  # Wait a bit longer than timeout
        start_time = time.time()
        result = None
        
        while time.time() - start_time < max_wait:
            try:
                result = thread_safe_inference.get_result(operation_id, timeout=1.0)
                if result:
                    break
            except:
                pass
            time.sleep(0.5)
        
        if result:
            print(f"✅ SUCCESS with thread-safe inference!")
            print(f"   ❓ Question: {result.get('question', 'N/A')}")
            print(f"   🔢 Options: {result.get('options', [])}")
            print(f"   ✅ Correct: {result.get('correct_answer', 'N/A')}")
            return True
        else:
            print(f"❌ FAILED with thread-safe inference - No result")
            
            # Check active operations
            active_ops = thread_safe_inference.get_active_operations()
            print(f"🔍 Active operations: {active_ops}")
            return False
        
    except Exception as e:
        print(f"❌ Thread-safe test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_concurrent_requests():
    """Test multiple concurrent requests like the app might do"""
    print("\n🔄 Testing Multiple Concurrent Requests...")
    
    try:
        from knowledge_app.core.thread_safe_inference import get_thread_safe_inference
        
        thread_safe_inference = get_thread_safe_inference()
        
        # Start multiple operations like the app might
        topics = ["atoms", "physics", "chemistry", "biology"]
        operation_ids = []
        
        print(f"🚀 Starting {len(topics)} concurrent operations...")
        
        for topic in topics:
            operation_id = thread_safe_inference.generate_mcq_async(
                topic=topic,
                difficulty="medium",
                question_type="mixed",
                mode="auto",
                timeout=30.0
            )
            operation_ids.append((topic, operation_id))
            print(f"   Started: {topic} -> {operation_id}")
        
        # Wait for all results
        import time
        max_wait = 40
        start_time = time.time()
        results = {}
        
        while time.time() - start_time < max_wait and len(results) < len(topics):
            for topic, op_id in operation_ids:
                if topic not in results:
                    try:
                        result = thread_safe_inference.get_result(op_id, timeout=0.1)
                        if result:
                            results[topic] = result
                            print(f"✅ {topic}: Got result")
                    except:
                        pass
            time.sleep(0.5)
        
        print(f"\n📊 Results: {len(results)}/{len(topics)} successful")
        for topic in topics:
            if topic in results:
                print(f"   ✅ {topic}: Success")
            else:
                print(f"   ❌ {topic}: Failed")
        
        success_rate = len(results) / len(topics)
        return success_rate >= 0.5  # At least 50% success
        
    except Exception as e:
        print(f"❌ Concurrent test failed: {e}")
        return False

def test_timeout_behavior():
    """Test what happens with very short timeouts"""
    print("\n⏰ Testing Timeout Behavior...")
    
    try:
        from knowledge_app.core.thread_safe_inference import get_thread_safe_inference
        
        thread_safe_inference = get_thread_safe_inference()
        
        # Test with very short timeout
        print("🧪 Testing with 5 second timeout...")
        
        operation_id = thread_safe_inference.generate_mcq_async(
            topic="atoms",
            difficulty="medium",
            question_type="mixed",
            mode="auto",
            timeout=5.0  # Very short timeout
        )
        
        # Wait for result
        import time
        time.sleep(7)  # Wait longer than timeout
        
        try:
            result = thread_safe_inference.get_result(operation_id, timeout=0.1)
            if result:
                print(f"✅ Got result despite short timeout")
                return True
            else:
                print(f"❌ No result - timeout likely occurred")
                return False
        except:
            print(f"❌ Exception getting result - timeout likely occurred")
            return False
        
    except Exception as e:
        print(f"❌ Timeout test failed: {e}")
        return False

def main():
    print("🧵 Thread-Safe Inference Simulation")
    print("=" * 60)
    
    # Test 1: Basic thread-safe inference
    basic_works = test_thread_safe_inference()
    
    # Test 2: Multiple concurrent requests
    concurrent_works = test_multiple_concurrent_requests()
    
    # Test 3: Timeout behavior
    timeout_works = test_timeout_behavior()
    
    print(f"\n📊 SUMMARY:")
    print(f"   Basic thread-safe: {'✅' if basic_works else '❌'}")
    print(f"   Concurrent requests: {'✅' if concurrent_works else '❌'}")
    print(f"   Timeout handling: {'✅' if timeout_works else '❌'}")
    
    if basic_works:
        print(f"\n💡 The thread-safe inference works - the app error might be intermittent")
        return 0
    else:
        print(f"\n❌ Thread-safe inference has issues")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
