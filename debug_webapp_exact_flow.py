#!/usr/bin/env python3

import sys
import os
import time

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_exact_webapp_flow():
    """Test the exact flow that the web app uses with detailed error reporting"""
    
    print("🔍 TESTING EXACT WEB APP FLOW")
    print("=" * 50)
    
    try:
        # Step 1: Initialize thread-safe inference (what the web app does)
        print("\n1️⃣ Initializing Thread-Safe Inference...")
        from knowledge_app.core.thread_safe_inference import get_thread_safe_inference
        
        thread_safe_inference = get_thread_safe_inference()
        print(f"   ✅ Thread-safe inference created: {thread_safe_inference}")
        
        # Step 2: Test the exact parameters the web app uses
        print("\n2️⃣ Testing with Web App Parameters...")
        
        # These are the exact parameters from the failing web app
        topic = "atoms"  # This was in the error message
        difficulty = "medium"  # Default from settings
        question_type = "mixed"  # Default submode
        timeout = 90.0  # Default timeout for medium difficulty
        
        print(f"   Topic: {topic}")
        print(f"   Difficulty: {difficulty}")
        print(f"   Question Type: {question_type}")
        print(f"   Timeout: {timeout}")
        
        # Step 3: Start async generation (what the web app does)
        print("\n3️⃣ Starting Async Generation...")
        
        operation_id = thread_safe_inference.generate_mcq_async(
            topic=topic,
            difficulty=difficulty,
            question_type=question_type,
            timeout=timeout
        )
        
        print(f"   ✅ Operation started: {operation_id}")
        
        # Step 4: Wait for result (what the web app does)
        print("\n4️⃣ Waiting for Result...")
        
        result = thread_safe_inference.get_result(operation_id, timeout=timeout + 30.0)
        
        if result:
            print("   ✅ Generation successful!")
            print(f"   Question: {result.get('question', 'N/A')[:100]}...")
            print(f"   Options: {len(result.get('options', []))} options")
            print(f"   Correct: {result.get('correct', 'N/A')}")
            return True
        else:
            print("   ❌ Generation returned None")
            
            # Check if there are any error details
            print("\n🔍 Checking for error details...")
            
            # Check active operations
            with thread_safe_inference._lock:
                active_ops = list(thread_safe_inference._active_operations.keys())
                print(f"   Active operations: {active_ops}")
                
                if operation_id in thread_safe_inference._active_operations:
                    op_info = thread_safe_inference._active_operations[operation_id]
                    print(f"   Operation info: {op_info}")
                    
                    # Check if the future has an exception
                    future = op_info.get('future')
                    if future and future.done():
                        try:
                            future_result = future.result()
                            print(f"   Future result: {future_result}")
                        except Exception as e:
                            print(f"   Future exception: {e}")
                            import traceback
                            traceback.print_exc()
            
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_generate_mcq_unified_directly():
    """Test the generate_mcq_unified function directly"""
    
    print("\n" + "=" * 60)
    print("TESTING generate_mcq_unified DIRECTLY")
    print("=" * 60)
    
    try:
        from knowledge_app.core.unified_inference_manager import generate_mcq_unified
        
        print("🔄 Calling generate_mcq_unified directly...")
        
        result = generate_mcq_unified(
            topic="atoms",
            difficulty="medium",
            question_type="mixed",
            timeout=30.0
        )
        
        if result:
            print("✅ Direct call successful!")
            print(f"Question: {result.get('question', 'N/A')[:100]}...")
            return True
        else:
            print("❌ Direct call returned None")
            return False
            
    except Exception as e:
        print(f"❌ Direct call failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    
    print("Testing the exact web app flow to find why it's failing...\n")
    
    # Test 1: Direct unified inference
    direct_works = test_generate_mcq_unified_directly()
    
    # Test 2: Thread-safe wrapper (what web app uses)
    wrapper_works = test_exact_webapp_flow()
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print('='*60)
    
    print(f"Direct generate_mcq_unified: {'✅ WORKS' if direct_works else '❌ FAILS'}")
    print(f"Thread-safe wrapper: {'✅ WORKS' if wrapper_works else '❌ FAILS'}")
    
    if direct_works and not wrapper_works:
        print("\n🔍 DIAGNOSIS: The core generation works, but the thread-safe wrapper is failing.")
        print("This suggests an issue in the threading/async handling.")
    elif not direct_works:
        print("\n🔍 DIAGNOSIS: The core generation itself is failing.")
        print("This suggests an issue in the unified inference manager or model setup.")
    elif direct_works and wrapper_works:
        print("\n🔍 DIAGNOSIS: Both work in isolation. The issue might be in the web app's Qt integration.")
    else:
        print("\n🔍 DIAGNOSIS: Both are failing. There's a fundamental issue with the AI setup.")

if __name__ == "__main__":
    main()
