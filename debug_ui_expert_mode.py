#!/usr/bin/env python3
"""
Debug UI Expert Mode Issue
Check why the UI is generating basic questions instead of PhD-level content
"""

import asyncio
import logging

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

async def debug_ui_expert_mode():
    """Debug the UI expert mode issue"""
    print("🔍 DEBUGGING UI EXPERT MODE ISSUE")
    print("=" * 60)
    
    try:
        # Test the exact same parameters the UI would send
        from src.knowledge_app.core.mcq_manager import get_mcq_manager
        
        mcq_manager = get_mcq_manager()
        
        # These are the EXACT parameters the UI sends for expert mode
        ui_quiz_params = {
            "topic": "fluid dynamics",  # Same topic as shown in UI
            "difficulty": "expert",     # Expert mode selected
            "game_mode": "casual",      # Default from UI
            "submode": "numerical",     # Numerical selected in UI
            "num_questions": 1
        }
        
        print(f"🧪 Testing with UI parameters: {ui_quiz_params}")
        print("\n🔄 Generating question...")
        
        # Generate using the same method the UI uses
        result = await mcq_manager.generate_quiz_async(ui_quiz_params)
        
        if result and hasattr(result, 'question'):
            question = result.question
            options = result.options
            explanation = result.explanation
            
            print("\n" + "=" * 60)
            print("📝 GENERATED QUESTION:")
            print("=" * 60)
            print(f"Question: {question}")
            print(f"Length: {len(question)} characters")
            print(f"\nOptions:")
            for i, option in enumerate(options):
                print(f"  {chr(65+i)}) {option}")
            print(f"\nExplanation: {explanation}")
            
            # Check if it's PhD-level
            print("\n" + "=" * 60)
            print("🎓 PhD-LEVEL ANALYSIS:")
            print("=" * 60)
            
            # Length check
            if len(question) >= 120:
                print(f"✅ Length: {len(question)} chars (≥120 for PhD-level)")
            else:
                print(f"❌ Length: {len(question)} chars (<120, not PhD-level)")
            
            # Advanced terminology check
            advanced_terms = [
                'mechanism', 'theoretical', 'framework', 'analysis', 'synthesis',
                'differential', 'integral', 'quantum', 'molecular', 'thermodynamic',
                'kinetic', 'potential', 'equilibrium', 'entropy', 'enthalpy',
                'viscosity', 'turbulence', 'laminar', 'reynolds', 'navier-stokes'
            ]
            
            found_terms = [term for term in advanced_terms if term.lower() in question.lower()]
            if found_terms:
                print(f"✅ Advanced terms found: {found_terms}")
            else:
                print(f"❌ No advanced terms found in question")
            
            # Check generation method
            if hasattr(result, 'generation_method'):
                method = result.generation_method
                print(f"🔧 Generation method: {method}")
                if 'deepseek' in method.lower():
                    print("✅ DeepSeek pipeline was used")
                else:
                    print("❌ DeepSeek pipeline was NOT used")
            else:
                print("⚠️ No generation method metadata available")
            
            # Overall assessment
            is_phd_level = (len(question) >= 120 and len(found_terms) > 0)
            if is_phd_level:
                print("\n🎉 RESULT: This IS PhD-level content!")
            else:
                print("\n❌ RESULT: This is NOT PhD-level content!")
                print("   The UI expert mode is not working properly.")
            
            return is_phd_level
            
        else:
            print("❌ No question generated!")
            return False
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_deepseek_directly():
    """Test DeepSeek pipeline directly to see if it works"""
    print("\n" + "=" * 60)
    print("🧠 TESTING DEEPSEEK PIPELINE DIRECTLY")
    print("=" * 60)
    
    try:
        from src.knowledge_app.core.deepseek_integration import get_deepseek_pipeline
        
        pipeline = get_deepseek_pipeline()
        
        if not pipeline.is_ready():
            print("❌ DeepSeek pipeline is not ready!")
            return False
        
        print("✅ DeepSeek pipeline is ready")
        
        # Test direct generation
        print("🔄 Generating PhD-level question directly...")
        
        result = pipeline.generate_expert_question(
            topic="fluid dynamics",
            difficulty="expert",
            question_type="numerical",
            context=None,
            progress_callback=lambda msg: print(f"   📝 {msg}")
        )
        
        if result:
            question = result.get('question', '')
            print(f"\n✅ Direct DeepSeek generation successful!")
            print(f"📝 Question: {question[:100]}...")
            print(f"📏 Length: {len(question)} characters")
            return True
        else:
            print("❌ Direct DeepSeek generation failed!")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek direct test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main debug function"""
    print("🚀 STARTING UI EXPERT MODE DEBUG")
    
    # Test 1: UI parameters
    ui_works = await debug_ui_expert_mode()
    
    # Test 2: Direct DeepSeek
    deepseek_works = await test_deepseek_directly()
    
    print("\n" + "=" * 60)
    print("🏆 DEBUG SUMMARY")
    print("=" * 60)
    
    if ui_works:
        print("✅ UI Expert Mode: Working correctly")
    else:
        print("❌ UI Expert Mode: NOT working")
    
    if deepseek_works:
        print("✅ DeepSeek Pipeline: Working correctly")
    else:
        print("❌ DeepSeek Pipeline: NOT working")
    
    if not ui_works and deepseek_works:
        print("\n🔧 DIAGNOSIS: DeepSeek works but UI integration is broken")
        print("   The issue is in the MCQ manager's expert mode detection or routing")
    elif not ui_works and not deepseek_works:
        print("\n🔧 DIAGNOSIS: DeepSeek pipeline itself is broken")
        print("   Need to fix the DeepSeek integration first")
    elif ui_works:
        print("\n🎉 DIAGNOSIS: Everything is working! The UI issue might be elsewhere")
    
    return ui_works

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
