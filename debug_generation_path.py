#!/usr/bin/env python3
"""
Debug which generation path is actually being used in the UI
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

import logging
logging.basicConfig(level=logging.INFO, format='%(name)s - %(levelname)s - %(message)s')

def test_generation_paths():
    """Test all possible generation paths to see which one is being used"""
    
    print("🔍 DEBUGGING GENERATION PATHS")
    print("=" * 60)
    
    # Test 1: Direct unified inference (my PhD override)
    print("\n1️⃣ Testing direct unified inference (PhD override)...")
    try:
        from knowledge_app.core.unified_inference_manager import generate_mcq_unified
        result = generate_mcq_unified('atoms', 'expert', 'numerical')
        if result:
            question = result.get('question', '')
            print(f"✅ Unified inference: {question[:100]}...")
            
            # Check if it's PhD-level
            phd_terms = ['relativistic', 'dirac', 'perturbation', 'fine structure', 'hyperfine']
            is_phd = any(term in question.lower() for term in phd_terms)
            print(f"🎓 PhD-level: {'YES' if is_phd else 'NO'}")
        else:
            print("❌ Unified inference failed")
    except Exception as e:
        print(f"❌ Unified inference error: {e}")
    
    # Test 2: MCQ Manager (what UI actually uses)
    print("\n2️⃣ Testing MCQ Manager (UI path)...")
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        mcq_manager = get_mcq_manager()
        
        quiz_params = {
            "topic": "atoms",
            "difficulty": "expert", 
            "game_mode": "casual",
            "submode": "numerical",
            "num_questions": 1
        }
        
        result = mcq_manager.generate_quiz(quiz_params)
        if result:
            question = result.get('question', '')
            print(f"✅ MCQ Manager: {question[:100]}...")
            
            # Check if it's PhD-level
            phd_terms = ['relativistic', 'dirac', 'perturbation', 'fine structure', 'hyperfine']
            is_phd = any(term in question.lower() for term in phd_terms)
            print(f"🎓 PhD-level: {'YES' if is_phd else 'NO'}")
        else:
            print("❌ MCQ Manager failed")
    except Exception as e:
        print(f"❌ MCQ Manager error: {e}")
    
    # Test 3: Thread-safe inference (FastQuestionGenerator path)
    print("\n3️⃣ Testing thread-safe inference (FastQuestionGenerator path)...")
    try:
        from knowledge_app.core.thread_safe_inference import get_thread_safe_inference
        thread_safe_inference = get_thread_safe_inference()
        
        operation_id = thread_safe_inference.generate_mcq_async(
            topic="atoms",
            difficulty="expert",
            question_type="numerical",
            timeout=180.0
        )
        
        result = thread_safe_inference.get_result(operation_id, timeout=200.0)
        if result:
            question = result.get('question', '')
            print(f"✅ Thread-safe inference: {question[:100]}...")
            
            # Check if it's PhD-level
            phd_terms = ['relativistic', 'dirac', 'perturbation', 'fine structure', 'hyperfine']
            is_phd = any(term in question.lower() for term in phd_terms)
            print(f"🎓 PhD-level: {'YES' if is_phd else 'NO'}")
        else:
            print("❌ Thread-safe inference failed")
    except Exception as e:
        print(f"❌ Thread-safe inference error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 CONCLUSION:")
    print("- If unified inference shows PhD-level but others don't,")
    print("  then the UI is using a different generation path")
    print("- Need to add PhD override to the path that actually works")

if __name__ == "__main__":
    test_generation_paths()
