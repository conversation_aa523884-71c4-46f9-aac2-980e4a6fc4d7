#!/usr/bin/env python3
"""
Test UI responsiveness during quiz generation
"""

import sys
import os
import json
import time
import threading
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_ui_responsiveness():
    """Test that quiz generation doesn't block the UI"""
    print("🖥️ Testing UI Responsiveness During Quiz Generation...")
    
    try:
        from knowledge_app.webengine_app import PythonBridge
        
        # Create bridge
        bridge = PythonBridge()
        
        # Test parameters for expert mode (most intensive)
        quiz_params = {
            "topic": "atoms",
            "difficulty": "expert",  # This should use DeepSeek
            "mode": "auto",
            "game_mode": "serious",
            "submode": "mixed",
            "num_questions": 1,
            "timer": "30s"
        }
        
        print(f"🧪 Starting expert quiz with params: {quiz_params}")
        
        # Measure time for quiz start (should be instant)
        start_time = time.time()
        
        # This should return immediately without blocking
        bridge.startQuiz(json.dumps(quiz_params))
        
        end_time = time.time()
        startup_time = end_time - start_time
        
        print(f"⏱️ Quiz start time: {startup_time:.3f} seconds")
        
        if startup_time < 1.0:
            print("✅ UI RESPONSIVENESS: EXCELLENT - Quiz starts instantly")
            return True
        elif startup_time < 3.0:
            print("⚠️ UI RESPONSIVENESS: GOOD - Quiz starts quickly")
            return True
        else:
            print("❌ UI RESPONSIVENESS: POOR - Quiz start is slow")
            return False
            
    except Exception as e:
        print(f"❌ UI responsiveness test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_background_generation():
    """Test that generation happens in background"""
    print("\n🔄 Testing Background Generation...")
    
    try:
        from knowledge_app.webengine_app import PythonBridge
        
        # Create bridge
        bridge = PythonBridge()
        
        # Start a quiz
        quiz_params = {
            "topic": "atoms",
            "difficulty": "expert",
            "mode": "auto",
            "game_mode": "serious",
            "submode": "mixed",
            "num_questions": 1,
            "timer": "30s"
        }
        
        print("🚀 Starting quiz...")
        bridge.startQuiz(json.dumps(quiz_params))
        
        # Check if we can still call other methods (UI not frozen)
        print("🧪 Testing if UI is still responsive...")
        
        for i in range(5):
            try:
                # Try to call a simple method
                bridge.getApiStatus()  # This should work if UI is responsive
                print(f"   📊 API status check {i+1}: UI responsive")
                time.sleep(0.5)
            except Exception as e:
                print(f"   ❌ UI frozen at check {i+1}: {e}")
                return False
        
        print("✅ UI remains responsive during background generation")
        return True
        
    except Exception as e:
        print(f"❌ Background generation test failed: {e}")
        return False

def main():
    print("🖥️ UI Responsiveness Test")
    print("=" * 60)
    
    # Test 1: UI responsiveness during quiz start
    ui_responsive = test_ui_responsiveness()
    
    # Test 2: Background generation
    background_works = test_background_generation()
    
    print(f"\n📊 UI RESPONSIVENESS RESULTS:")
    print(f"   Quiz Start Speed: {'✅' if ui_responsive else '❌'}")
    print(f"   Background Generation: {'✅' if background_works else '❌'}")
    
    if ui_responsive and background_works:
        print(f"\n🎉 UI RESPONSIVENESS: PERFECT!")
        print(f"   ✅ Quiz starts instantly without blocking")
        print(f"   ✅ Generation happens in background")
        print(f"   ✅ UI remains responsive during generation")
        return 0
    else:
        print(f"\n❌ UI responsiveness needs improvement")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
