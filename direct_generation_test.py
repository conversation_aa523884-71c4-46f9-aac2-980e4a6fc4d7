#!/usr/bin/env python3
"""
Direct generation test - bypass UI and test generation directly
"""

import sys
import os
import time
import json
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_offline_generation():
    """Test offline generation directly"""
    try:
        print("🔥 DIRECT OFFLINE GENERATION TEST")
        
        # Import the offline generator
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator
        
        # Create generator
        generator = OfflineMCQGenerator()
        print("✅ Offline generator created")
        
        # Test generation
        print("🚀 Generating question about atoms...")
        result = generator.generate_mcq(
            topic="atoms",
            difficulty="expert", 
            question_type="numerical",
            context="atomic structure and properties"
        )
        
        if result:
            print("✅ Question generated successfully!")
            print(f"📝 Question: {result.get('question', 'N/A')}")
            print(f"🔢 Options: {result.get('options', 'N/A')}")
            print(f"✅ Correct: {result.get('correct', 'N/A')}")
            print(f"💡 Explanation: {result.get('explanation', 'N/A')}")
            return True
        else:
            print("❌ No question generated")
            return False
            
    except Exception as e:
        print(f"❌ Offline generation failed: {e}")
        return False

def test_online_generation():
    """Test online generation directly"""
    try:
        print("\n🌐 DIRECT ONLINE GENERATION TEST")
        
        # Import the online generator
        from knowledge_app.core.online_mcq_generator import OnlineMCQGenerator
        
        # Create generator
        generator = OnlineMCQGenerator()
        print("✅ Online generator created")
        
        # Test generation
        print("🚀 Generating question about atoms...")
        result = generator.generate_mcq(
            topic="atoms",
            difficulty="expert",
            question_type="numerical", 
            context="atomic structure and properties"
        )
        
        if result:
            print("✅ Question generated successfully!")
            print(f"📝 Question: {result.get('question', 'N/A')}")
            print(f"🔢 Options: {result.get('options', 'N/A')}")
            print(f"✅ Correct: {result.get('correct', 'N/A')}")
            print(f"💡 Explanation: {result.get('explanation', 'N/A')}")
            return True
        else:
            print("❌ No question generated")
            return False
            
    except Exception as e:
        print(f"❌ Online generation failed: {e}")
        return False

def test_unified_manager():
    """Test the unified inference manager"""
    try:
        print("\n🔧 UNIFIED MANAGER TEST")

        from knowledge_app.core.unified_inference_manager import UnifiedInferenceManager, initialize_unified_inference

        # Initialize the manager first
        print("🔧 Initializing unified manager...")
        success = initialize_unified_inference()
        if not success:
            print("❌ Failed to initialize unified manager")
            return False
        print("✅ Unified manager initialized")

        # Create manager
        manager = UnifiedInferenceManager()
        print("✅ Unified manager created")

        # Test generation using correct method
        print("🚀 Generating question via unified manager...")
        result = manager.generate_mcq_sync(
            topic="atoms",
            difficulty="expert",
            question_type="numerical"
        )

        if result:
            print("✅ Question generated successfully!")
            print(f"📝 Question: {result.get('question', 'N/A')}")
            print(f"🔢 Options: {result.get('options', 'N/A')}")
            print(f"✅ Correct: {result.get('correct', 'N/A')}")
            print(f"💡 Explanation: {result.get('explanation', 'N/A')}")
            return True
        else:
            print("❌ No question generated")
            return False

    except Exception as e:
        print(f"❌ Unified manager failed: {e}")
        return False

def test_ollama_status():
    """Check if Ollama is running"""
    try:
        print("\n🔍 OLLAMA STATUS CHECK")
        import requests

        response = requests.get("http://localhost:11434/api/tags", timeout=3)
        if response.status_code == 200:
            data = response.json()
            models = data.get("models", [])
            print(f"✅ Ollama is running with {len(models)} models")
            if models:
                model_names = [m.get('name', 'unknown') for m in models[:3]]
                print(f"📋 Available models: {', '.join(model_names)}")
                if len(models) > 3:
                    print(f"   ... and {len(models) - 3} more models")
            return True
        else:
            print(f"❌ Ollama responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama not available: {e}")
        return False

def main():
    print("🧪 DIRECT GENERATION TESTING")
    print("=" * 50)

    # Check Ollama status first
    ollama_running = test_ollama_status()

    # Test all generation methods
    offline_success = test_offline_generation()
    online_success = test_online_generation()
    unified_success = test_unified_manager()

    print("\n📊 RESULTS:")
    print(f"🔍 Ollama: {'✅ RUNNING' if ollama_running else '❌ NOT RUNNING'}")
    print(f"🔥 Offline: {'✅ SUCCESS' if offline_success else '❌ FAILED'}")
    print(f"🌐 Online: {'✅ SUCCESS' if online_success else '❌ FAILED'}")
    print(f"🔧 Unified: {'✅ SUCCESS' if unified_success else '❌ FAILED'}")

    if any([offline_success, online_success, unified_success]):
        print("\n🎉 At least one generation method works!")
    else:
        print("\n💥 All generation methods failed!")
        if not ollama_running:
            print("💡 Try starting Ollama: ollama serve")

if __name__ == "__main__":
    main()
