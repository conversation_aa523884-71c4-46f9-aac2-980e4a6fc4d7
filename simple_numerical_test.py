#!/usr/bin/env python3
"""
Simple test to generate a numerical question and display it in the UI
"""

import sys
import os
import time
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, Qt

def main():
    print("🔢 SIMPLE NUMERICAL QUESTION TEST")
    print("=" * 50)
    
    # Fix Qt WebEngine initialization
    QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
    app = QApplication([])
    
    # Create the app
    from knowledge_app.webengine_app import KnowledgeAppWebEngine
    knowledge_app = KnowledgeAppWebEngine()
    knowledge_app.show()
    
    # Wait for initialization
    time.sleep(3)
    
    def generate_simple_numerical_question():
        try:
            print("🔢 Generating REAL numerical question using working generator...")

            # Use the working Mathstral model directly with forced numerical prompts
            from knowledge_app.core.ollama_json_generator import <PERSON>llamaJ<PERSON>NGenerator

            generator = OllamaJSONGenerator()
            if not generator.initialize():
                print("❌ Failed to initialize generator")
                return

            print(f"🔧 Using model: {generator.active_model}")

            # Force numerical generation with explicit prompt
            numerical_prompt = """Create a NUMERICAL multiple choice question about atoms that requires CALCULATION.

CRITICAL: This MUST be a numerical question with:
- Specific numbers in the question
- Mathematical calculation required
- Units (eV, nm, Hz, etc.)
- Formula application

Example: "Calculate the energy of an electron in the n=2 shell of hydrogen..."

Create a question about atomic energy levels, wavelengths, or binding energies.

Output JSON format:
{
  "question": "Calculate the [specific numerical problem]?",
  "options": ["numerical answer with units", "numerical answer with units", "numerical answer with units", "numerical answer with units"],
  "correct_answer": "A",
  "explanation": "calculation steps and formula"
}"""

            # Call the generator directly with numerical enforcement
            response = generator._call_ollama_api(numerical_prompt)
            if response:
                parsed = generator._parse_single_json(response)
                if parsed:
                    numerical_question = {
                        "question": parsed.get('question', 'Generated question'),
                        "options": parsed.get('options', ['A', 'B', 'C', 'D']),
                        "correct_answer": parsed.get('correct_answer', 'A'),
                        "explanation": parsed.get('explanation', 'Generated explanation'),
                        "question_number": 1,
                        "total_questions": 1,
                        "is_loading": False
                    }
                else:
                    print("❌ Failed to parse JSON, using fallback")
                    numerical_question = create_fallback_question()
            else:
                print("❌ No response from generator, using fallback")
                numerical_question = create_fallback_question()
            
            print(f"✅ Created numerical question:")
            print(f"   Question: {numerical_question['question']}")
            print(f"   Options: {numerical_question['options']}")
            print(f"   Correct: {numerical_question['correct_answer']}")
            
            # Emit directly to UI via bridge
            print("📡 Emitting question to UI...")
            knowledge_app.bridge.questionReceived.emit(numerical_question)
            
            print("🎉 Numerical question should now be displayed!")
            
        except Exception as e:
            print(f"❌ Failed to create question: {e}")
    
    # Generate question after app is ready
    QTimer.singleShot(5000, generate_simple_numerical_question)
    
    # Keep app running for 30 seconds to see the result
    def close_app():
        print("🔚 Closing app...")
        app.quit()
    
    QTimer.singleShot(35000, close_app)
    
    print("⏳ App running... Check the UI for the numerical question!")
    app.exec_()

if __name__ == "__main__":
    main()
