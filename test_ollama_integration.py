#!/usr/bin/env python3
"""
Test Ollama integration for quiz generation
"""

import sys
import os
import json
import requests
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_ollama_direct():
    """Test direct Ollama API call"""
    print("🔍 Testing direct Ollama API...")
    
    try:
        # Test if Ollama is running
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ Ollama is running with {len(models.get('models', []))} models")
            for model in models.get('models', [])[:3]:
                print(f"   - {model['name']}")
            return True
        else:
            print(f"❌ Ollama API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama API test failed: {e}")
        return False

def test_ollama_generation():
    """Test Ollama question generation"""
    print("\n🔍 Testing Ollama question generation...")
    
    try:
        prompt = """Generate a multiple choice question about physics in JSON format:
{
  "question": "...",
  "options": ["A", "B", "C", "D"],
  "correct": "A",
  "explanation": "..."
}"""

        payload = {
            "model": "llama3.1:8b",
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.1,
                "top_p": 0.9
            }
        }
        
        response = requests.post("http://localhost:11434/api/generate", 
                               json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('response', '')
            print(f"✅ Generated response ({len(content)} chars)")
            print(f"📝 Preview: {content[:200]}...")
            return True
        else:
            print(f"❌ Generation failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Generation test failed: {e}")
        return False

def test_app_config():
    """Test app configuration"""
    print("\n🔍 Testing app configuration...")
    
    try:
        config_path = Path("config/unified_config.json")
        if config_path.exists():
            with open(config_path) as f:
                config = json.load(f)
            
            inference_mode = config.get('inference', {}).get('mode', 'unknown')
            mcq_type = config.get('mcq', {}).get('mcq_generator_type', 'unknown')
            
            print(f"✅ Config loaded:")
            print(f"   - Inference mode: {inference_mode}")
            print(f"   - MCQ generator: {mcq_type}")
            
            if inference_mode == "ollama_json" and mcq_type == "ollama_json":
                print("✅ Configuration is set for Ollama")
                return True
            else:
                print("⚠️ Configuration may not be optimal for Ollama")
                return False
        else:
            print("❌ Config file not found")
            return False
            
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

def test_app_imports():
    """Test app imports"""
    print("\n🔍 Testing app imports...")
    
    try:
        from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator
        print("✅ OllamaJSONGenerator import successful")
        
        from knowledge_app.core.unified_inference_manager import get_unified_inference_manager
        print("✅ Unified inference manager import successful")
        
        return True
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def main():
    print("🚀 Ollama Integration Test")
    print("=" * 50)
    
    tests = [
        ("Ollama Direct API", test_ollama_direct),
        ("Ollama Generation", test_ollama_generation),
        ("App Configuration", test_app_config),
        ("App Imports", test_app_imports)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Ollama integration should work.")
    elif passed >= 2:
        print("⚠️ Some tests failed, but basic functionality should work.")
    else:
        print("❌ Major issues detected. Check Ollama installation.")
    
    return 0 if passed >= 2 else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
