#!/usr/bin/env python3
"""
Test DeepSeek expert mode generation specifically
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_deepseek_integration():
    """Test DeepSeek integration directly"""
    print("🧠 Testing DeepSeek Integration...")
    
    try:
        from knowledge_app.core.deepseek_integration import get_deepseek_pipeline
        
        # Get the pipeline
        pipeline = get_deepseek_pipeline()
        if not pipeline:
            print("❌ Failed to get DeepSeek pipeline")
            return False
        
        print(f"✅ DeepSeek pipeline created")
        print(f"   🧠 Thinking model: {pipeline.thinking_model}")
        print(f"   📝 JSON model: {pipeline.json_model}")
        print(f"   📋 Available models: {pipeline.available_models}")
        
        # Check if ready
        if not pipeline.is_ready():
            print("❌ DeepSeek pipeline not ready")
            return False
        
        print("✅ DeepSeek pipeline is ready")
        
        # Test generation
        print("🧪 Testing expert question generation...")
        
        def progress_callback(message):
            print(f"   📊 {message}")
        
        result = pipeline.generate_expert_question(
            topic="atoms",
            difficulty="expert",
            progress_callback=progress_callback
        )
        
        if result:
            print("✅ DeepSeek generation successful!")
            print(f"   ❓ Question: {result.get('question', 'N/A')}")
            print(f"   🔢 Options: {result.get('options', {})}")
            print(f"   ✅ Correct: {result.get('correct', 'N/A')}")
            print(f"   📝 Explanation: {result.get('explanation', 'N/A')}")
            return True
        else:
            print("❌ DeepSeek generation failed - no result")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_webengine_deepseek():
    """Test DeepSeek through webengine bridge"""
    print("\n🌐 Testing WebEngine DeepSeek Bridge...")
    
    try:
        from knowledge_app.webengine_app import PythonBridge
        
        # Create bridge
        bridge = PythonBridge()
        
        # Test DeepSeek status
        status_json = bridge.getDeepSeekStatus()
        status = json.loads(status_json)
        
        print(f"📊 DeepSeek Status: {status}")
        
        if not status.get('available', False):
            print("❌ DeepSeek not available through bridge")
            return False
        
        if not status.get('ready', False):
            print("❌ DeepSeek not ready through bridge")
            return False
        
        print("✅ DeepSeek available and ready through bridge")
        
        # Test generation through bridge
        print("🧪 Testing generation through bridge...")
        
        result_json = bridge.generateDeepSeekQuestion("atoms", "expert")
        result = json.loads(result_json)
        
        print(f"📋 Bridge result: {result}")
        
        if result.get('success', False):
            print("✅ Bridge generation successful!")
            question_data = result.get('question', {})
            print(f"   ❓ Question: {question_data.get('question', 'N/A')}")
            return True
        else:
            print(f"❌ Bridge generation failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Bridge test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_expert_mode_flow():
    """Test the complete expert mode flow"""
    print("\n🎓 Testing Complete Expert Mode Flow...")

    try:
        # Test unified inference with expert difficulty
        from knowledge_app.core.unified_inference_manager import generate_mcq_unified, initialize_unified_inference

        print("🔧 Initializing unified inference system...")
        init_success = initialize_unified_inference()
        if not init_success:
            print("❌ Failed to initialize unified inference")
            return False

        print("🧪 Testing unified inference with expert difficulty...")

        result = generate_mcq_unified(
            topic="atoms",
            difficulty="expert",
            question_type="mixed",
            timeout=60.0
        )

        if result:
            print("✅ Expert mode unified generation successful!")
            print(f"   ❓ Question: {result.get('question', 'N/A')}")
            print(f"   🔢 Options: {result.get('options', [])}")
            print(f"   ✅ Correct: {result.get('correct_answer', 'N/A')}")
            return True
        else:
            print("❌ Expert mode unified generation failed")
            return False

    except Exception as e:
        print(f"❌ Expert mode flow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🧠 DeepSeek Expert Mode Debug")
    print("=" * 60)
    
    # Test 1: Direct DeepSeek integration
    deepseek_works = test_deepseek_integration()
    
    # Test 2: WebEngine bridge
    bridge_works = test_webengine_deepseek()
    
    # Test 3: Complete expert mode flow
    expert_flow_works = test_expert_mode_flow()
    
    print(f"\n📊 SUMMARY:")
    print(f"   DeepSeek Integration: {'✅' if deepseek_works else '❌'}")
    print(f"   WebEngine Bridge: {'✅' if bridge_works else '❌'}")
    print(f"   Expert Mode Flow: {'✅' if expert_flow_works else '❌'}")
    
    if deepseek_works and bridge_works and expert_flow_works:
        print(f"\n🎉 Expert mode is working!")
        return 0
    else:
        print(f"\n❌ Expert mode needs fixing")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
