#!/usr/bin/env python3
"""
Test GPU-optimized DeepSeek R1 with aggressive GPU settings
"""

import sys
import os
import logging
import time
import requests
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_gpu_optimized_deepseek():
    """Test DeepSeek with aggressive GPU optimization"""
    try:
        print("🚀 Testing GPU-Optimized DeepSeek R1")
        print("=" * 50)
        
        # Aggressive GPU-only parameters
        gpu_optimized_params = {
            'temperature': 0.3,
            'top_p': 0.9,
            'top_k': 50,
            'num_predict': 512,         # VERY SHORT for speed test
            'num_ctx': 2048,            # SMALL context for speed
            'repeat_penalty': 1.1,
            'n_gpu_layers': -1,         # Full GPU
            'main_gpu': 0,              # Primary GPU
            'num_thread': 1,            # MINIMAL CPU usage
            'num_batch': 1024,          # Large GPU batch
            'use_mlock': True,          # Lock in GPU memory
            'use_mmap': False,          # No memory mapping
            'low_vram': False,          # Use full VRAM
        }
        
        # Simple, fast prompt for testing
        test_prompt = """Create a simple physics question about force. Respond with ONLY this JSON format:
{
  "question": "What is the unit of force?",
  "options": ["Newton", "Joule", "Watt", "Pascal"],
  "correct_answer": "Newton",
  "explanation": "Force is measured in Newtons."
}"""
        
        payload = {
            "model": "deepseek-r1:14b",  # 🚀 MUCH FASTER: 14B instead of 32B
            "prompt": test_prompt,
            "stream": False,
            "keep_alive": "1m",  # Short keep-alive
            "options": gpu_optimized_params
        }
        
        print("🔥 Making GPU-optimized API call...")
        print(f"🚀 GPU layers: {gpu_optimized_params['n_gpu_layers']}")
        print(f"🚀 CPU threads: {gpu_optimized_params['num_thread']}")
        print(f"🚀 Max tokens: {gpu_optimized_params['num_predict']}")
        print(f"🚀 Batch size: {gpu_optimized_params['num_batch']}")
        
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=payload,
            timeout=30  # Short timeout
        )
        
        api_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API call completed in {api_time:.1f}s")
            print("=" * 50)
            print("Response:")
            print(result.get('response', 'No response'))
            print("=" * 50)
            return True
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_gpu_usage():
    """Check current GPU usage"""
    try:
        print("\n🔍 Checking GPU status...")
        
        # Check what models are loaded
        response = requests.get("http://localhost:11434/api/ps")
        if response.status_code == 200:
            models = response.json()
            print(f"Loaded models: {models}")
        
    except Exception as e:
        print(f"❌ Error checking GPU: {e}")

if __name__ == "__main__":
    print("🚀 GPU-Optimized DeepSeek Test")
    print("=" * 60)
    
    # Check initial state
    check_gpu_usage()
    
    # Test GPU optimization
    success = test_gpu_optimized_deepseek()
    
    if success:
        print("\n🎉 GPU-optimized test PASSED!")
    else:
        print("\n❌ GPU-optimized test FAILED!")
    
    # Check final state
    check_gpu_usage()
    
    print("=" * 60)
