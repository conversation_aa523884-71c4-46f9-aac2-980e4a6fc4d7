{"include_extensions": [".py", ".tsx", ".ts", ".qml", "Dockerfile"], "skip_directories": ["models", "data", "uploaded_books", "lora_adapters_mistral", "lora_adapters", "automation_screenshots", "generated_images", "resources", "wheels", "cache", "image_cache", "fire_cache", "oracle_cache", "memory_cache", "test_cache", "test_fire_cache", "test_oracle_cache", "test_models", "nltk_data_fixed"], "skip_file_patterns": ["*.bin", "*.dat", "*.pkl", "*.pickle", "*.pt", "*.safetensors", "*.gguf", "*.ggml", "*.tar", "*.gz", "*.zip", "*.rar", "*.7z", "*.png", "*.jpg", "*.jpeg", "*.gif", "*.bmp", "*.ico", "*.mp4", "*.mp3", "*.wav", "*.avi", "*.mov", "*.pdf", "*.doc", "*.docx", "*.xls", "*.xlsx", "*.sqlite3", "*.sqlite", "*.db", "*.log", "complete.txt", "massive_code_collection.txt", "knowledge_app*_analysis.txt"], "size_limit_mb": 10}