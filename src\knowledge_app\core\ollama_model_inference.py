from .async_converter import async_requests_post, async_requests_get


from .async_converter import async_requests_post, async_requests_get


import logging
import requests
import json
from typing import Dict, Any, List, Optional
import time

logger = logging.getLogger(__name__)


class OllamaModelInference:
    """
    A robust and optimized inference client for a local Ollama server.
    This connects to an existing Ollama instance and automatically selects
    the best available model with GPU optimization for maximum speed.
    """

    def __init__(self, ollama_url: str = "http://localhost:11434", preferred_models: Optional[List[str]] = None):
        self.base_url = ollama_url
        self.ollama_api_url = f"{self.base_url}/api/generate"
        self.preferred_models = preferred_models or ["llama3.1", "llama3", "llama2", "phi3"]
        self.active_model: Optional[str] = None
        
        # GPU optimization parameters for maximum performance
        self.generation_params = {
            'temperature': 0.8,  # Balanced creativity/speed
            'top_p': 0.9,
            'top_k': 30,  # Reduced for faster generation
            'num_predict': 400,  # Shorter responses for speed
            'num_ctx': 2048,  # Smaller context window for speed
            'repeat_penalty': 1.1,
            'seed': -1,  # Random seed for variety
            # GPU optimization flags - FORCE MAXIMUM GPU USAGE
            'num_gpu': -1,  # Use ALL available GPU layers
            'num_thread': 8,  # Optimize CPU threads for remaining work
            'low_vram': False,  # Don't limit VRAM usage - use it all!
            'use_mmap': True,  # Memory mapping for efficiency
            'use_mlock': True,  # Lock memory for performance
            # Performance optimization
            'numa': True,  # NUMA optimization
            'batch_size': 512,  # Larger batch size for GPU efficiency
            'parallel': 4,  # Parallel processing
        }
        
        self._initialize_model_sync()

    def _initialize_model_sync(self):
        """Synchronous model initialization (main implementation)"""
        logger.info("Initializing Ollama: Verifying connection and finding available model...")

        try:
            # Simple synchronous check to avoid async issues
            import requests
            response = requests.get(f"{self.base_url}/api/tags", timeout=3.0)

            if response.status_code == 200:
                data = response.json()
                available_models = [model["name"] for model in data.get("models", [])]
                logger.info(f"Available Ollama models: {available_models}")

                # Select the best model from preferences
                for preferred in self.preferred_models:
                    for available in available_models:
                        if preferred in available:
                            self.active_model = available
                            logger.info(f"✅ Active Ollama model set to: {self.active_model}")

                            # Force GPU optimization for the selected model
                            self._optimize_model_for_gpu()
                            return

                # If no preferred model found, use the first available
                if available_models:
                    self.active_model = available_models[0]
                    logger.info(f"✅ Active Ollama model set to: {self.active_model} (first available)")
                    self._optimize_model_for_gpu()
                else:
                    logger.error("❌ No models available in Ollama")
            else:
                logger.error(f"❌ Ollama responded with status {response.status_code}")
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to connect to Ollama: {e}")
        except Exception as e:
            logger.error(f"❌ Ollama initialization error: {e}")

    async def _initialize_model(self):
        """Async version of model initialization for unified inference manager compatibility"""
        return self._initialize_model_sync()

    def _optimize_model_for_gpu(self):
        """Force GPU optimization for the active model"""
        try:
            if not self.active_model:
                return
                
            logger.info(f"🚀 Optimizing {self.active_model} for MAXIMUM GPU utilization...")
            
            # Skip the preload request during initialization to avoid hanging
            # GPU optimization will happen on first actual generation
            logger.info("🎮 GPU optimization will be applied on first generation request")
            
            # Just set the optimization parameters without making a request
            self.gpu_optimized = True
                
        except Exception as e:
            logger.error(f"❌ GPU optimization setup failed: {e}")

    async def generate_text(self, prompt: str, **kwargs) -> Optional[str]:
        """
        Generate text using the active Ollama model with GPU optimization.
        
        Args:
            prompt: Input text prompt
            **kwargs: Additional generation parameters (including 'request_timeout' for custom timeout)
        
        Returns:
            Generated text or None if generation fails
        """
        if not self.active_model:
            logger.error("❌ No active model available for generation")
            return None

        # Extract custom timeout if provided
        request_timeout = kwargs.pop('request_timeout', None)
        
        # Merge user parameters with optimized defaults
        generation_options = {**self.generation_params, **kwargs}
        
        # Ensure GPU optimization is always enabled
        generation_options.update({
            'num_gpu': -1,  # Always force full GPU usage
            'low_vram': False,  # Never limit VRAM
            'use_mmap': True,
            'use_mlock': True,
        })

        payload = {
            "model": self.active_model,
            "prompt": prompt,
            "stream": False,  # Non-streaming for faster processing
            "options": generation_options
        }

        # Dynamic timeout based on model type and complexity
        if request_timeout is not None:
            timeout = request_timeout
        elif 'deepseek' in self.active_model.lower() or 'r1' in self.active_model.lower():
            timeout = 240  # 4 minutes for reasoning models like DeepSeek-R1
        elif any(keyword in prompt.lower() for keyword in ['expert', 'complex', 'difficult']):
            timeout = 120  # 2 minutes for complex prompts
        else:
            timeout = 60   # 1 minute for standard generation

        try:
            start_time = time.time()
            logger.info(f"🚀 NON-BLOCKING generation with Ollama model '{self.active_model}' (GPU accelerated)")
            logger.info(f"⏱️  Using timeout: {timeout}s for model '{self.active_model}'")

            # 🚀 CRITICAL FIX: Use fully async converter to prevent ANY UI blocking
            from .async_converter import get_async_converter

            async_converter = get_async_converter()

            # Use truly async Ollama request
            response_text = await async_converter.async_ollama_request(
                url=self.ollama_api_url,
                payload=payload,
                timeout=timeout
            )

            if response_text:
                elapsed = time.time() - start_time
                tokens_per_second = len(response_text.split()) / elapsed if elapsed > 0 else 0

                logger.info(f"✅ NON-BLOCKING Ollama generation complete in {elapsed:.1f}s ({tokens_per_second:.1f} tokens/s)")
                return response_text
            else:
                logger.error(f"❌ Non-blocking Ollama generation failed - no response")
                return None

        except Exception as e:
            logger.error(f"❌ Non-blocking Ollama generation failed: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Unexpected error in Ollama generation: {e}")
            return None

    def is_available(self) -> bool:
        """Check if Ollama is available with an active model (NON-BLOCKING)"""
        try:
            # Simple synchronous check to avoid async issues
            import requests
            response = requests.get(f"{self.base_url}/api/tags", timeout=2.0)
            if response.status_code == 200:
                # Check if we have an active model
                return self.active_model is not None
            return False
        except Exception as e:
            logger.debug(f"Ollama availability check failed: {e}")
            return False

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the active model"""
        if not self.active_model:
            return {}

        try:
            import requests
            response = requests.post(
                f"{self.base_url}/api/show",
                json={"model": self.active_model},
                timeout=10
            )

            if response.status_code == 200:
                info = response.json()
                # Add our optimization status
                info["gpu_optimized"] = True
                info["optimization_params"] = self.generation_params
                return info
            return {}
        except Exception as e:
            logger.error(f"Failed to get model info: {e}")
            return {}

    def optimize_for_speed(self):
        """Apply maximum speed optimizations at runtime"""
        logger.info("🚀 Applying TURBO speed optimizations...")
        
        # Ultra-fast generation parameters
        self.generation_params.update({
            'temperature': 0.9,  # Higher for faster sampling
            'top_k': 20,  # Even more restrictive for speed
            'num_predict': 300,  # Shorter responses
            'num_ctx': 1536,  # Smaller context
            'repeat_penalty': 1.05,  # Lower penalty for speed
            # Maximum GPU utilization
            'num_gpu': -1,
            'num_thread': 12,  # More CPU threads
            'batch_size': 1024,  # Larger batches
            'parallel': 6,  # More parallel processing
        })
        
        logger.info("⚡ TURBO mode activated - Maximum speed with full GPU utilization!")

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            "model": self.active_model,
            "gpu_optimized": True,
            "generation_params": self.generation_params,
            "server_url": self.base_url,
            "status": "ready" if self.is_available() else "unavailable"
        }

    async def _get_available_models(self) -> List[str]:
        """Get list of available Ollama models"""
        try:
            # 🚀 CRITICAL FIX: Use async converter to prevent UI blocking
            from .async_converter import get_async_converter
            async_converter = get_async_converter()

            response_data = await async_converter.async_get(
                f"{self.base_url}/api/tags",
                timeout=2.0
            )

            if response_data:
                models = response_data.get("models", [])
                return [model["name"] for model in models]
            else:
                logger.error(f"❌ Failed to fetch available models")
                return []
        except Exception as e:
            logger.error(f"❌ Error fetching available models: {e}")
            return []