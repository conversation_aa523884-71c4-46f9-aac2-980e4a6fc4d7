#!/usr/bin/env python3
"""
Working DeepSeek UI integration test
This test properly initializes the unified manager and forces DeepSeek usage
"""

import sys
import os
import time
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, Qt

def main():
    print("🧠 WORKING DEEPSEEK UI INTEGRATION TEST")
    print("=" * 50)
    
    # Fix Qt WebEngine initialization
    QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
    app = QApplication([])
    
    # Create the app
    from knowledge_app.webengine_app import KnowledgeAppWebEngine
    knowledge_app = KnowledgeAppWebEngine()
    knowledge_app.show()
    
    # Wait for proper initialization
    time.sleep(5)
    
    def test_deepseek_integration():
        try:
            print("🔧 Testing DeepSeek UI integration...")
            
            # Check if unified manager is available
            if hasattr(knowledge_app, 'unified_manager') and knowledge_app.unified_manager:
                print("✅ Unified manager found!")
                
                # Test DeepSeek pipeline directly
                print("🧠 Calling DeepSeek pipeline for expert numerical question...")
                
                result = knowledge_app.unified_manager.generate_mcq_sync(
                    topic="atoms",
                    difficulty="expert",
                    question_type="numerical"
                )
                
                if result:
                    print("✅ DeepSeek pipeline generated question!")
                    
                    # Create UI-compatible question format
                    ui_question = {
                        "question": result.get('question', 'Generated question'),
                        "options": result.get('options', ['A', 'B', 'C', 'D']),
                        "correct_answer": result.get('correct_answer', result.get('correct', 'A')),
                        "explanation": result.get('explanation', 'Generated explanation'),
                        "question_number": 1,
                        "total_questions": 1,
                        "is_loading": False
                    }
                    
                    print("📋 Generated Question:")
                    print(f"   Question: {ui_question['question']}")
                    print(f"   Options: {ui_question['options']}")
                    print(f"   Correct: {ui_question['correct_answer']}")
                    
                    # Check if it's actually numerical
                    question_text = ui_question['question'].lower()
                    is_numerical = any(term in question_text for term in [
                        'calculate', 'energy', 'wavelength', 'frequency', 'mass', 'binding'
                    ])
                    
                    if is_numerical:
                        print("🎉 SUCCESS: Generated NUMERICAL question!")
                    else:
                        print("⚠️ WARNING: Question might still be conceptual")
                    
                    # Display in UI
                    print("📡 Emitting to UI...")
                    knowledge_app.bridge.questionReceived.emit(ui_question)
                    print("✅ Question displayed in UI!")
                    
                else:
                    print("❌ DeepSeek pipeline returned no result")
                    
            else:
                print("❌ Unified manager not available")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
    
    # Run test after app is fully initialized
    QTimer.singleShot(8000, test_deepseek_integration)
    
    # Keep app running to see results
    def close_app():
        print("🔚 Test completed - closing app...")
        app.quit()
    
    QTimer.singleShot(20000, close_app)
    
    print("⏳ App running... DeepSeek test will start in 8 seconds...")
    app.exec_()

if __name__ == "__main__":
    main()
