#!/usr/bin/env python3
"""
Better OCR reader using multiple OCR engines to actually read the question
"""

import os
import sys
from pathlib import Path

def read_with_pytesseract(image_path):
    """Try reading with pytesseract (Tesseract OCR)"""
    try:
        import pytesseract
        from PIL import Image
        
        print("🔍 Using Tesseract OCR...")
        
        # Open image
        image = Image.open(image_path)
        
        # Extract text
        text = pytesseract.image_to_string(image)
        
        print(f"📝 Tesseract extracted {len(text)} characters")
        return text
        
    except ImportError:
        print("❌ pytesseract not available")
        return None
    except Exception as e:
        print(f"❌ Tesseract failed: {e}")
        return None

def read_with_easyocr(image_path):
    """Try reading with EasyOCR"""
    try:
        import easyocr
        
        print("🔍 Using EasyOCR...")
        
        # Initialize reader
        reader = easyocr.Reader(['en'])
        
        # Extract text
        results = reader.readtext(str(image_path))
        
        # Combine all text
        text = ' '.join([result[1] for result in results])
        
        print(f"📝 EasyOCR extracted {len(text)} characters")
        return text
        
    except ImportError:
        print("❌ easyocr not available")
        return None
    except Exception as e:
        print(f"❌ EasyOCR failed: {e}")
        return None

def read_with_opencv(image_path):
    """Try reading with OpenCV + Tesseract"""
    try:
        import cv2
        import pytesseract
        
        print("🔍 Using OpenCV + Tesseract...")
        
        # Read image
        image = cv2.imread(str(image_path))
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply threshold to get better OCR results
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Extract text
        text = pytesseract.image_to_string(thresh)
        
        print(f"📝 OpenCV+Tesseract extracted {len(text)} characters")
        return text
        
    except ImportError:
        print("❌ opencv-python or pytesseract not available")
        return None
    except Exception as e:
        print(f"❌ OpenCV+Tesseract failed: {e}")
        return None

def analyze_question_content(text):
    """Analyze the actual question content"""
    print("\n" + "="*60)
    print("📋 ACTUAL QUESTION CONTENT ANALYSIS")
    print("="*60)
    
    # Clean up the text
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    
    print("📝 EXTRACTED TEXT:")
    for i, line in enumerate(lines, 1):
        print(f"   {i:2d}: {line}")
    
    # Look for question patterns
    question_lines = []
    for line in lines:
        if '?' in line or 'question' in line.lower() or 'calculate' in line.lower():
            question_lines.append(line)
    
    print(f"\n🔍 POTENTIAL QUESTIONS ({len(question_lines)} found):")
    for i, q in enumerate(question_lines, 1):
        print(f"   Q{i}: {q}")
    
    # Check for numerical content
    import re
    numbers_found = re.findall(r'\d+\.?\d*', text)
    print(f"\n🔢 NUMBERS FOUND: {numbers_found}")
    
    # Check for units
    units = ['eV', 'nm', 'Hz', 'J', 'kg', 'mol', 'K', 'Pa', 'V', 'A', 'Ω']
    units_found = [unit for unit in units if unit in text]
    print(f"📏 UNITS FOUND: {units_found}")
    
    # Check for calculation keywords
    calc_keywords = ['calculate', 'compute', 'find', 'determine', 'solve', 'what is the value']
    calc_found = [kw for kw in calc_keywords if kw.lower() in text.lower()]
    print(f"🧮 CALCULATION KEYWORDS: {calc_found}")
    
    # Check for PhD-level terms
    phd_terms = ['quantum', 'orbital', 'electron configuration', 'ionization energy', 'binding energy', 
                 'wavelength', 'frequency', 'photon', 'transition', 'excited state', 'ground state']
    phd_found = [term for term in phd_terms if term.lower() in text.lower()]
    print(f"🎓 PhD-LEVEL TERMS: {phd_found}")
    
    # Final assessment
    print(f"\n🎯 ASSESSMENT:")
    is_numerical = len(numbers_found) > 0 or len(calc_found) > 0
    is_phd_level = len(phd_found) > 0
    has_units = len(units_found) > 0
    
    print(f"   📊 Numerical: {'✅ YES' if is_numerical else '❌ NO'}")
    print(f"   🎓 PhD-level: {'✅ YES' if is_phd_level else '❌ NO'}")
    print(f"   📏 Has units: {'✅ YES' if has_units else '❌ NO'}")
    
    if is_numerical and is_phd_level:
        print("   🎉 VERDICT: This appears to be a numerical PhD-level question!")
    elif is_numerical:
        print("   ⚠️ VERDICT: Numerical but not PhD-level")
    elif is_phd_level:
        print("   ⚠️ VERDICT: PhD-level but not numerical")
    else:
        print("   ❌ VERDICT: Neither numerical nor PhD-level")

def main():
    print("🔍 BETTER OCR ANALYSIS - Reading Actual Question")
    print("="*60)
    
    # Find the latest screenshot
    screenshot_dir = Path("test_screenshots")
    if not screenshot_dir.exists():
        print("❌ No test_screenshots directory found")
        return
    
    # Get the latest question screenshot
    question_files = list(screenshot_dir.glob("question_loaded_final_*.jpg"))
    if not question_files:
        print("❌ No question screenshot found")
        return
    
    latest_file = max(question_files, key=lambda x: x.stat().st_mtime)
    print(f"📸 Analyzing: {latest_file}")
    
    # Try multiple OCR methods
    text_results = []
    
    # Method 1: Tesseract
    text1 = read_with_pytesseract(latest_file)
    if text1:
        text_results.append(("Tesseract", text1))
    
    # Method 2: EasyOCR
    text2 = read_with_easyocr(latest_file)
    if text2:
        text_results.append(("EasyOCR", text2))
    
    # Method 3: OpenCV + Tesseract
    text3 = read_with_opencv(latest_file)
    if text3:
        text_results.append(("OpenCV+Tesseract", text3))
    
    if not text_results:
        print("❌ All OCR methods failed!")
        return
    
    # Use the longest result (usually most complete)
    best_method, best_text = max(text_results, key=lambda x: len(x[1]))
    print(f"\n🏆 Using best result from: {best_method}")
    
    # Analyze the actual content
    analyze_question_content(best_text)

if __name__ == "__main__":
    main()
