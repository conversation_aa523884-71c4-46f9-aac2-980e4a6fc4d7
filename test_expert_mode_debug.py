#!/usr/bin/env python3
"""
Debug expert mode quiz generation
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

async def test_expert_mode_debug():
    """Debug expert mode generation step by step"""
    print("🔍 DEBUGGING EXPERT MODE GENERATION")
    print("=" * 60)
    
    try:
        # Test 1: Check DeepSeek pipeline
        print("\n1️⃣ Testing DeepSeek pipeline...")
        try:
            from knowledge_app.core.deepseek_integration import get_deepseek_pipeline
            
            pipeline = get_deepseek_pipeline()
            if pipeline:
                print(f"   ✅ Pipeline created")
                print(f"   📊 Available models: {len(pipeline.available_models)}")
                print(f"   🧠 Thinking model: {pipeline.thinking_model}")
                print(f"   📝 JSON model: {pipeline.json_model}")
                print(f"   🚀 Ready: {pipeline.is_ready()}")
                
                if not pipeline.is_ready():
                    print("   ❌ Pipeline not ready - this explains the fallback to unified inference")
                else:
                    print("   ✅ Pipeline ready - should work for expert mode")
            else:
                print("   ❌ Failed to create pipeline")
        except Exception as e:
            print(f"   ❌ DeepSeek error: {e}")
        
        # Test 2: Check MCQ Manager
        print("\n2️⃣ Testing MCQ Manager...")
        try:
            from knowledge_app.core.mcq_manager import get_mcq_manager
            
            mcq_manager = get_mcq_manager()
            print(f"   ✅ MCQ Manager created")
            
            # Test expert mode parameters
            quiz_params = {
                "topic": "fluid dynamics",
                "difficulty": "expert",
                "game_mode": "serious",
                "submode": "mixed",
                "num_questions": 1
            }
            
            print(f"   🧪 Testing with params: {quiz_params}")
            
            # Try generation
            result = await mcq_manager.generate_quiz_async(quiz_params)
            
            if result:
                print("   ✅ Generation successful!")
                print(f"   ❓ Question: {result.question[:100]}...")
                print(f"   🔧 Method: {getattr(result, 'generation_method', 'unknown')}")
            else:
                print("   ❌ Generation failed - returned None")
                
        except Exception as e:
            print(f"   ❌ MCQ Manager error: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 3: Check Unified Inference Manager
        print("\n3️⃣ Testing Unified Inference Manager...")
        try:
            from knowledge_app.core.unified_inference_manager import get_unified_inference_manager
            
            manager = get_unified_inference_manager()
            status = manager.get_status()
            print(f"   📊 Status: {status}")
            
            # Test direct generation
            from knowledge_app.core.unified_inference_manager import InferenceRequest
            
            request = InferenceRequest(
                request_id="debug_test",
                operation="generate_mcq",
                params={
                    "topic": "fluid dynamics",
                    "difficulty": "expert",
                    "question_type": "mixed",
                    "context": None,
                    "adapter_name": None
                },
                timeout=30.0
            )
            
            print("   🔄 Testing direct unified inference...")
            result = await manager._handle_mcq_generation(request)
            
            if result:
                print("   ✅ Unified inference successful!")
                print(f"   ❓ Question: {result.get('question', 'N/A')[:100]}...")
            else:
                print("   ❌ Unified inference failed")
                
        except Exception as e:
            print(f"   ❌ Unified Inference error: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("🔍 DEBUG COMPLETE")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_expert_mode_debug())
