#!/usr/bin/env python3
"""
Real PhD Quality Test - Focus on actual question complexity, not hard-coded criteria
Tests if questions are genuinely advanced and complex, regardless of format
"""

import asyncio
import time
import random
from datetime import datetime

# PhD-level topics that should generate truly complex questions
ADVANCED_RESEARCH_TOPICS = [
    "quantum field theory", "general relativity", "organic synthesis mechanisms",
    "machine learning theory", "differential geometry", "immunology pathways",
    "cryptographic protocols", "neural network optimization", "molecular dynamics",
    "algebraic topology", "computational complexity", "pharmacokinetics",
    "string theory", "biochemical pathways", "distributed systems",
    "quantum computing algorithms", "statistical mechanics", "gene regulation",
    "compiler optimization", "abstract algebra", "protein folding",
    "game theory", "signal processing", "metabolic networks"
]

def analyze_question_complexity(question, topic):
    """
    Analyze if a question is genuinely PhD-level complex
    Focus on actual content quality, not format requirements
    """
    if not question or len(question.strip()) < 50:
        return False, "Question too basic or empty"
    
    question_lower = question.lower()
    
    # Check for genuine complexity indicators (not hard requirements)
    complexity_indicators = {
        'advanced_concepts': any(term in question_lower for term in [
            'mechanism', 'pathway', 'algorithm', 'optimization', 'synthesis',
            'analysis', 'framework', 'methodology', 'paradigm', 'theory',
            'principle', 'phenomenon', 'interaction', 'regulation', 'dynamics'
        ]),
        
        'technical_depth': any(term in question_lower for term in [
            'molecular', 'quantum', 'computational', 'statistical', 'differential',
            'algebraic', 'topological', 'biochemical', 'thermodynamic', 'kinetic',
            'stochastic', 'deterministic', 'probabilistic', 'heuristic'
        ]),
        
        'research_language': any(phrase in question_lower for phrase in [
            'research shows', 'studies indicate', 'experimental evidence',
            'theoretical model', 'empirical data', 'systematic analysis',
            'comparative study', 'meta-analysis', 'peer-reviewed'
        ]),
        
        'specific_terminology': len([word for word in question_lower.split() 
                                   if len(word) > 10]) >= 2,  # Complex vocabulary
        
        'multi_concept': question_lower.count('and') >= 2 or question_lower.count('or') >= 2,
        
        'quantitative_aspect': any(char.isdigit() for char in question) or 
                              any(term in question_lower for term in [
                                  'rate', 'coefficient', 'constant', 'variable',
                                  'parameter', 'factor', 'ratio', 'percentage'
                              ])
    }
    
    # Count genuine complexity indicators
    complexity_score = sum(complexity_indicators.values())
    
    # A truly PhD-level question should have multiple complexity indicators
    is_complex = complexity_score >= 3
    
    return is_complex, f"Complexity score: {complexity_score}/6 indicators"

def evaluate_options_quality(options):
    """Evaluate if options show genuine understanding and complexity"""
    if not options or len(options) != 4:
        return False, "Invalid options structure"
    
    # Check for substantial, technical options
    substantial_options = sum(1 for opt in options if len(str(opt).strip()) >= 15)
    
    # Check for technical terminology in options
    all_options_text = ' '.join(str(opt) for opt in options).lower()
    technical_terms = sum(1 for term in [
        'mechanism', 'process', 'system', 'method', 'approach', 'technique',
        'analysis', 'synthesis', 'optimization', 'regulation', 'interaction'
    ] if term in all_options_text)
    
    # Options should be substantial and contain technical content
    quality_score = (substantial_options / 4) + min(technical_terms / 5, 1)
    
    return quality_score >= 1.5, f"Options quality: {quality_score:.1f}/2.0"

async def test_phd_question_generation(topic, test_num, total_tests):
    """Test PhD-level question generation focusing on real quality"""
    print(f"\n🎓 PhD Test {test_num:2d}/{total_tests}: {topic}")
    
    try:
        from src.knowledge_app.core.unified_inference_manager import generate_mcq_unified
        
        start_time = time.time()
        
        # Generate with EXPERT difficulty
        result = generate_mcq_unified(
            topic=topic,
            difficulty="expert",
            question_type="mixed",
            timeout=90.0
        )
        
        duration = time.time() - start_time
        
        if not result:
            print(f"   ❌ GENERATION FAILED - No result after {duration:.1f}s")
            return False, f"Failed generation"
        
        question = result.get('question', '')
        options = result.get('options', [])
        explanation = result.get('explanation', '')
        
        print(f"   📝 QUESTION: {question}")
        print(f"   📋 OPTIONS:")
        for i, opt in enumerate(options):
            print(f"      {chr(65 + i)}) {opt}")
        if explanation:
            print(f"   💡 EXPLANATION: {explanation[:150]}{'...' if len(explanation) > 150 else ''}")
        
        # Analyze actual question complexity (not hard-coded criteria)
        is_complex, complexity_msg = analyze_question_complexity(question, topic)
        options_good, options_msg = evaluate_options_quality(options)
        
        print(f"   🧠 COMPLEXITY: {complexity_msg}")
        print(f"   📊 OPTIONS: {options_msg}")
        print(f"   ⏱️ TIME: {duration:.1f}s")
        
        # Overall assessment based on actual quality
        if is_complex and options_good:
            print(f"   ✅ EXCELLENT - Genuinely PhD-level question!")
            return True, "PhD-level quality"
        elif is_complex:
            print(f"   🟡 GOOD - Complex question, but options need work")
            return True, "Good complexity"
        elif options_good:
            print(f"   🟡 OKAY - Good options, but question lacks complexity")
            return False, "Lacks complexity"
        else:
            print(f"   ❌ POOR - Neither question nor options meet PhD standards")
            return False, "Poor quality"
        
    except Exception as e:
        print(f"   ❌ ERROR - {e}")
        return False, f"Exception: {e}"

async def run_real_phd_quality_test(batch_size=15):
    """Run real PhD quality test focusing on actual content quality"""
    print("🎓 REAL PhD QUALITY TEST - FOCUS ON ACTUAL COMPLEXITY")
    print("=" * 70)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialize system
    print("\n🔧 Initializing expert mode system...")
    try:
        from src.knowledge_app.core.unified_inference_manager import initialize_unified_inference
        
        success = initialize_unified_inference({
            'timeout': 90.0,
            'mode': 'auto',
            'prefer_local': True
        })
        
        if not success:
            print("❌ System initialization failed")
            return False
        
        print("✅ System ready for PhD-level testing")
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return False
    
    # Select diverse advanced topics
    selected_topics = random.sample(ADVANCED_RESEARCH_TOPICS, min(batch_size, len(ADVANCED_RESEARCH_TOPICS)))
    
    print(f"\n🎯 Testing {len(selected_topics)} advanced research topics...")
    
    # Run tests
    start_time = time.time()
    excellent_count = 0
    good_count = 0
    results = []
    
    for i, topic in enumerate(selected_topics, 1):
        success, message = await test_phd_question_generation(topic, i, len(selected_topics))
        results.append((topic, success, message))
        
        if success and "PhD-level quality" in message:
            excellent_count += 1
        elif success:
            good_count += 1
    
    duration = time.time() - start_time
    total_acceptable = excellent_count + good_count
    acceptance_rate = (total_acceptable / len(selected_topics)) * 100
    excellence_rate = (excellent_count / len(selected_topics)) * 100
    
    # Results summary
    print("\n" + "=" * 70)
    print("🏆 REAL PhD QUALITY RESULTS")
    print("=" * 70)
    
    print(f"📊 Quality Assessment:")
    print(f"   Excellent (PhD-level): {excellent_count}/{len(selected_topics)} ({excellence_rate:.1f}%)")
    print(f"   Good (Complex): {good_count}/{len(selected_topics)} ({(good_count/len(selected_topics)*100):.1f}%)")
    print(f"   Total Acceptable: {total_acceptable}/{len(selected_topics)} ({acceptance_rate:.1f}%)")
    print(f"   Duration: {duration:.1f} seconds")
    print(f"   Average per test: {duration/len(selected_topics):.1f} seconds")
    
    print(f"\n📈 Detailed Results:")
    for topic, success, message in results:
        status = "✅" if success else "❌"
        print(f"   {status} {topic:<30} - {message}")
    
    # Real-world assessment
    if acceptance_rate >= 80:
        print(f"\n🎉 PRODUCTION READY: {acceptance_rate:.1f}% acceptable quality!")
        print("🚀 This quality level is suitable for paying customers!")
        return True
    elif acceptance_rate >= 70:
        print(f"\n👏 VERY GOOD: {acceptance_rate:.1f}% acceptable quality!")
        print("🔧 Minor improvements needed for production")
        return True
    elif acceptance_rate >= 60:
        print(f"\n📊 DECENT: {acceptance_rate:.1f}% acceptable quality")
        print("⚠️ Needs optimization before production launch")
        return True
    else:
        print(f"\n❌ UNACCEPTABLE: {acceptance_rate:.1f}% quality")
        print("🔧 Major improvements required - not ready for users")
        return False

async def main():
    """Main test runner"""
    print("🚀 REAL PhD QUALITY TESTER")
    
    try:
        success = await run_real_phd_quality_test(15)
        return 0 if success else 1
    except Exception as e:
        print(f"❌ Test crashed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
