#!/usr/bin/env python3
"""
Quick fix test - bypass all hanging issues and directly fix the problem
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_direct_ollama():
    print("🔧 QUICK FIX TEST")
    print("=" * 30)
    
    try:
        # Test direct Ollama connection
        import requests
        
        print("1. Testing Ollama connection...")
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            model_names = [m["name"] for m in models]
            print(f"✅ Ollama connected. Models: {model_names}")
            
            # Find working model (not DeepSeek)
            working_model = None
            for model in ["mathstral:latest", "llama3.1:latest", "llama3.1:8b"]:
                if model in model_names:
                    working_model = model
                    break
                    
            if not working_model:
                working_model = model_names[0] if model_names else None
                
            if working_model:
                print(f"2. Using working model: {working_model}")
                
                # Test direct generation
                prompt = """Create a numerical physics question about atoms with calculations.

Output ONLY JSON:
{
  "question": "Calculate the wavelength of light emitted when an electron transitions from n=3 to n=2 in hydrogen (Rydberg constant = 1.097×10^7 m^-1)?",
  "options": ["656 nm", "486 nm", "434 nm", "410 nm"],
  "correct_answer": "A", 
  "explanation": "Using 1/λ = R(1/n₁² - 1/n₂²) = 1.097×10^7(1/4 - 1/9) = 1.52×10^6 m^-1, so λ = 656 nm"
}"""

                payload = {
                    "model": working_model,
                    "prompt": prompt,
                    "stream": False,
                    "format": "json",
                    "options": {"temperature": 0.3, "num_predict": 500}
                }
                
                print("3. Generating numerical question...")
                gen_response = requests.post(
                    "http://localhost:11434/api/generate",
                    json=payload,
                    timeout=30
                )
                
                if gen_response.status_code == 200:
                    result = gen_response.json()
                    generated_text = result.get("response", "")
                    print(f"✅ Generated: {generated_text[:200]}...")
                    
                    # Try to parse JSON
                    import json
                    try:
                        question_data = json.loads(generated_text)
                        print("\n🎉 SUCCESS! Generated numerical question:")
                        print(f"Question: {question_data.get('question', 'N/A')}")
                        print(f"Options: {question_data.get('options', 'N/A')}")
                        print(f"Answer: {question_data.get('correct_answer', 'N/A')}")
                        
                        # Check if it's actually numerical
                        question_text = question_data.get('question', '').lower()
                        if any(term in question_text for term in ['calculate', 'wavelength', 'energy', 'frequency']):
                            print("✅ This is a NUMERICAL question!")
                        else:
                            print("⚠️ This might still be conceptual")
                            
                    except json.JSONDecodeError:
                        print("❌ Failed to parse JSON")
                        
                else:
                    print(f"❌ Generation failed: {gen_response.status_code}")
            else:
                print("❌ No working models found")
        else:
            print("❌ Ollama not responding")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_direct_ollama()
