"""
Memory Manager Tests
"""
import pytest
import time
from unittest.mock import Mock, patch
from knowledge_app.core.memory_manager import MemoryManager

class TestMemoryManager:
    """Test memory management functionality"""

    @pytest.fixture
    def memory_config(self):
        """Default memory manager configuration"""
        return {
            'memory_cache_path': 'data/memory_cache',
            'memory_cache_size': 512 * 1024 * 1024,  # 512MB
            'cleanup_threshold': 0.7,
            'cache_expiry': 1800,  # 30 minutes
            'memory_threshold': 0.7,  # 70%
            'gpu_memory_threshold': 0.98,  # 98%
            'critical_memory_threshold': 0.95,  # 95%
            'memory_check_interval': 60  # 60 seconds
        }

    def test_memory_manager_initialization(self, memory_config):
        """Test that MemoryManager can be initialized with config"""
        manager = MemoryManager(memory_config)
        assert manager is not None
        # Clean up
        manager.cleanup()
    
    def test_get_memory_info(self, memory_config):
        """Test getting memory information"""
        manager = MemoryManager(memory_config)
        try:
            # Test that memory stats are accessible
            assert hasattr(manager, '_memory_stats')
            assert isinstance(manager._memory_stats, dict)
            assert 'system' in manager._memory_stats
            assert 'gpu' in manager._memory_stats
            assert 'process' in manager._memory_stats
        finally:
            manager.cleanup()
    
    def test_memory_monitoring(self, memory_config):
        """Test memory monitoring functionality"""
        manager = MemoryManager(memory_config)
        try:
            # Test that monitoring can be started and stopped
            assert hasattr(manager, 'start_monitoring')
            assert hasattr(manager, 'stop_monitoring')

            # Test monitoring state
            assert hasattr(manager, '_monitor_thread')
            assert hasattr(manager, '_stop_monitoring')
        finally:
            manager.cleanup()
    
    def test_memory_cleanup(self, memory_config):
        """Test memory cleanup functionality"""
        manager = MemoryManager(memory_config)
        try:
            # Test cleanup methods exist
            assert hasattr(manager, '_normal_cleanup')
            assert hasattr(manager, '_aggressive_cleanup')
            assert hasattr(manager, '_cleanup_gpu_memory')

            # Test cleanup can be called
            manager._normal_cleanup()
            manager._cleanup_gpu_memory()
        finally:
            manager.cleanup()
    
    def test_memory_thresholds(self, memory_config):
        """Test memory threshold configuration"""
        manager = MemoryManager(memory_config)
        try:
            # Test that thresholds are set correctly
            assert hasattr(manager, 'memory_threshold')
            assert hasattr(manager, 'gpu_memory_threshold')
            assert hasattr(manager, 'critical_memory_threshold')

            # Test threshold values are reasonable
            assert 0 < manager.memory_threshold <= 1
            assert 0 < manager.gpu_memory_threshold <= 1
            assert 0 < manager.critical_memory_threshold <= 1
        finally:
            manager.cleanup()
    
    @patch('psutil.virtual_memory')
    def test_memory_stats_update(self, mock_virtual_memory, memory_config):
        """Test memory stats update with mocked psutil"""
        # Mock psutil response
        mock_memory = Mock()
        mock_memory.total = 8 * 1024 * 1024 * 1024  # 8GB
        mock_memory.available = 4 * 1024 * 1024 * 1024  # 4GB
        mock_memory.percent = 50.0
        mock_virtual_memory.return_value = mock_memory

        manager = MemoryManager(memory_config)
        try:
            # Test that update method exists and can be called
            assert hasattr(manager, '_update_memory_stats')
            manager._update_memory_stats()

            # Test that stats are updated
            assert 'system' in manager._memory_stats
        finally:
            manager.cleanup()

    def test_cache_integration(self, memory_config):
        """Test integration with cache manager"""
        manager = MemoryManager(memory_config)
        try:
            # Test that MemoryManager inherits from BaseCacheManager
            assert hasattr(manager, 'cleanup')
            assert hasattr(manager, '_cleanup_expired')

            # Test cache functionality
            manager._cleanup_expired()
        finally:
            manager.cleanup()
