"""
Question model classes for the Knowledge App quiz system
"""

from typing import List, Optional, Dict, Any
from dataclasses import dataclass
import json
import os
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class Question:
    """A quiz question with multiple choice options"""

    id: str
    text: str
    options: List[str]
    correct_answer: int  # Index of the correct option
    category: str = "General"
    difficulty: str = "Medium"
    explanation: Optional[str] = None
    tags: List[str] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert question to dictionary"""
        return {
            "id": self.id,
            "text": self.text,
            "options": self.options,
            "correct_answer": self.correct_answer,
            "category": self.category,
            "difficulty": self.difficulty,
            "explanation": self.explanation,
            "tags": self.tags,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Question":
        """Create question from dictionary"""
        return cls(
            id=data["id"],
            text=data["text"],
            options=data["options"],
            correct_answer=data["correct_answer"],
            category=data.get("category", "General"),
            difficulty=data.get("difficulty", "Medium"),
            explanation=data.get("explanation"),
            tags=data.get("tags", []),
        )


class QuestionManager:
    """Manages quiz questions - loading, filtering, and selection"""

    def __init__(self, data_path: Optional[str] = None):
        self.data_path = data_path or self._get_default_data_path()
        self.questions: List[Question] = []
        self._load_questions()

    def _get_default_data_path(self) -> str:
        """Get default path for question data"""
        app_root = Path(__file__).parent.parent.parent.parent
        return str(app_root / "quiz_storage" / "questions.json")

    def _load_questions(self):
        """Load questions from data file"""
        try:
            if os.path.exists(self.data_path):
                with open(self.data_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.questions = [Question.from_dict(q) for q in data.get("questions", [])]
                print(f"Loaded {len(self.questions)} questions from {self.data_path}")
            else:
                print(
                    f"No question data found at {self.data_path}, starting with empty question set"
                )
                self.questions = []
                self.save_questions()
        except Exception as e:
            print(f"Error loading questions: {e}")
            self.questions = []

    def get_questions(
        self,
        category: Optional[str] = None,
        difficulty: Optional[str] = None,
        count: Optional[int] = None,
    ) -> List[Question]:
        """Get filtered questions"""
        filtered = self.questions.copy()

        # Filter by category
        if category:
            filtered = [q for q in filtered if q.category.lower() == category.lower()]

        # Filter by difficulty
        if difficulty:
            filtered = [q for q in filtered if q.difficulty.lower() == difficulty.lower()]

        # Limit count
        if count and len(filtered) > count:
            import random

            filtered = random.sample(filtered, count)

        return filtered

    def add_question(self, question: Question):
        """Add a new question"""
        self.questions.append(question)

    def remove_question(self, question_id: str) -> bool:
        """Remove a question by ID"""
        original_count = len(self.questions)
        self.questions = [q for q in self.questions if q.id != question_id]
        return len(self.questions) < original_count

    def save_questions(self):
        """Save questions to data file"""
        try:
            os.makedirs(os.path.dirname(self.data_path), exist_ok=True)
            data = {"questions": [q.to_dict() for q in self.questions], "version": "1.0"}
            with open(self.data_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"Saved {len(self.questions)} questions to {self.data_path}")
        except Exception as e:
            print(f"Error saving questions: {e}")

    def get_categories(self) -> List[str]:
        """Get all available categories"""
        return list(set(q.category for q in self.questions))

    def get_difficulties(self) -> List[str]:
        """Get all available difficulty levels"""
        return list(set(q.difficulty for q in self.questions))