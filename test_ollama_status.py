#!/usr/bin/env python3
"""
Test Ollama status and provide guidance
"""

import requests
import subprocess
import sys
import os
from pathlib import Path

def check_ollama_status():
    """Check if Ollama is running and provide guidance"""
    print("🔍 CHECKING OLLAMA STATUS")
    print("=" * 50)
    
    # Test 1: Check if <PERSON>lla<PERSON> is running
    print("\n1️⃣ Testing Ollama connection...")
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=2)
        if response.status_code == 200:
            print("   ✅ Ollama is running!")
            models = response.json().get('models', [])
            print(f"   📊 Available models: {len(models)}")
            for model in models[:5]:  # Show first 5 models
                print(f"      - {model.get('name', 'Unknown')}")
            if len(models) > 5:
                print(f"      ... and {len(models) - 5} more")
            return True
        else:
            print(f"   ❌ Ollama responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("   ❌ Cannot connect to <PERSON>lla<PERSON> (Connection refused)")
        print("   💡 This means Ollama is not running")
        return False
    except requests.exceptions.Timeout:
        print("   ❌ Ollama connection timed out")
        return False
    except Exception as e:
        print(f"   ❌ Error checking Ollama: {e}")
        return False

def check_ollama_installation():
    """Check if Ollama is installed"""
    print("\n2️⃣ Checking Ollama installation...")
    try:
        result = subprocess.run(['ollama', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"   ✅ Ollama is installed: {result.stdout.strip()}")
            return True
        else:
            print("   ❌ Ollama command failed")
            return False
    except FileNotFoundError:
        print("   ❌ Ollama is not installed or not in PATH")
        return False
    except subprocess.TimeoutExpired:
        print("   ❌ Ollama command timed out")
        return False
    except Exception as e:
        print(f"   ❌ Error checking Ollama installation: {e}")
        return False

def provide_guidance():
    """Provide guidance on how to fix Ollama issues"""
    print("\n🔧 GUIDANCE FOR FIXING OLLAMA")
    print("=" * 50)
    
    ollama_running = check_ollama_status()
    ollama_installed = check_ollama_installation()
    
    if not ollama_installed:
        print("\n📥 OLLAMA NOT INSTALLED:")
        print("   1. Download Ollama from: https://ollama.ai/")
        print("   2. Install it following the instructions for your OS")
        print("   3. Restart your terminal/command prompt")
        print("   4. Run 'ollama --version' to verify installation")
        
    elif not ollama_running:
        print("\n🚀 OLLAMA NOT RUNNING:")
        print("   1. Open a terminal/command prompt")
        print("   2. Run: ollama serve")
        print("   3. Keep that terminal open (Ollama will run in the background)")
        print("   4. In another terminal, run: ollama pull llama3.1:8b")
        print("   5. Wait for the model to download")
        print("   6. Try the Knowledge App again")
        
    else:
        print("\n✅ OLLAMA IS WORKING CORRECTLY!")
        print("   The issue might be elsewhere in the system.")
    
    print("\n💡 QUICK TEST:")
    print("   After starting Ollama, you can test it with:")
    print("   ollama run llama3.1:8b 'Hello, how are you?'")

if __name__ == "__main__":
    provide_guidance()
