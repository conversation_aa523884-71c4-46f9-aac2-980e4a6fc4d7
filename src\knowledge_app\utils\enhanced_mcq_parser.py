"""
Enhanced MCQ Parser for Local Model Responses

This module provides robust parsing for MCQ responses from local models,
handling various output formats and providing comprehensive fallback mechanisms.
"""

import logging
import re
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ParseFormat(Enum):
    """Supported MCQ parsing formats"""

    JSON = "json"
    STRUCTURED = "structured"  # Question: ... A) ... B) ... etc.
    SIMPLE = "simple"  # Just question and options without labels
    MISTRAL = "mistral"  # Mistral-specific format
    LLAMA = "llama"  # Llama-specific format
    MIXED = "mixed"  # Mixed format detection


@dataclass
class ParseResult:
    """Result of MCQ parsing attempt"""

    success: bool
    mcq_data: Optional[Dict[str, Any]]
    format_detected: Optional[ParseFormat]
    confidence: float  # 0.0 to 1.0
    issues: List[str]
    raw_response: str


class EnhancedMCQParser:
    """
    A robust parser designed to extract and repair JSON from messy,
    unreliable text generated by local language models.
    """

    def parse_mcq(self, raw_response: str) -> Optional[Dict[str, Any]]:
        """
        The main parsing function. It attempts to find, clean, and parse a JSON
        object from the raw text response.

        Args:
            raw_response: The raw string output from the language model.

        Returns:
            A dictionary with the parsed MCQ data, or None if parsing fails.
        """
        if not raw_response or not isinstance(raw_response, str):
            logger.warning("Parser received empty or invalid input.")
            return None

        try:
            # CRITICAL FIX: Try multiple extraction methods
            json_str = None
            
            # Method 1: Extract JSON block
            json_str = self._extract_json_block(raw_response)
            
            # Method 2: If no JSON block found, try structured parsing
            if not json_str:
                logger.debug("No JSON block found, trying structured parsing...")
                structured_data = self._parse_structured_format(raw_response)
                if structured_data:
                    return structured_data
            
            # Method 3: If no structured format, try array format
            if not json_str:
                json_str = self._extract_json_array(raw_response)
            
            if not json_str:
                logger.warning("Could not find any parseable format in response.")
                return self._emergency_fallback_parse(raw_response)

            # Clean and parse the extracted JSON
            cleaned_json_str = self._clean_json_string(json_str)
            
            # CRITICAL FIX: Try parsing as both object and array
            try:
                parsed_data = json.loads(cleaned_json_str)
            except json.JSONDecodeError:
                # Try with additional cleaning
                cleaned_json_str = self._aggressive_json_cleanup(cleaned_json_str)
                parsed_data = json.loads(cleaned_json_str)

            # Handle array format (multiple questions)
            if isinstance(parsed_data, list) and len(parsed_data) > 0:
                parsed_data = parsed_data[0]  # Take first question

            # Normalize and validate the structure
            normalized_data = self._normalize_and_validate(parsed_data)
            if not normalized_data:
                logger.error("Parsed JSON failed structural validation.")
                return self._emergency_fallback_parse(raw_response)

            logger.info("Successfully parsed and validated MCQ from model response.")
            return normalized_data

        except json.JSONDecodeError as e:
            logger.error(f"Final JSON parsing failed after cleaning: {e}")
            logger.debug(f"Failed JSON string: {cleaned_json_str if 'cleaned_json_str' in locals() else 'N/A'}")
            return self._emergency_fallback_parse(raw_response)
        except Exception as e:
            logger.error(f"An unexpected error occurred during parsing: {e}", exc_info=True)
            return self._emergency_fallback_parse(raw_response)

    def _extract_json_block(self, text: str) -> Optional[str]:
        """CRITICAL FIX: Enhanced JSON block extraction with multiple patterns"""
        # Remove markdown code blocks first
        text = re.sub(r'```json\s*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'```\s*$', '', text, flags=re.MULTILINE)
        
        # Pattern 1: Standard JSON object
        match = re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', text, re.DOTALL)
        if match:
            return match.group(0)
        
        # Pattern 2: JSON with nested objects (more permissive)
        match = re.search(r'\{.*\}', text, re.DOTALL)
        if match:
            json_candidate = match.group(0)
            # Verify it has basic JSON structure
            if '"question"' in json_candidate and '"options"' in json_candidate:
                return json_candidate
        
        return None

    def _extract_json_array(self, text: str) -> Optional[str]:
        """Extract JSON array format [{"question": ...}]"""
        # Remove markdown code blocks
        text = re.sub(r'```json\s*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'```\s*$', '', text, flags=re.MULTILINE)
        
        # Look for array format
        match = re.search(r'\[.*\]', text, re.DOTALL)
        if match:
            array_candidate = match.group(0)
            if '"question"' in array_candidate and '"options"' in array_candidate:
                return array_candidate
        
        return None

    def _parse_structured_format(self, text: str) -> Optional[Dict[str, Any]]:
        """CRITICAL FIX: Parse structured text format (Question: ... A) ... B) ...)"""
        try:
            # Extract question
            question_match = re.search(r'(?:Question|Q):\s*(.+?)(?=\n|$|A\)|Option A)', text, re.IGNORECASE | re.DOTALL)
            if not question_match:
                return None
            
            question = question_match.group(1).strip()
            
            # Extract options
            options = {}
            option_patterns = [
                (r'A\)\s*(.+?)(?=\n|B\)|$)', 'A'),
                (r'B\)\s*(.+?)(?=\n|C\)|$)', 'B'), 
                (r'C\)\s*(.+?)(?=\n|D\)|$)', 'C'),
                (r'D\)\s*(.+?)(?=\n|Correct|Answer|Explanation|$)', 'D')
            ]
            
            for pattern, key in option_patterns:
                match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
                if match:
                    options[key] = match.group(1).strip()
            
            if len(options) < 2:
                return None
            
            # Extract correct answer
            correct_match = re.search(r'(?:Correct|Answer):\s*([A-D])', text, re.IGNORECASE)
            correct = correct_match.group(1) if correct_match else 'A'
            
            # Extract explanation
            explanation_match = re.search(r'(?:Explanation|Reason):\s*(.+?)(?=\n\n|$)', text, re.IGNORECASE | re.DOTALL)
            explanation = explanation_match.group(1).strip() if explanation_match else "No explanation provided."
            
            return {
                "question": question,
                "options": options,
                "correct": correct,
                "explanation": explanation,
                "source": "structured_format_parsing"
            }
            
        except Exception as e:
            logger.debug(f"Structured format parsing failed: {e}")
            return None

    def _clean_json_string(self, json_str: str) -> str:
        """CRITICAL FIX: Enhanced JSON cleaning with better pattern matching"""
        # Remove markdown formatting
        json_str = re.sub(r'```json\s*', '', json_str, flags=re.IGNORECASE)
        json_str = re.sub(r'```\s*$', '', json_str, flags=re.MULTILINE)
        
        # Replace single quotes with double quotes (but not in contractions)
        json_str = re.sub(r"(?<!\\)(?<!\w)'(?!\w)", '"', json_str)
        
        # Remove trailing commas before } or ]
        json_str = re.sub(r',\s*([\}\]])', r'\1', json_str)
        
        # Add quotes to unquoted keys
        json_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)
        
        # Fix common escaping issues
        json_str = json_str.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
        
        # Remove multiple spaces
        json_str = re.sub(r'\s+', ' ', json_str)
        
        return json_str.strip()

    def _aggressive_json_cleanup(self, json_str: str) -> str:
        """CRITICAL FIX: More aggressive JSON cleanup for stubborn strings"""
        # Fix unescaped quotes in values
        json_str = re.sub(r'(:\s*")([^"]*)"([^"]*)"([^"]*")', r'\1\2\"\3\4', json_str)
        
        # Fix missing closing quotes
        json_str = re.sub(r'(:\s*[^",}\]]+)([,}\]])', r'"\1"\2', json_str)
        
        # Fix array format issues
        json_str = re.sub(r'\[\s*"([^"]*)",\s*"([^"]*)",\s*"([^"]*)",\s*"([^"]*)"\s*\]', 
                         r'{"A": "\1", "B": "\2", "C": "\3", "D": "\4"}', json_str)
        
        return json_str

    def _normalize_and_validate(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """CRITICAL FIX: Enhanced validation with better error handling"""
        if not isinstance(data, dict):
            logger.error(f"Data is not a dictionary: {type(data)}")
            return None
        
        # Handle variations in key names with more mappings
        key_mappings = [
            (["question", "question_text", "q"], "question"),
            (["options", "choices", "answers"], "options"),
            (["correct", "correct_answer", "answer", "correct_option"], "correct"),
            (["explanation", "reason", "rationale"], "explanation")
        ]
        
        normalized = {}
        for old_keys, new_key in key_mappings:
            for old_key in old_keys:
                if old_key in data:
                    normalized[new_key] = data[old_key]
                    break

        # CRITICAL FIX: Better validation
        required_keys = ["question", "options", "correct"]
        missing_keys = [key for key in required_keys if key not in normalized]
        if missing_keys:
            logger.error(f"Missing required keys: {missing_keys}. Have: {list(normalized.keys())}")
            return None

        # CRITICAL FIX: Enhanced options handling
        options = normalized["options"]
        
        # Handle list format
        if isinstance(options, list):
            if len(options) >= 4:
                normalized["options"] = {chr(65+i): opt for i, opt in enumerate(options[:4])}
            else:
                logger.error(f"Options list too short: {len(options)} items")
                return None
        
        # Handle dict format
        elif isinstance(options, dict):
            # Convert numeric keys to letters
            if all(str(k).isdigit() for k in options.keys()):
                new_options = {}
                for i, (k, v) in enumerate(sorted(options.items())):
                    if i < 4:
                        new_options[chr(65+i)] = v
                normalized["options"] = new_options
            
            # Ensure we have at least 2 options
            if len(normalized["options"]) < 2:
                logger.error(f"Too few options: {len(normalized['options'])}")
                return None
        else:
            logger.error(f"Invalid options type: {type(options)}")
            return None

        # CRITICAL FIX: Better correct answer validation
        correct = str(normalized["correct"]).upper().strip()
        
        # If correct is a single letter, use it
        if len(correct) == 1 and correct in 'ABCD':
            normalized["correct"] = correct
        else:
            # Try to find matching option by content
            found_key = None
            for key, value in normalized["options"].items():
                if str(value).strip().lower() == str(normalized["correct"]).strip().lower():
                    found_key = key
                    break
            
            if found_key:
                normalized["correct"] = found_key
            else:
                # Fallback to first option
                logger.warning(f"Could not validate correct answer '{correct}', using 'A'")
                normalized["correct"] = 'A'

        # Ensure explanation exists
        if "explanation" not in normalized or not normalized["explanation"]:
            normalized["explanation"] = "No explanation provided."

        # Add metadata
        normalized["parsed_successfully"] = True
        normalized["parser_version"] = "enhanced_v2"

        return normalized

    def _emergency_fallback_parse(self, text: str) -> Optional[Dict[str, Any]]:
        """CRITICAL FIX: Enhanced emergency fallback with better regex patterns"""
        try:
            logger.warning("Attempting emergency fallback parsing...")
            
            # Multiple question patterns
            question_patterns = [
                r"(?:Question|Q):\s*(.+?)(?=\n|A\)|Option A)",
                r"(?:Question|Q)\s*[:\-]\s*(.+?)(?=\n|A\)|Option A)",
                r"^\s*(.+?\?)\s*(?=\n|A\)|Option A)",
                r"(.+?\?)"  # Any sentence ending with ?
            ]
            
            question = None
            for pattern in question_patterns:
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE | re.DOTALL)
                if match:
                    question = match.group(1).strip()
                    if len(question) > 10:  # Minimum reasonable length
                        break
            
            if not question:
                logger.error("Could not extract question in fallback parsing")
                return None

            # Extract options with multiple patterns
            options = {}
            option_patterns = [
                (r'[Aa]\)\s*(.+?)(?=\n|B\)|$)', 'A'),
                (r'[Bb]\)\s*(.+?)(?=\n|C\)|$)', 'B'),
                (r'[Cc]\)\s*(.+?)(?=\n|D\)|$)', 'C'),
                (r'[Dd]\)\s*(.+?)(?=\n|Correct|Answer|Explanation|$)', 'D'),
                # Alternative patterns
                (r'Option A[:\-]\s*(.+?)(?=\n|Option B|$)', 'A'),
                (r'Option B[:\-]\s*(.+?)(?=\n|Option C|$)', 'B'),
                (r'Option C[:\-]\s*(.+?)(?=\n|Option D|$)', 'C'),
                (r'Option D[:\-]\s*(.+?)(?=\n|Correct|Answer|$)', 'D'),
            ]
            
            for pattern, key in option_patterns:
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE | re.DOTALL)
                if match:
                    option_text = match.group(1).strip()
                    if option_text and len(option_text) > 1:
                        options[key] = option_text

            if len(options) < 2:
                logger.error(f"Not enough options found in fallback: {len(options)}")
                return None

            # Extract correct answer
            correct_patterns = [
                r'(?:Correct|Answer):\s*([A-D])',
                r'(?:Correct|Answer)\s*[:\-]\s*([A-D])',
                r'Answer:\s*Option\s*([A-D])',
            ]
            
            correct = 'A'  # Default
            for pattern in correct_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    correct = match.group(1).upper()
                    break

            # Extract explanation
            explanation_patterns = [
                r'(?:Explanation|Reason):\s*(.+?)(?=\n\n|$)',
                r'(?:Explanation|Reason)\s*[:\-]\s*(.+?)(?=\n\n|$)',
                r'(?:Because|Since)\s+(.+?)(?=\n\n|$)'
            ]
            
            explanation = "No explanation provided."
            for pattern in explanation_patterns:
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE | re.DOTALL)
                if match:
                    explanation = match.group(1).strip()
                    if len(explanation) > 10:
                        break

            result = {
                "question": question,
                "options": options,
                "correct": correct,
                "explanation": explanation,
                "source": "emergency_fallback_parsing",
                "confidence": 0.6  # Lower confidence for fallback
            }
            
            logger.info(f"Emergency fallback successful: extracted {len(options)} options")
            return result
            
        except Exception as e:
            logger.error(f"Emergency fallback parsing also failed: {e}")
            return None


# Convenience function
def parse_mcq_response(raw_response: str) -> Optional[Dict[str, Any]]:
    """CRITICAL FIX: Enhanced convenience function with error handling"""
    try:
        parser = EnhancedMCQParser()
        return parser.parse_mcq(raw_response)
    except Exception as e:
        logger.error(f"Parse convenience function failed: {e}")
        return None