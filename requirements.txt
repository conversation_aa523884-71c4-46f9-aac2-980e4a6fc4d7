# Knowledge App - Comprehensive Requirements
# Core Python dependencies for the Knowledge App with Ollama integration

# ===== CORE FRAMEWORK =====
PyQt5>=5.15.0
PyQtWebEngine>=5.15.0

# ===== AI/ML CORE LIBRARIES =====
# Note: torch is optional for advanced features but not required for Ollama
torch>=2.0.0; platform_machine != "arm64"  # Skip on ARM for compatibility
torchvision>=0.15.0; platform_machine != "arm64"
transformers>=4.30.0
sentence-transformers>=2.2.0

# ===== LOCAL MODEL INFERENCE =====
llama-cpp-python>=0.3.9  # For GGUF model support with Ollama
requests>=2.31.0  # For Ollama API communication

# ===== ONLINE API PROVIDERS =====
openai>=1.0.0  # OpenAI API client
anthropic>=0.25.0  # Claude API client
groq>=0.4.0  # Groq API client
aiohttp>=3.8.0  # Async HTTP for API calls
aiofiles>=23.0.0  # Async file operations

# ===== DATA PROCESSING =====
numpy>=1.24.0
pandas>=2.0.0
pydantic>=2.0.0  # Data validation
python-dotenv>=1.0.0  # Environment variables

# ===== DOCUMENT PROCESSING =====
pdfplumber>=0.9.0  # PDF text extraction
Pillow>=10.0.0  # Image processing

# ===== VECTOR SEARCH & RAG =====
faiss-cpu>=1.7.4  # Vector similarity search
haystack>=1.20.0  # Document processing pipeline

# ===== ADVANCED ML (OPTIONAL) =====
# These are for advanced training features - not required for basic Ollama usage
peft>=0.8.0  # Parameter-Efficient Fine-Tuning
bitsandbytes>=0.41.0; platform_system != "Windows"  # Quantization (Linux/Mac only)
datasets>=2.14.0  # HuggingFace datasets
accelerate>=0.24.0  # Training acceleration

# ===== WEB FRAMEWORK =====
flask>=2.3.0  # Web server for API endpoints
flask-cors>=4.0.0  # CORS support

# ===== UTILITIES =====
six>=1.16.0  # Python 2/3 compatibility
tqdm>=4.65.0  # Progress bars
colorama>=0.4.6  # Colored terminal output
psutil>=5.9.0  # System monitoring
packaging>=23.0  # Version parsing

# ===== LOGGING & MONITORING =====
loguru>=0.7.0  # Enhanced logging

# ===== TESTING =====
pytest>=7.4.0  # Testing framework
pytest-asyncio>=0.21.0  # Async testing support

# ===== DEVELOPMENT TOOLS =====
black>=23.0.0  # Code formatting
isort>=5.12.0  # Import sorting
flake8>=6.0.0  # Linting

# ===== ELECTRON BRIDGE =====
# These are handled by Node.js/Electron, but listed for reference
# - electron (handled by npm)
# - node.js (system dependency)

# ===== PLATFORM-SPECIFIC NOTES =====
# Windows: Visual Studio Build Tools required for llama-cpp-python
# Linux: gcc, g++, cmake required for compilation
# macOS: Xcode command line tools required

# ===== OPTIONAL DEPENDENCIES =====
# Uncomment these for additional features:
# jupyter>=1.0.0  # Jupyter notebook support
# matplotlib>=3.7.0  # Plotting
# seaborn>=0.12.0  # Statistical plotting
# scikit-learn>=1.3.0  # Traditional ML algorithms
