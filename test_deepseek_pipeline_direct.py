#!/usr/bin/env python3
"""
Direct test of DeepSeek R1 pipeline to verify it's working
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_deepseek_pipeline():
    """Test the DeepSeek R1 pipeline directly"""
    try:
        print("🔥 Testing DeepSeek R1 Pipeline Direct")
        print("=" * 50)
        
        # Import the Ollama generator
        from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator
        
        # Create generator
        generator = OllamaJSONGenerator()
        
        # Initialize
        print("🔧 Initializing Ollama generator...")
        if not generator.initialize():
            print("❌ Failed to initialize Ollama generator")
            return False
        
        print(f"✅ Ollama generator initialized with model: {generator.active_model}")
        
        # Check if DeepSeek model is selected
        if 'deepseek' not in generator.active_model.lower():
            print(f"⚠️ WARNING: Active model '{generator.active_model}' is not DeepSeek!")
            print("   DeepSeek pipeline will not be triggered")
        else:
            print(f"🧠 DeepSeek model detected: {generator.active_model}")
            print("   DeepSeek reasoning pipeline will be used")
        
        # Test question generation
        print("\n🚀 Generating test question...")
        print("Topic: quantum mechanics")
        print("Difficulty: expert")
        print("Question Type: mixed")
        
        questions = generator.generate_mcq(
            topic="quantum mechanics",
            context="",
            num_questions=1,
            difficulty="expert",
            game_mode="casual",
            question_type="mixed"
        )
        
        if questions and len(questions) > 0:
            question = questions[0]
            print("\n✅ Question generated successfully!")
            print("=" * 50)
            print(f"Question: {question.get('question', 'N/A')}")
            print(f"Options: {question.get('options', [])}")
            print(f"Correct: {question.get('correct_answer', 'N/A')}")
            print(f"Explanation: {question.get('explanation', 'N/A')}")
            print(f"Generated by: {question.get('generated_by', 'N/A')}")
            print("=" * 50)
            return True
        else:
            print("❌ No questions generated")
            return False
            
    except Exception as e:
        print(f"❌ Error testing DeepSeek pipeline: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_available_models():
    """Test what models are available"""
    try:
        print("\n🔍 Checking available models...")
        
        from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator
        generator = OllamaJSONGenerator()
        
        # Get available models
        models = generator._get_available_models()
        print(f"Available models: {models}")
        
        # Check preferred models
        print(f"Preferred models: {generator.preferred_models}")
        
        # Initialize to see which model gets selected
        if generator.initialize():
            print(f"Selected model: {generator.active_model}")
        
    except Exception as e:
        print(f"❌ Error checking models: {e}")

if __name__ == "__main__":
    print("🧠 DeepSeek R1 Pipeline Direct Test")
    print("=" * 60)
    
    # Test available models first
    test_available_models()
    
    # Test the pipeline
    success = test_deepseek_pipeline()
    
    if success:
        print("\n🎉 DeepSeek pipeline test PASSED!")
    else:
        print("\n❌ DeepSeek pipeline test FAILED!")
    
    print("=" * 60)
