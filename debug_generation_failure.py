#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def debug_generation_failure():
    """Debug why question generation is completely failing"""
    
    print("🔍 DEBUGGING QUESTION GENERATION FAILURE")
    print("=" * 50)
    
    try:
        # Test 1: Check unified inference manager initialization
        print("\n1️⃣ Testing Unified Inference Manager...")
        from knowledge_app.core.unified_inference_manager import get_unified_inference_manager
        
        manager = get_unified_inference_manager()
        print(f"   Manager instance: {manager}")
        
        # Initialize it
        print("   Initializing manager...")
        success = await manager.initialize_async()
        print(f"   Initialization success: {success}")
        
        if not success:
            print("   ❌ Manager initialization failed!")
            return False
        
        # Check status
        status = manager.get_status()
        print(f"   Manager status: {status}")
        
        # Test 2: Try simple generation
        print("\n2️⃣ Testing Simple Generation...")
        try:
            # Use the correct sync method
            result = manager.generate_mcq_sync(
                topic="atoms",
                difficulty="medium",
                question_type="mixed",
                timeout=30.0
            )
            
            if result:
                print("   ✅ Generation successful!")
                print(f"   Question: {result.get('question', 'N/A')[:100]}...")
                return True
            else:
                print("   ❌ Generation returned None")
                return False
                
        except Exception as e:
            print(f"   ❌ Generation failed with error: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ollama_direct():
    """Test Ollama connection directly"""
    print("\n3️⃣ Testing Ollama Direct Connection...")
    
    try:
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            # Test Ollama health
            async with session.get("http://localhost:11434/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = [model['name'] for model in data.get('models', [])]
                    print(f"   ✅ Ollama is running with models: {models[:3]}...")
                    
                    # Test simple generation
                    payload = {
                        "model": "llama3.1:latest",
                        "prompt": "Generate a simple multiple choice question about atoms.",
                        "stream": False
                    }
                    
                    async with session.post("http://localhost:11434/api/generate", json=payload) as gen_response:
                        if gen_response.status == 200:
                            result = await gen_response.json()
                            print(f"   ✅ Direct Ollama generation works!")
                            print(f"   Response: {result.get('response', 'N/A')[:100]}...")
                            return True
                        else:
                            print(f"   ❌ Ollama generation failed: {gen_response.status}")
                            return False
                else:
                    print(f"   ❌ Ollama not responding: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"   ❌ Ollama test failed: {e}")
        return False

async def main():
    """Main debug function"""
    
    # Test Ollama first
    ollama_works = await test_ollama_direct()
    
    if not ollama_works:
        print("\n❌ Ollama is not working - this is the root cause!")
        return
    
    # Test unified manager
    manager_works = await debug_generation_failure()
    
    if not manager_works:
        print("\n❌ Unified manager is not working despite Ollama being available!")
        print("   This suggests an issue in the app's model initialization or connection logic.")
    else:
        print("\n✅ Everything works! The issue might be elsewhere.")

if __name__ == "__main__":
    asyncio.run(main())
