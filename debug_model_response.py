#!/usr/bin/env python3
"""
Debug what the model is actually generating
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging to see what's happening
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

def test_raw_ollama_response():
    """Test what Ollama is actually returning"""
    print("🔍 Testing raw Ollama response...")
    
    try:
        import requests
        
        # Test with a simple physics question prompt
        prompt = """Generate a multiple choice question about physics in JSON format:

{
  "question": "What is the unit of force?",
  "options": ["Newton", "Joule", "Watt", "Pascal"],
  "correct_answer": "A",
  "explanation": "The Newton is the SI unit of force."
}

Generate only the JSON:"""

        payload = {
            "model": "mathstral:latest",
            "prompt": prompt,
            "format": "json",
            "stream": False,
            "options": {
                "temperature": 0.4,
                "num_predict": 1800
            }
        }
        
        print(f"📤 Sending request to Ollama...")
        response = requests.post("http://localhost:11434/api/generate", json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            raw_response = result.get('response', '')
            
            print(f"✅ Got response from Ollama")
            print(f"📝 Raw response length: {len(raw_response)}")
            print(f"📄 Raw response:")
            print("-" * 50)
            print(raw_response)
            print("-" * 50)
            
            # Try to parse it as JSON
            try:
                parsed = json.loads(raw_response)
                print(f"✅ Successfully parsed as JSON!")
                print(f"📋 Parsed data: {parsed}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                
                # Try to extract JSON from the response
                import re
                json_match = re.search(r'\{.*\}', raw_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    print(f"🔍 Extracted JSON: {json_str}")
                    try:
                        parsed = json.loads(json_str)
                        print(f"✅ Successfully parsed extracted JSON!")
                        print(f"📋 Parsed data: {parsed}")
                        return True
                    except json.JSONDecodeError as e2:
                        print(f"❌ Extracted JSON parsing also failed: {e2}")
                
                return False
        else:
            print(f"❌ Ollama request failed with status {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ollama_generator_directly():
    """Test the Ollama generator class directly"""
    print("\n🎯 Testing Ollama generator class...")
    
    try:
        from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator
        
        # Create generator
        generator = OllamaJSONGenerator()
        
        # Initialize
        if not generator.initialize():
            print("❌ Failed to initialize generator")
            return False
        
        print("✅ Generator initialized")
        
        # Generate a question
        result = generator.generate_mcq("physics", "", 1, "medium", "casual", "mixed")
        
        print(f"📝 Generator result: {result}")
        
        if result and len(result) > 0:
            question = result[0]
            print(f"✅ Generated question: {question.get('question', 'N/A')}")
            print(f"📋 Options: {question.get('options', [])}")
            print(f"✅ Correct: {question.get('correct_answer', 'N/A')}")
            return True
        else:
            print("❌ No result from generator")
            return False
            
    except Exception as e:
        print(f"❌ Generator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_different_models():
    """Test with different models to see which works"""
    print("\n🔄 Testing different models...")
    
    models_to_test = [
        "deepseek-r1:32b",
        "llama3.1:latest", 
        "mathstral:latest"
    ]
    
    for model in models_to_test:
        print(f"\n🧪 Testing model: {model}")
        
        try:
            import requests
            
            prompt = """Generate a physics MCQ in JSON format:
{
  "question": "What is the unit of force?",
  "options": ["Newton", "Joule", "Watt", "Pascal"],
  "correct_answer": "A",
  "explanation": "The Newton is the SI unit of force."
}"""

            payload = {
                "model": model,
                "prompt": prompt,
                "format": "json",
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "num_predict": 500
                }
            }
            
            response = requests.post("http://localhost:11434/api/generate", json=payload, timeout=20)
            
            if response.status_code == 200:
                result = response.json()
                raw_response = result.get('response', '')
                
                print(f"✅ {model} responded")
                print(f"📝 Response: {raw_response[:200]}...")
                
                # Try to parse
                try:
                    parsed = json.loads(raw_response)
                    print(f"✅ {model} - JSON parsing successful!")
                    return model, parsed
                except:
                    print(f"❌ {model} - JSON parsing failed")
            else:
                print(f"❌ {model} - Request failed")
                
        except Exception as e:
            print(f"❌ {model} - Error: {e}")
    
    return None, None

def main():
    print("🧠 Model Response Debug Tool")
    print("=" * 60)
    
    # Test 1: Raw Ollama response
    raw_ok = test_raw_ollama_response()
    
    # Test 2: Generator class
    generator_ok = test_ollama_generator_directly()
    
    # Test 3: Different models
    working_model, sample_response = test_with_different_models()
    
    print("\n📊 SUMMARY:")
    print(f"   Raw Ollama: {'✅' if raw_ok else '❌'}")
    print(f"   Generator: {'✅' if generator_ok else '❌'}")
    print(f"   Working model: {working_model or '❌ None'}")
    
    if working_model:
        print(f"\n🎉 SUCCESS! Model {working_model} is working!")
        print(f"📋 Sample response: {sample_response}")
        return 0
    else:
        print("\n❌ All models failed to generate proper JSON")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
