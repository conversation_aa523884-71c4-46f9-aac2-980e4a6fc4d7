#!/usr/bin/env python3
"""
Test the app's question generation directly
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_question_generation():
    """Test question generation through the app's systems"""
    print("🔍 Testing app question generation...")
    
    try:
        # Import the unified inference manager
        from knowledge_app.core.unified_inference_manager import get_unified_inference_manager
        
        # Get the manager instance
        manager = get_unified_inference_manager()
        
        # Initialize it
        print("🔧 Initializing inference manager...")
        success = await manager.initialize_async()
        
        if not success:
            print("❌ Failed to initialize inference manager")
            return False
        
        print("✅ Inference manager initialized")
        
        # Test question generation
        print("🧠 Generating test question...")
        
        result = await manager.generate_mcq_async(
            topic="physics",
            difficulty="medium",
            question_type="mixed",
            context="Basic physics concepts",
            timeout=30.0
        )
        
        if result:
            print("✅ Question generated successfully!")
            print(f"📝 Question: {result.get('question', 'N/A')[:100]}...")
            print(f"🎯 Options: {len(result.get('options', []))} choices")
            print(f"✅ Correct: {result.get('correct', 'N/A')}")
            return True
        else:
            print("❌ No question generated")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ollama_generator_direct():
    """Test the Ollama generator directly"""
    print("\n🔍 Testing Ollama generator directly...")
    
    try:
        from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator
        
        # Create generator
        generator = OllamaJSONGenerator()
        
        # Initialize
        if not generator.initialize():
            print("❌ Failed to initialize Ollama generator")
            return False
        
        print("✅ Ollama generator initialized")
        
        # Generate question
        print("🧠 Generating question with Ollama...")
        
        questions = generator.generate_mcq(
            topic="mathematics",
            context="Basic algebra",
            num_questions=1,
            difficulty="medium",
            game_mode="casual",
            question_type="mixed"
        )
        
        if questions and len(questions) > 0:
            question = questions[0]
            print("✅ Question generated successfully!")
            print(f"📝 Question: {question.get('question', 'N/A')[:100]}...")
            print(f"🎯 Options: {len(question.get('options', []))} choices")
            return True
        else:
            print("❌ No questions generated")
            return False
            
    except Exception as e:
        print(f"❌ Direct test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🚀 App Question Generation Test")
    print("=" * 50)
    
    # Test 1: Direct Ollama generator
    result1 = await test_ollama_generator_direct()
    
    # Test 2: Through unified inference manager
    result2 = await test_question_generation()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   {'✅ PASS' if result1 else '❌ FAIL'} - Direct Ollama Generator")
    print(f"   {'✅ PASS' if result2 else '❌ FAIL'} - Unified Inference Manager")
    
    if result1 or result2:
        print("\n🎉 At least one method works! Your app should be able to generate questions.")
        print("💡 Try using a real topic like 'physics' or 'mathematics' instead of random text.")
    else:
        print("\n❌ Both methods failed. There may be a configuration issue.")
    
    return 0 if (result1 or result2) else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
