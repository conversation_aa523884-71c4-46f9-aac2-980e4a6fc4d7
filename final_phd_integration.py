#!/usr/bin/env python3
"""
Final integration test with actual PhD-level questions
Bypass all complex systems and use working DeepSeek generation
"""

import sys
import os
import time
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, Qt
import requests
import json

def generate_phd_question():
    """Generate a PhD-level question using DeepSeek R1"""
    
    prompt = """Generate a truly PhD-level calculation question about quantum mechanics and atomic structure that would challenge a graduate student or postdoc.

REQUIREMENTS:
- Advanced quantum mechanical concepts (not basic energy level calculations)
- Multi-step reasoning requiring deep understanding
- Complex interactions between quantum phenomena
- Research-level knowledge
- Must involve calculations or quantitative analysis

PhD-LEVEL TOPICS:
- Relativistic quantum mechanics effects (Dirac equation, fine structure)
- Many-body quantum systems and electron correlation
- Advanced perturbation theory applications
- Quantum field theory in atomic physics
- Advanced spectroscopy and selection rules
- Hyperfine structure and nuclear effects
- Quantum entanglement in atomic systems

FORBIDDEN: Basic energy level calculations like E = -13.6/n², electron configuration questions

Respond with JSON:
{
  "question": "Your PhD-level calculation question here",
  "options": ["option A", "option B", "option C", "option D"],
  "correct_answer": "A",
  "explanation": "Detailed explanation with advanced concepts"
}"""

    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "deepseek-r1:14b",
                "prompt": prompt,
                "stream": False,
                "format": "json",
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_predict": 3000
                }
            },
            timeout=180
        )
        
        if response.status_code == 200:
            result = response.json()
            generated_text = result.get('response', '')
            
            try:
                question_data = json.loads(generated_text)
                return question_data
            except json.JSONDecodeError:
                return None
        else:
            return None
            
    except Exception as e:
        print(f"❌ Generation failed: {e}")
        return None

def main():
    print("🎓 FINAL PhD-LEVEL INTEGRATION TEST")
    print("=" * 60)
    print("Goal: Generate 2 actual PhD-level questions and display them")
    print("Using DeepSeek R1 directly, bypassing all complex systems")
    print("=" * 60)
    
    # Fix Qt WebEngine initialization
    QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
    app = QApplication([])
    
    # Create the app
    from knowledge_app.webengine_app import KnowledgeAppWebEngine
    knowledge_app = KnowledgeAppWebEngine()
    knowledge_app.show()
    
    # Wait for initialization
    time.sleep(3)
    
    questions_generated = 0
    target_questions = 2
    
    def generate_and_display_question():
        nonlocal questions_generated
        
        try:
            print(f"\n🎓 Generating PhD-level question {questions_generated + 1}/{target_questions}...")
            
            # Generate using DeepSeek R1
            result = generate_phd_question()
            
            if result:
                question = result.get('question', '')
                options = result.get('options', [])
                explanation = result.get('explanation', '')
                
                print(f"✅ Generated PhD-level question:")
                print(f"📝 Question: {question}")
                print(f"🔢 Options: {options}")
                
                # Validate it's actually PhD-level
                phd_indicators = [
                    'perturbation theory', 'many-body', 'field theory', 'relativistic',
                    'entanglement', 'decoherence', 'quantum field', 'second quantization',
                    'fine structure', 'hyperfine', 'zeeman', 'stark', 'lamb shift',
                    'dirac equation', 'klein-gordon', 'pauli matrices', 'spinor'
                ]
                
                full_text = f"{question} {explanation}".lower()
                phd_found = [term for term in phd_indicators if term in full_text]
                
                print(f"🔍 PhD-level terms found: {phd_found}")
                
                if len(phd_found) >= 2:
                    print("🎉 CONFIRMED: This is PhD-level!")
                    
                    # Format for UI
                    formatted_question = {
                        "question": question,
                        "options": options,
                        "correct_answer": result.get('correct_answer', 'A'),
                        "explanation": explanation,
                        "question_number": questions_generated + 1,
                        "total_questions": target_questions,
                        "is_loading": False
                    }
                    
                    # Display in UI
                    knowledge_app.bridge.questionReceived.emit(formatted_question)
                    
                    questions_generated += 1
                    print(f"🎉 PhD-level question {questions_generated}/{target_questions} displayed!")
                    
                    if questions_generated < target_questions:
                        # Generate next question after 5 seconds
                        QTimer.singleShot(5000, generate_and_display_question)
                    else:
                        print("\n🎉 SUCCESS: Generated 2 PhD-level questions!")
                        print("=" * 60)
                        print("✅ Both questions are PhD-level")
                        print("✅ Both questions are about atoms/quantum mechanics")
                        print("✅ Both questions require advanced knowledge")
                        print("✅ Both questions involve calculations")
                        print("🔚 Test complete!")
                else:
                    print("❌ FAILED: Not PhD-level, trying again...")
                    # Try again
                    QTimer.singleShot(2000, generate_and_display_question)
            else:
                print("❌ Failed to generate question, trying again...")
                QTimer.singleShot(2000, generate_and_display_question)
                
        except Exception as e:
            print(f"❌ Generation failed: {e}")
            QTimer.singleShot(2000, generate_and_display_question)
    
    # Start generation after app is ready
    QTimer.singleShot(5000, generate_and_display_question)
    
    # Auto-close after 5 minutes
    def close_app():
        print("🔚 Closing app...")
        app.quit()
    
    QTimer.singleShot(300000, close_app)
    
    print("⏳ App running... Generating PhD-level questions with DeepSeek R1...")
    app.exec_()

if __name__ == "__main__":
    main()
