#!/usr/bin/env python3
"""
Comprehensive stress test for semantic keyword mapping with random inputs
Tests Phi model's ability to handle various types of inputs and prompt selection
"""

import sys
import os
import time
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_semantic_mapping_stress():
    """Test semantic mapping with a variety of random and challenging inputs"""
    print("🧠 SEMANTIC MAPPING STRESS TEST")
    print("=" * 60)
    
    # Mix of abbreviations, typos, random words, technical terms, etc.
    test_inputs = [
        # Common abbreviations
        "AI", "ML", "CBT", "MBT", "FHD", "HD", "SVD", "API", "SQL", "HTTP",
        
        # Medical/Psychology terms
        "PTSD", "ADHD", "OCD", "MRI", "CT", "ECG", "DNA", "RNA", "PCR",
        
        # Technology/Computing
        "GPU", "CPU", "RAM", "SSD", "IoT", "VR", "AR", "5G", "WiFi", "LAN",
        
        # Random/Misspelled words
        "quantom", "phisics", "mathemetics", "biologgy", "chemestry",
        
        # Single letters/numbers
        "X", "Y", "Z", "42", "π", "∞",
        
        # Weird combinations
        "AI-ML", "CBT/DBT", "HTTP/2", "5G-WiFi", "DNA-RNA",
        
        # Foreign/Technical terms
        "schadenfreude", "zeitgeist", "kaizen", "ubuntu", "feng shui",
        
        # Random nonsense
        "xyzabc", "qwerty", "asdfgh", "123abc", "!@#$%",
        
        # Long technical terms
        "electroencephalography", "pneumonoultramicroscopicsilicovolcanoconiosiss",
        
        # Empty/edge cases
        "", " ", "   ", "\n", "\t"
    ]
    
    try:
        from knowledge_app.core.intelligent_semantic_mapper import get_semantic_mapper
        
        mapper = get_semantic_mapper()
        print(f"✅ Semantic mapper initialized")
        print(f"📋 Testing {len(test_inputs)} diverse inputs...")
        print()
        
        results = []
        total_time = 0
        
        for i, test_input in enumerate(test_inputs):
            print(f"🧪 Test {i+1:2d}: '{test_input}' ", end="")
            
            start_time = time.time()
            try:
                result = mapper.map_topic_semantically(test_input)
                end_time = time.time()
                
                call_time = end_time - start_time
                total_time += call_time
                
                # Analyze the result
                success = True
                analysis = {
                    "input": test_input,
                    "expanded": result.expanded_topic,
                    "type": result.question_type,
                    "confidence": result.confidence,
                    "reasoning": result.reasoning,
                    "is_abbrev": result.is_abbreviation,
                    "full_form": result.full_form,
                    "time": call_time
                }
                
                # Quick result summary
                if call_time < 0.1:
                    print(f"💨 CACHE ({call_time:.3f}s)")
                else:
                    print(f"⏱️  {call_time:.2f}s")
                
                print(f"     → '{result.expanded_topic}' [{result.question_type}] (conf: {result.confidence:.2f})")
                if result.is_abbreviation and result.full_form:
                    print(f"     📝 Full form: {result.full_form}")
                print(f"     🧠 Reasoning: {result.reasoning}")
                print()
                
                results.append(analysis)
                
            except Exception as e:
                print(f"❌ FAILED: {e}")
                results.append({
                    "input": test_input,
                    "error": str(e),
                    "time": time.time() - start_time
                })
                print()
        
        # Analysis summary
        print("=" * 60)
        print("📊 STRESS TEST ANALYSIS")
        print("=" * 60)
        
        successful = [r for r in results if "error" not in r]
        failed = [r for r in results if "error" in r]
        
        print(f"✅ Successful: {len(successful)}/{len(test_inputs)} ({len(successful)/len(test_inputs)*100:.1f}%)")
        print(f"❌ Failed: {len(failed)}/{len(test_inputs)} ({len(failed)/len(test_inputs)*100:.1f}%)")
        print(f"⏱️  Average time: {total_time/len(test_inputs):.3f}s")
        print(f"🚀 Total time: {total_time:.2f}s")
        print()
        
        # Categorize results
        if successful:
            abbreviations = [r for r in successful if r["is_abbrev"]]
            conceptual = [r for r in successful if r["type"] == "conceptual"]
            numerical = [r for r in successful if r["type"] == "numerical"]
            mixed = [r for r in successful if r["type"] == "mixed"]
            high_conf = [r for r in successful if r["confidence"] > 0.8]
            
            print("🔍 CATEGORIZATION:")
            print(f"   📝 Abbreviations detected: {len(abbreviations)}")
            print(f"   🧠 Conceptual questions: {len(conceptual)}")
            print(f"   🔢 Numerical questions: {len(numerical)}")
            print(f"   🔀 Mixed questions: {len(mixed)}")
            print(f"   🎯 High confidence (>0.8): {len(high_conf)}")
            print()
            
            # Show some interesting examples
            print("🌟 INTERESTING EXAMPLES:")
            for r in successful[:10]:  # Show first 10
                if r["is_abbrev"] or r["confidence"] > 0.8:
                    print(f"   '{r['input']}' → '{r['expanded']}' [{r['type']}] (conf: {r['confidence']:.2f})")
        
        if failed:
            print("\n❌ FAILED INPUTS:")
            for r in failed:
                print(f"   '{r['input']}': {r['error']}")
        
        return len(successful) > len(failed)
        
    except Exception as e:
        print(f"❌ Stress test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_intelligence():
    """Test if Phi model is intelligently selecting/creating prompts"""
    print("\n🎯 PROMPT INTELLIGENCE TEST")
    print("=" * 60)
    
    # Test cases that should trigger different prompt strategies
    test_cases = [
        {
            "input": "CBT",
            "expected_behavior": "Should recognize as Cognitive Behavioral Therapy and use psychology prompts"
        },
        {
            "input": "AI",
            "expected_behavior": "Should recognize as Artificial Intelligence and use tech prompts"
        },
        {
            "input": "quantom",
            "expected_behavior": "Should correct to 'quantum' and use physics prompts"
        },
        {
            "input": "DNA",
            "expected_behavior": "Should recognize as biology and use science prompts"
        },
        {
            "input": "xyzabc",
            "expected_behavior": "Should handle unknown input gracefully"
        }
    ]
    
    try:
        from knowledge_app.core.intelligent_semantic_mapper import get_semantic_mapper
        
        mapper = get_semantic_mapper()
        
        for i, case in enumerate(test_cases):
            print(f"\n🧪 Prompt Test {i+1}: '{case['input']}'")
            print(f"Expected: {case['expected_behavior']}")
            
            result = mapper.map_topic_semantically(case['input'])
            
            print(f"✅ Result:")
            print(f"   Expanded: '{result.expanded_topic}'")
            print(f"   Type: {result.question_type}")
            print(f"   Confidence: {result.confidence:.2f}")
            print(f"   Reasoning: {result.reasoning}")
            
            # Analyze if the prompt selection seems intelligent
            reasoning_quality = len(result.reasoning) > 20  # Has substantial reasoning
            expansion_quality = result.expanded_topic != case['input']  # Actually expanded
            confidence_reasonable = 0.3 <= result.confidence <= 1.0  # Reasonable confidence
            
            intelligence_score = sum([reasoning_quality, expansion_quality, confidence_reasonable])
            print(f"🧠 Intelligence Score: {intelligence_score}/3")
            
            if intelligence_score >= 2:
                print("✅ Prompt selection appears intelligent")
            else:
                print("⚠️ Prompt selection may need improvement")
        
        return True
        
    except Exception as e:
        print(f"❌ Prompt intelligence test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mcq_generation_with_random_topics():
    """Test full MCQ generation with random topics to see end-to-end behavior"""
    print("\n🚀 END-TO-END MCQ GENERATION TEST")
    print("=" * 60)
    
    random_topics = ["CBT", "AI", "quantom", "DNA", "xyzabc", "HTTP", "PTSD"]
    
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        mcq_manager = get_mcq_manager()
        
        for i, topic in enumerate(random_topics):
            print(f"\n🧪 MCQ Test {i+1}: '{topic}'")
            
            quiz_params = {
                "topic": topic,
                "difficulty": "medium",
                "game_mode": "casual",
                "submode": "mixed",
                "num_questions": 1,
                "mode": "auto"
            }
            
            try:
                start_time = time.time()
                result = mcq_manager.generate_quiz(quiz_params)
                end_time = time.time()
                
                if result and hasattr(result, 'question'):
                    print(f"✅ Generated in {end_time - start_time:.2f}s")
                    print(f"   Question: {result.question[:100]}...")
                    print(f"   Options: {len(result.options)} choices")
                    print(f"   Correct: {result.correct_answer}")
                else:
                    print("❌ Generation failed or returned invalid result")
                    
            except Exception as e:
                print(f"❌ Generation failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ End-to-end test setup failed: {e}")
        return False

def main():
    """Run comprehensive semantic mapping tests"""
    print("🎯 COMPREHENSIVE SEMANTIC MAPPING TEST SUITE")
    print("=" * 70)
    
    tests = [
        ("Semantic Mapping Stress Test", test_semantic_mapping_stress),
        ("Prompt Intelligence Test", test_prompt_intelligence),
        ("End-to-End MCQ Generation", test_mcq_generation_with_random_topics)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🚀 Starting: {test_name}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 70)
    print("📊 FINAL TEST RESULTS:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} test suites passed")
    
    if passed == len(results):
        print("🎉 Semantic mapping system is working excellently!")
    else:
        print("⚠️ Some issues detected. Check output above for details.")

if __name__ == "__main__":
    main()
