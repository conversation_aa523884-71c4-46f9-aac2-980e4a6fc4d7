#!/usr/bin/env python3
"""
Test the intelligent topic resolution and question generation
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_topic_resolution():
    """Test intelligent topic resolution"""
    print("🧠 Testing Intelligent Topic Resolution")
    print("=" * 50)
    
    try:
        from knowledge_app.core.intelligent_topic_resolver import get_intelligent_topic_resolver
        
        resolver = get_intelligent_topic_resolver()
        
        # Test cases including the problematic ones
        test_inputs = [
            "dfs",
            "sdfsf", 
            "physics",
            "math",
            "ai",
            "x",
            "123",
            "random text here",
            "",
            "bfs",
            "ml"
        ]
        
        for test_input in test_inputs:
            print(f"\n🔍 Testing: '{test_input}'")
            result = resolver.resolve_topic(test_input)
            
            print(f"   ✅ Resolved to: {result['resolved_topic']}")
            print(f"   📚 Subject: {result['subject_area']}")
            print(f"   🎯 Confidence: {result['confidence']:.2f}")
            print(f"   🔧 Method: {result['resolution_method']}")
            print(f"   📝 Context: {result['context'][:80]}...")
            
        return True
        
    except Exception as e:
        print(f"❌ Topic resolution test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_generation():
    """Test intelligent prompt generation"""
    print("\n🧠 Testing Intelligent Prompt Generation")
    print("=" * 50)
    
    try:
        from knowledge_app.core.intelligent_prompt_generator import get_intelligent_prompt_generator
        
        generator = get_intelligent_prompt_generator()
        
        # Test the problematic case
        test_input = "dfs"
        print(f"\n🔍 Testing prompt generation for: '{test_input}'")
        
        result = generator.generate_intelligent_prompt(
            raw_input=test_input,
            difficulty="medium",
            question_type="mixed"
        )
        
        print(f"   ✅ Original: {result['metadata']['original_input']}")
        print(f"   🎯 Resolved: {result['metadata']['resolved_topic']}")
        print(f"   📚 Subject: {result['metadata']['subject_area']}")
        print(f"   🔧 Method: {result['metadata']['resolution_method']}")
        print(f"   📈 Confidence: {result['confidence']:.2f}")
        print(f"\n📝 Generated Prompt Preview:")
        print(f"   {result['prompt'][:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Prompt generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ollama_integration():
    """Test integration with Ollama generator"""
    print("\n🔥 Testing Ollama Integration")
    print("=" * 50)
    
    try:
        from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator
        
        generator = OllamaJSONGenerator()
        
        if not generator.initialize():
            print("❌ Failed to initialize Ollama generator")
            return False
        
        print("✅ Ollama generator initialized")
        
        # Test with the problematic input
        test_input = "dfs"
        print(f"\n🔍 Testing question generation for: '{test_input}'")
        
        questions = generator.generate_mcq(
            topic=test_input,
            context="",
            num_questions=1,
            difficulty="medium",
            game_mode="casual",
            question_type="mixed"
        )
        
        if questions and len(questions) > 0:
            question = questions[0]
            print(f"✅ Question generated successfully!")
            print(f"📝 Original input: {question.get('original_input', 'N/A')}")
            print(f"🎯 Resolved topic: {question.get('resolved_topic', 'N/A')}")
            print(f"❓ Question: {question.get('question', 'N/A')[:100]}...")
            print(f"🔢 Options: {len(question.get('options', []))} choices")
            print(f"✅ Correct: {question.get('correct', 'N/A')}")
            return True
        else:
            print("❌ No questions generated")
            return False
            
    except Exception as e:
        print(f"❌ Ollama integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 Intelligent Question Generation Test")
    print("=" * 60)
    
    tests = [
        ("Topic Resolution", test_topic_resolution),
        ("Prompt Generation", test_prompt_generation),
        ("Ollama Integration", test_ollama_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Intelligent generation should work with ANY input!")
        print("💡 Now 'dfs', 'sdfsf', or any random text should generate meaningful questions.")
    elif passed >= 2:
        print("⚠️ Most tests passed. Basic intelligent functionality should work.")
    else:
        print("❌ Major issues detected. Check the implementation.")
    
    return 0 if passed >= 2 else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
