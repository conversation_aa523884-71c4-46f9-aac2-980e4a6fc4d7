#!/usr/bin/env python3
"""
Lines of Code Counter for Knowledge App
Counts all necessary code files including frontend and backend
Outputs to a single .txt file and always scans from scratch
"""

import os
import sys
from pathlib import Path
from collections import defaultdict
import datetime

# File extensions to count
CODE_EXTENSIONS = {
    # Python files
    '.py': 'Python',
    
    # Web frontend files
    '.js': 'JavaScript',
    '.html': 'HTML',
    '.css': 'CSS',
    '.jsx': 'React JSX',
    '.ts': 'TypeScript',
    '.tsx': 'TypeScript JSX',
    '.vue': 'Vue',
    
    # Configuration files
    '.json': 'JSON Config',
    '.yaml': 'YAML Config',
    '.yml': 'YAML Config',
    '.toml': 'TOML Config',
    '.ini': 'INI Config',
    
    # Documentation
    '.md': 'Markdown',
    '.rst': 'reStructuredText',
    
    # SQL files
    '.sql': 'SQL',
    
    # Shell scripts
    '.sh': 'Shell Script',
    '.bat': 'Batch Script',
    '.ps1': 'PowerShell',
}

# Directories to exclude from counting
EXCLUDE_DIRS = {
    '__pycache__',
    '.git',
    '.vscode',
    '.idea',
    'node_modules',
    'venv',
    'env',
    '.env',
    'cache',
    'logs',
    'temp',
    'tmp',
    '.pytest_cache',
    'htmlcov',
    'coverage',
    'dist',
    'build',
    'egg-info',
}

# Files to exclude
EXCLUDE_FILES = {
    'countloc.py',  # Don't count this script itself
    '.gitignore',
    '.gitkeep',
    'requirements.txt',  # Already counted as config if needed
}

def count_lines_in_file(file_path):
    """Count lines in a file, handling encoding issues gracefully"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            total_lines = len(lines)

            # Count non-empty lines and comment lines
            non_empty_lines = 0
            comment_lines = 0
            pure_code_lines = []  # 🚀 NEW: Store only pure code lines

            for line in lines:
                stripped = line.strip()
                if stripped:
                    non_empty_lines += 1
                    # Enhanced comment detection
                    is_comment = False
                    if (stripped.startswith('#') or
                        stripped.startswith('//') or
                        stripped.startswith('/*') or
                        stripped.startswith('*') or
                        stripped.startswith('<!--') or
                        stripped.startswith('"""') or
                        stripped.startswith("'''") or
                        stripped.startswith('*/')):
                        comment_lines += 1
                        is_comment = True

                    # 🚀 CRITICAL: Only store pure code lines (no comments, no empty lines)
                    if not is_comment:
                        pure_code_lines.append(stripped)

            return {
                'total': total_lines,
                'non_empty': non_empty_lines,
                'comments': comment_lines,
                'code': non_empty_lines - comment_lines,
                'pure_code_lines': pure_code_lines  # 🚀 NEW: Pure code only
            }
    except (UnicodeDecodeError, PermissionError, FileNotFoundError):
        # Try with different encoding
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                lines = f.readlines()
                return {
                    'total': len(lines),
                    'non_empty': len([l for l in lines if l.strip()]),
                    'comments': 0,  # Skip comment detection for non-UTF8 files
                    'code': len([l for l in lines if l.strip()])
                }
        except Exception as e:
            print(f"Warning: Could not read {file_path}: {e}")
            return {'total': 0, 'non_empty': 0, 'comments': 0, 'code': 0, 'pure_code_lines': []}

def should_exclude_path(path):
    """Check if a path should be excluded"""
    path_parts = Path(path).parts
    
    # Check if any part of the path is in exclude dirs
    for part in path_parts:
        if part in EXCLUDE_DIRS:
            return True
    
    # Check if filename is in exclude files
    if Path(path).name in EXCLUDE_FILES:
        return True
    
    return False

def scan_codebase(root_dir):
    """Scan the codebase and count lines of code"""
    stats = defaultdict(lambda: {
        'files': 0,
        'total_lines': 0,
        'non_empty_lines': 0,
        'comment_lines': 0,
        'code_lines': 0,
        'file_list': [],
        'pure_code_lines': []  # 🚀 NEW: Store all pure code lines
    })
    
    root_path = Path(root_dir)
    
    for file_path in root_path.rglob('*'):
        if file_path.is_file():
            # Skip excluded paths
            if should_exclude_path(file_path):
                continue
            
            # Check if file extension is in our list
            extension = file_path.suffix.lower()
            if extension in CODE_EXTENSIONS:
                file_type = CODE_EXTENSIONS[extension]
                
                # Count lines in this file
                line_counts = count_lines_in_file(file_path)
                
                # Update statistics
                stats[file_type]['files'] += 1
                stats[file_type]['total_lines'] += line_counts['total']
                stats[file_type]['non_empty_lines'] += line_counts['non_empty']
                stats[file_type]['comment_lines'] += line_counts['comments']
                stats[file_type]['code_lines'] += line_counts['code']
                stats[file_type]['pure_code_lines'].extend(line_counts['pure_code_lines'])  # 🚀 NEW
                stats[file_type]['file_list'].append({
                    'path': str(file_path.relative_to(root_path)),
                    'lines': line_counts
                })
    
    return dict(stats)

def read_file_content(file_path):
    """Read file content safely with encoding handling"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except (UnicodeDecodeError, PermissionError, FileNotFoundError):
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()
        except Exception as e:
            return f"[ERROR: Could not read file - {e}]"

def generate_report(stats, output_file, root_dir):
    """🚀 Generate PURE CODE ONLY report - no empty lines, no comments, no metadata"""
    with open(output_file, 'w', encoding='utf-8') as f:
        # Header
        f.write("=" * 80 + "\n")
        f.write("KNOWLEDGE APP - PURE CODE EXTRACTION (NO BLOAT)\n")
        f.write("=" * 80 + "\n")
        f.write(f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Scanned from: {os.getcwd()}\n\n")

        # Summary table
        f.write("SUMMARY BY FILE TYPE\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'File Type':<20} {'Files':<8} {'Total':<10} {'Non-Empty':<12} {'Comments':<10} {'Code':<10}\n")
        f.write("-" * 80 + "\n")

        total_files = 0
        total_lines = 0
        total_non_empty = 0
        total_comments = 0
        total_code = 0

        for file_type, data in sorted(stats.items()):
            f.write(f"{file_type:<20} {data['files']:<8} {data['total_lines']:<10} "
                   f"{data['non_empty_lines']:<12} {data['comment_lines']:<10} {data['code_lines']:<10}\n")

            total_files += data['files']
            total_lines += data['total_lines']
            total_non_empty += data['non_empty_lines']
            total_comments += data['comment_lines']
            total_code += data['code_lines']

        f.write("-" * 80 + "\n")
        f.write(f"{'TOTAL':<20} {total_files:<8} {total_lines:<10} "
               f"{total_non_empty:<12} {total_comments:<10} {total_code:<10}\n")
        f.write("-" * 80 + "\n\n")

        # Calculate pure code statistics
        total_pure_code_lines = sum(len(data['pure_code_lines']) for data in stats.values())

        # Final summary
        f.write(f"FINAL SUMMARY\n")
        f.write("=" * 40 + "\n")
        f.write(f"Total Files Analyzed: {total_files}\n")
        f.write(f"Total Lines (with bloat): {total_lines:,}\n")
        f.write(f"Non-Empty Lines: {total_non_empty:,}\n")
        f.write(f"Comment Lines: {total_comments:,}\n")
        f.write(f"Code Lines: {total_code:,}\n")
        f.write(f"🚀 PURE CODE LINES: {total_pure_code_lines:,}\n")
        f.write(f"Code Density: {(total_code/total_lines*100):.1f}%\n" if total_lines > 0 else "Code Density: 0%\n")
        f.write(f"🚀 BLOAT REMOVED: {total_lines - total_pure_code_lines:,} lines ({((total_lines - total_pure_code_lines)/total_lines*100):.1f}%)\n")
        f.write("\n" + "=" * 80 + "\n")
        f.write("🚀 PURE CODE DUMP - NO EMPTY LINES, NO COMMENTS, NO METADATA\n")
        f.write("=" * 80 + "\n\n")

        # 🚀 PURE CODE DUMP - Only actual code lines, no bloat
        for file_type, data in sorted(stats.items()):
            if not data['pure_code_lines']:  # Skip if no pure code
                continue

            f.write(f"\n{'#' * 80}\n")
            f.write(f"# {file_type.upper()} - PURE CODE ONLY ({len(data['pure_code_lines'])} lines)\n")
            f.write(f"{'#' * 80}\n\n")

            # Write all pure code lines for this file type
            for i, code_line in enumerate(data['pure_code_lines'], 1):
                f.write(f"{code_line}\n")

            f.write(f"\n{'#' * 80}\n")
            f.write(f"# END {file_type.upper()} - {len(data['pure_code_lines'])} pure code lines\n")
            f.write(f"{'#' * 80}\n\n")

def main():
    """Main function"""
    print("Knowledge App - Lines of Code Counter")
    print("=" * 40)
    
    # Get the current directory (should be the app root)
    root_dir = os.getcwd()
    output_file = "knowledge_app_loc_analysis.txt"
    
    print(f"Scanning codebase from: {root_dir}")
    print("This may take a moment...")
    
    # Remove existing output file to ensure fresh scan
    if os.path.exists(output_file):
        os.remove(output_file)
        print(f"Removed existing {output_file}")
    
    # Scan the codebase
    stats = scan_codebase(root_dir)
    
    if not stats:
        print("No code files found!")
        return
    
    # Generate report
    generate_report(stats, output_file, root_dir)
    
    print(f"\nAnalysis complete!")
    print(f"Report saved to: {output_file}")
    
    # Print quick summary
    total_files = sum(data['files'] for data in stats.values())
    total_lines = sum(data['total_lines'] for data in stats.values())
    total_code = sum(data['code_lines'] for data in stats.values())
    total_pure_code = sum(len(data['pure_code_lines']) for data in stats.values())
    bloat_removed = total_lines - total_pure_code

    print(f"\n🚀 BLOAT ANALYSIS SUMMARY:")
    print(f"- {total_files} files analyzed")
    print(f"- {total_lines:,} total lines (with bloat)")
    print(f"- {total_code:,} lines of code")
    print(f"- 🚀 {total_pure_code:,} PURE CODE LINES")
    print(f"- 🗑️ {bloat_removed:,} lines of BLOAT REMOVED ({(bloat_removed/total_lines*100):.1f}%)")
    print(f"- 📊 Code efficiency: {(total_pure_code/total_lines*100):.1f}%")
    
    print(f"\nFile types found:")
    for file_type, data in sorted(stats.items()):
        print(f"- {file_type}: {data['files']} files ({data['code_lines']:,} lines of code)")

if __name__ == "__main__":
    main()
