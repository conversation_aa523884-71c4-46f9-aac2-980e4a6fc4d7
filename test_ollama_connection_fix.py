#!/usr/bin/env python3
"""
Test the fixed Ollama connection
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_ollama_connection_fix():
    """Test if the Ollama connection fix works"""
    print("🔧 TESTING OLLAMA CONNECTION FIX")
    print("=" * 50)
    
    try:
        print("\n1️⃣ Testing OllamaModelInference...")
        from knowledge_app.core.ollama_model_inference import OllamaModelInference
        
        # Create instance
        ollama = OllamaModelInference()
        print(f"   ✅ OllamaModelInference created")
        print(f"   🤖 Active model: {ollama.active_model}")
        print(f"   🚀 Available: {ollama.is_available()}")
        
        if ollama.is_available():
            print("   ✅ Ollama connection working!")
        else:
            print("   ❌ Ollama connection still failing")
            return False
            
    except Exception as e:
        print(f"   ❌ OllamaModelInference error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    try:
        print("\n2️⃣ Testing OfflineMCQGenerator...")
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator
        
        # Create instance
        generator = OfflineMCQGenerator()
        print(f"   ✅ OfflineMCQGenerator created")
        
        # Test initialization
        success = generator.initialize()
        print(f"   🚀 Initialized: {success}")
        
        if success:
            print("   ✅ OfflineMCQGenerator working!")
        else:
            print("   ❌ OfflineMCQGenerator initialization failed")
            return False
            
    except Exception as e:
        print(f"   ❌ OfflineMCQGenerator error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    try:
        print("\n3️⃣ Testing UnifiedInferenceManager...")
        from knowledge_app.core.unified_inference_manager import get_unified_inference_manager
        
        # Create instance
        manager = get_unified_inference_manager()
        status = manager.get_status()
        print(f"   📊 Status: {status}")
        
        local_available = status.get('local_available', False)
        print(f"   🏠 Local available: {local_available}")
        
        if local_available:
            print("   ✅ UnifiedInferenceManager detects local models!")
            return True
        else:
            print("   ❌ UnifiedInferenceManager still not detecting local models")
            return False
            
    except Exception as e:
        print(f"   ❌ UnifiedInferenceManager error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ollama_connection_fix()
    if success:
        print("\n🎉 SUCCESS: Ollama connection fix is working!")
    else:
        print("\n❌ FAILED: Ollama connection still has issues")
