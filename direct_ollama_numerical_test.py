#!/usr/bin/env python3
"""
Direct test of Ollama with numerical override
"""

import requests
import json
import re

def test_direct_numerical_generation():
    """Test direct Ollama generation with numerical override"""
    
    # Simple numerical prompt that should work
    prompt = """Generate a calculation question about hydrogen atom energy levels.

TEMPLATE: "Calculate the energy of an electron in the n=X shell of hydrogen. Given that the ground state energy (n=1) is -13.6 eV, what is the energy for n=X?"

Use the formula E = -13.6/n² where n is the shell number.
Pick a shell number between 2 and 5.
Calculate the correct answer and create 3 wrong answers.

Example: For n=3, E = -13.6/(3²) = -13.6/9 = -1.51 eV

Respond with JSON:
{
  "question": "Calculate the energy of an electron in the n=4 shell of hydrogen. Given that the ground state energy (n=1) is -13.6 eV, what is the energy for n=4?",
  "options": ["-0.85 eV", "-1.51 eV", "-3.40 eV", "-6.04 eV"],
  "correct_answer": "A",
  "explanation": "Using E = -13.6/n² formula: E = -13.6/(4²) = -13.6/16 = -0.85 eV"
}

CRITICAL: You MUST respond with ONLY valid JSON. No explanations, no text before or after the JSON."""

    try:
        print("🔢 Testing direct numerical generation with Ollama...")
        
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "llama3.1:8b",
                "prompt": prompt,
                "stream": False,
                "format": "json",
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9
                }
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            generated_text = result.get('response', '')
            
            print(f"✅ Generated response ({len(generated_text)} chars):")
            print(f"📝 Response: {generated_text}")
            
            # Try to parse as JSON
            try:
                question_data = json.loads(generated_text)
                
                question = question_data.get('question', '')
                options = question_data.get('options', [])
                
                print(f"\n✅ Successfully parsed JSON:")
                print(f"   Question: {question}")
                print(f"   Options: {options}")
                print(f"   Correct: {question_data.get('correct_answer', 'N/A')}")
                
                # Check if it's actually numerical
                has_numbers = bool(re.search(r'\d+', question)) or any(
                    re.search(r'\d+', str(opt)) for opt in options
                )
                
                calc_keywords = ['calculate', 'compute', 'energy', 'wavelength', 'binding']
                has_calc_keywords = any(kw in question.lower() for kw in calc_keywords)
                
                units = ['eV', 'nm', 'Hz', 'J']
                has_units = any(unit in question for unit in units) or any(
                    any(unit in str(opt) for unit in units) for opt in options
                )
                
                # More specific forbidden patterns (avoid false positives)
                forbidden = ['electron configuration', 'which theory', 'primary difference', 'main difference']
                is_conceptual = any(pattern in question.lower() for pattern in forbidden)

                # Special case: "what is the energy" or "what is the wavelength" are numerical
                if 'what is the energy' in question.lower() or 'what is the wavelength' in question.lower():
                    is_conceptual = False
                
                print(f"\n🔍 ANALYSIS:")
                print(f"   Has numbers: {'✅' if has_numbers else '❌'}")
                print(f"   Has calc keywords: {'✅' if has_calc_keywords else '❌'}")
                print(f"   Has units: {'✅' if has_units else '❌'}")
                print(f"   Is conceptual: {'❌' if not is_conceptual else '✅'}")
                
                if (has_numbers or has_calc_keywords or has_units) and not is_conceptual:
                    print("🎉 SUCCESS: This is a numerical calculation question!")
                    return True
                else:
                    print("❌ FAILED: Still not numerical despite override")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                return False
        else:
            print(f"❌ Ollama request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_multiple_attempts():
    """Test multiple attempts to get numerical questions"""
    print("🔄 Testing multiple attempts to generate numerical questions...")
    
    successes = 0
    attempts = 5
    
    for i in range(attempts):
        print(f"\n--- ATTEMPT {i+1}/{attempts} ---")
        if test_direct_numerical_generation():
            successes += 1
    
    print(f"\n📊 RESULTS: {successes}/{attempts} successful numerical questions")
    
    if successes >= 1:
        print("🎉 SUCCESS: Can generate numerical questions!")
        return True
    else:
        print("💥 FAILED: Cannot generate numerical questions")
        return False

if __name__ == "__main__":
    success = test_multiple_attempts()
    
    if success:
        print("\n✅ Direct Ollama numerical generation works!")
        print("💡 The issue is in the app's generation pipeline, not Ollama itself")
    else:
        print("\n❌ Even direct Ollama generation fails")
        print("💡 Need to fix the prompt or try a different model")
