#!/usr/bin/env python3
"""
Test script to verify Phi model is generating instruction prompts instead of definitions
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_phi_instruction_generation():
    """Test if Phi model generates instruction prompts for MCQ generation"""
    print("🧠 PHI INSTRUCTION PROMPT GENERATION TEST")
    print("=" * 60)
    
    test_cases = [
        {
            "input": "CBT",
            "expected_type": "instruction prompt for therapy questions",
            "should_contain": ["generate", "questions", "techniques", "approaches", "methods"]
        },
        {
            "input": "AI", 
            "expected_type": "instruction prompt for AI questions",
            "should_contain": ["create", "questions", "algorithms", "applications", "concepts"]
        },
        {
            "input": "calculus",
            "expected_type": "instruction prompt for math questions", 
            "should_contain": ["generate", "problems", "derivatives", "integrals", "calculations"]
        },
        {
            "input": "DNA",
            "expected_type": "instruction prompt for biology questions",
            "should_contain": ["create", "questions", "structure", "function", "processes"]
        }
    ]
    
    try:
        from knowledge_app.core.intelligent_semantic_mapper import get_semantic_mapper
        
        mapper = get_semantic_mapper()
        print(f"✅ Semantic mapper initialized")
        print()
        
        results = []
        
        for i, case in enumerate(test_cases):
            print(f"🧪 Test {i+1}: '{case['input']}'")
            print(f"Expected: {case['expected_type']}")
            
            result = mapper.map_topic_semantically(case['input'])
            
            print(f"✅ Result:")
            print(f"   Expanded: '{result.expanded_topic}'")
            print(f"   Type: {result.question_type}")
            print(f"   Confidence: {result.confidence:.2f}")
            print(f"   Reasoning: {result.reasoning}")
            print()
            
            # Analyze if the reasoning is an instruction prompt
            reasoning_lower = result.reasoning.lower()
            
            # Check for instruction keywords
            instruction_keywords = ["generate", "create", "questions", "problems", "design", "develop"]
            has_instruction_words = any(keyword in reasoning_lower for keyword in instruction_keywords)
            
            # Check for definition keywords (bad)
            definition_keywords = ["is a", "refers to", "means", "definition", "stands for"]
            has_definition_words = any(keyword in reasoning_lower for keyword in definition_keywords)
            
            # Check for expected content
            has_expected_content = any(word in reasoning_lower for word in case['should_contain'])
            
            # Score the result
            instruction_score = 0
            if has_instruction_words:
                instruction_score += 2
                print(f"   ✅ Contains instruction words")
            else:
                print(f"   ❌ Missing instruction words")
            
            if not has_definition_words:
                instruction_score += 2
                print(f"   ✅ Avoids definition language")
            else:
                print(f"   ⚠️ Contains definition language")
            
            if has_expected_content:
                instruction_score += 1
                print(f"   ✅ Contains expected domain content")
            else:
                print(f"   ⚠️ Missing expected domain content")
            
            print(f"   🎯 Instruction Score: {instruction_score}/5")
            
            if instruction_score >= 4:
                print(f"   ✅ EXCELLENT: This is a proper instruction prompt!")
            elif instruction_score >= 3:
                print(f"   ✅ GOOD: This is mostly an instruction prompt")
            elif instruction_score >= 2:
                print(f"   ⚠️ MIXED: Contains both instruction and definition elements")
            else:
                print(f"   ❌ POOR: This is more definition than instruction")
            
            results.append({
                "input": case['input'],
                "score": instruction_score,
                "reasoning": result.reasoning
            })
            
            print("-" * 60)
        
        # Summary analysis
        print("\n📊 SUMMARY ANALYSIS")
        print("=" * 60)
        
        total_score = sum(r['score'] for r in results)
        max_score = len(results) * 5
        percentage = (total_score / max_score) * 100
        
        print(f"Overall Score: {total_score}/{max_score} ({percentage:.1f}%)")
        
        excellent = len([r for r in results if r['score'] >= 4])
        good = len([r for r in results if r['score'] == 3])
        mixed = len([r for r in results if r['score'] == 2])
        poor = len([r for r in results if r['score'] < 2])
        
        print(f"Excellent instruction prompts: {excellent}/{len(results)}")
        print(f"Good instruction prompts: {good}/{len(results)}")
        print(f"Mixed prompts: {mixed}/{len(results)}")
        print(f"Poor/definition prompts: {poor}/{len(results)}")
        
        if percentage >= 80:
            print("\n🎉 EXCELLENT: Phi model is generating proper instruction prompts!")
            return True
        elif percentage >= 60:
            print("\n✅ GOOD: Phi model is mostly generating instruction prompts")
            return True
        elif percentage >= 40:
            print("\n⚠️ MIXED: Phi model needs improvement in instruction generation")
            return False
        else:
            print("\n❌ POOR: Phi model is still generating definitions instead of instructions")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_instruction_flow():
    """Test if instructions flow through to MCQ generation"""
    print("\n🚀 END-TO-END INSTRUCTION FLOW TEST")
    print("=" * 60)
    
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        mcq_manager = get_mcq_manager()
        print("✅ MCQ Manager obtained")
        
        # Test with a topic that should generate clear instructions
        test_topic = "CBT"
        
        quiz_params = {
            "topic": test_topic,
            "difficulty": "medium",
            "game_mode": "casual", 
            "submode": "mixed",
            "num_questions": 1,
            "mode": "auto"
        }
        
        print(f"🧪 Testing instruction flow for: '{test_topic}'")
        print("This should:")
        print("1. Use Phi to generate instruction prompts")
        print("2. Pass instructions to DeepSeek/Ollama models")
        print("3. Generate questions based on instructions")
        print()
        
        # Generate quiz (this will test the full pipeline)
        result = mcq_manager.generate_quiz(quiz_params)
        
        if result and hasattr(result, 'question'):
            print("✅ Quiz generation completed with instruction flow")
            print(f"   Question: {result.question[:150]}...")
            return True
        else:
            print("❌ Quiz generation failed")
            return False
        
    except Exception as e:
        print(f"❌ End-to-end test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run instruction prompt tests"""
    print("🎯 PHI INSTRUCTION PROMPT TEST SUITE")
    print("=" * 70)
    
    tests = [
        ("Phi Instruction Generation", test_phi_instruction_generation),
        ("End-to-End Instruction Flow", test_end_to_end_instruction_flow)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🚀 Starting: {test_name}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 70)
    print("📊 FINAL RESULTS:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 Phi model is generating proper instruction prompts!")
    else:
        print("⚠️ Phi model needs improvement in instruction generation.")

if __name__ == "__main__":
    main()
