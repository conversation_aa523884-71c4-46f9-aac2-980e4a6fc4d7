#!/usr/bin/env python3
"""
Test expert mode integration in the quiz flow
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_expert_mode_mcq_manager():
    """Test expert mode through MCQ manager"""
    print("🧠 Testing Expert Mode through MCQ Manager...")
    
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        # Create MCQ manager
        mcq_manager = get_mcq_manager()
        
        # Test expert mode quiz parameters
        quiz_params = {
            "topic": "atoms",
            "difficulty": "expert",  # This should trigger DeepSeek
            "game_mode": "serious",
            "submode": "mixed",
            "num_questions": 1,
            "mode": "auto"
        }
        
        print(f"🧪 Testing with params: {quiz_params}")
        
        # Generate quiz
        result = mcq_manager.generate_quiz(quiz_params)
        
        if result:
            print("✅ Expert mode MCQ generation successful!")
            print(f"   ❓ Question: {result.question[:100]}...")
            print(f"   🔢 Options: {len(result.options)}")
            print(f"   ✅ Correct: {result.correct_answer}")
            print(f"   🧠 Method: {getattr(result, 'generation_method', 'unknown')}")
            
            # Check if it used DeepSeek
            if hasattr(result, 'generation_method') and result.generation_method == 'deepseek_pipeline':
                print("🎉 SUCCESS: Used DeepSeek pipeline for expert mode!")
                return True
            else:
                print(f"⚠️ WARNING: Used {getattr(result, 'generation_method', 'unknown')} instead of DeepSeek")
                return False
        else:
            print("❌ Expert mode MCQ generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_regular_mode_mcq_manager():
    """Test regular mode through MCQ manager"""
    print("\n📚 Testing Regular Mode through MCQ Manager...")
    
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        # Create MCQ manager
        mcq_manager = get_mcq_manager()
        
        # Test regular mode quiz parameters
        quiz_params = {
            "topic": "atoms",
            "difficulty": "medium",  # This should NOT trigger DeepSeek
            "game_mode": "casual",
            "submode": "mixed",
            "num_questions": 1,
            "mode": "auto"
        }
        
        print(f"🧪 Testing with params: {quiz_params}")
        
        # Generate quiz
        result = mcq_manager.generate_quiz(quiz_params)
        
        if result:
            print("✅ Regular mode MCQ generation successful!")
            print(f"   ❓ Question: {result.question[:100]}...")
            print(f"   🔢 Options: {len(result.options)}")
            print(f"   ✅ Correct: {result.correct_answer}")
            print(f"   🧠 Method: {getattr(result, 'generation_method', 'unknown')}")
            
            # Check if it did NOT use DeepSeek
            if hasattr(result, 'generation_method') and result.generation_method != 'deepseek_pipeline':
                print("✅ SUCCESS: Used regular generation for medium mode!")
                return True
            else:
                print(f"⚠️ WARNING: Unexpectedly used DeepSeek for medium mode")
                return False
        else:
            print("❌ Regular mode MCQ generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_webengine_expert_flow():
    """Test expert mode through WebEngine flow"""
    print("\n🌐 Testing Expert Mode through WebEngine Flow...")
    
    try:
        from knowledge_app.webengine_app import PythonBridge
        
        # Create bridge
        bridge = PythonBridge()
        
        # Test quiz parameters (simulating what the UI sends)
        quiz_params = {
            "topic": "atoms",
            "difficulty": "expert",
            "mode": "auto",
            "game_mode": "serious",
            "submode": "mixed",
            "num_questions": 2,
            "timer": "30s"
        }
        
        print(f"🧪 Starting quiz with params: {quiz_params}")
        
        # This is what the UI calls
        bridge.startQuiz(json.dumps(quiz_params))
        
        print("✅ Quiz start initiated successfully (non-blocking)")
        print("   📝 Note: Actual generation happens in background")
        return True
        
    except Exception as e:
        print(f"❌ WebEngine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🧠 Expert Mode Integration Test")
    print("=" * 60)
    
    # Test 1: Expert mode through MCQ manager
    expert_mcq_works = test_expert_mode_mcq_manager()
    
    # Test 2: Regular mode through MCQ manager
    regular_mcq_works = test_regular_mode_mcq_manager()
    
    # Test 3: WebEngine flow
    webengine_works = test_webengine_expert_flow()
    
    print(f"\n📊 INTEGRATION TEST RESULTS:")
    print(f"   Expert MCQ Manager: {'✅' if expert_mcq_works else '❌'}")
    print(f"   Regular MCQ Manager: {'✅' if regular_mcq_works else '❌'}")
    print(f"   WebEngine Flow: {'✅' if webengine_works else '❌'}")
    
    if expert_mcq_works and regular_mcq_works and webengine_works:
        print(f"\n🎉 ALL INTEGRATION TESTS PASSED!")
        print(f"   ✅ Expert mode automatically uses DeepSeek")
        print(f"   ✅ Regular mode uses standard generation")
        print(f"   ✅ WebEngine flow works without blocking")
        return 0
    else:
        print(f"\n❌ Some integration tests failed")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
