#!/usr/bin/env python3
"""
Test the unified manager with intelligent generation
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_unified_intelligent():
    """Test unified manager with intelligent generation"""
    print("🧠 Testing Unified Manager with Intelligent Generation")
    print("=" * 60)
    
    try:
        from knowledge_app.core.unified_inference_manager import get_unified_inference_manager
        
        # Get the manager
        manager = get_unified_inference_manager()
        
        # Initialize
        print("🔧 Initializing unified manager...")
        success = await manager.initialize_async()
        
        if not success:
            print("❌ Failed to initialize unified manager")
            return False
        
        print("✅ Unified manager initialized")
        
        # Test with problematic inputs
        test_cases = [
            ("magnetism", "medium"),
            ("dfs", "medium"),
            ("sdfsf", "easy"),
            ("physics", "medium"),
            ("random text", "easy")
        ]
        
        for topic, difficulty in test_cases:
            print(f"\n🔍 Testing: '{topic}' (difficulty: {difficulty})")
            
            try:
                result = await manager.generate_mcq_async(
                    topic=topic,
                    difficulty=difficulty,
                    question_type="mixed",
                    timeout=30.0
                )
                
                if result:
                    print(f"✅ SUCCESS for '{topic}'!")
                    print(f"   📝 Original: {result.get('original_input', topic)}")
                    print(f"   🎯 Resolved: {result.get('resolved_topic', 'N/A')}")
                    print(f"   ❓ Question: {result.get('question', 'N/A')[:80]}...")
                    print(f"   🔢 Options: {len(result.get('options', []))} choices")
                else:
                    print(f"❌ FAILED for '{topic}' - No result returned")
                    
            except Exception as e:
                print(f"❌ EXCEPTION for '{topic}': {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🚀 Unified Intelligent Generation Test")
    print("=" * 60)
    
    success = await test_unified_intelligent()
    
    if success:
        print("\n🎉 SUCCESS! Unified manager with intelligent generation works!")
        print("💡 Your app should now handle ANY input intelligently!")
    else:
        print("\n❌ Test failed. Check the implementation.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
