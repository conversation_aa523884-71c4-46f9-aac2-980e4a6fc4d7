#!/usr/bin/env python3
"""
100-Test Suite with Enhanced PhD-Level Pipeline
Tests the enhanced DeepSeek two-model pipeline across 100 diverse topics
"""

import asyncio
import json
import time
import random
from datetime import datetime
from pathlib import Path

# 100 diverse topics across all difficulty levels
TEST_TOPICS_100 = [
    # EXPERT LEVEL (25 topics) - PhD-level content
    ("quantum field theory", "expert"), ("machine learning theory", "expert"), 
    ("organic synthesis mechanisms", "expert"), ("protein folding dynamics", "expert"),
    ("distributed consensus protocols", "expert"), ("differential geometry", "expert"),
    ("computational complexity theory", "expert"), ("immunology pathways", "expert"),
    ("cryptographic protocols", "expert"), ("neural network optimization", "expert"),
    ("biochemical pathways", "expert"), ("molecular dynamics", "expert"),
    ("algebraic topology", "expert"), ("pharmacokinetics", "expert"),
    ("string theory", "expert"), ("gene regulation", "expert"),
    ("compiler optimization", "expert"), ("statistical mechanics", "expert"),
    ("signal processing", "expert"), ("metabolic networks", "expert"),
    ("game theory", "expert"), ("abstract algebra", "expert"),
    ("quantum computing algorithms", "expert"), ("enzyme kinetics", "expert"),
    ("theoretical computer science", "expert"),
    
    # HARD LEVEL (25 topics) - Advanced undergraduate/graduate
    ("calculus optimization", "hard"), ("organic chemistry reactions", "hard"),
    ("data structures algorithms", "hard"), ("thermodynamics", "hard"),
    ("linear algebra", "hard"), ("cell biology", "hard"),
    ("electromagnetism", "hard"), ("database systems", "hard"),
    ("genetics", "hard"), ("fluid mechanics", "hard"),
    ("discrete mathematics", "hard"), ("microbiology", "hard"),
    ("computer networks", "hard"), ("physical chemistry", "hard"),
    ("operating systems", "hard"), ("ecology", "hard"),
    ("probability theory", "hard"), ("molecular biology", "hard"),
    ("software engineering", "hard"), ("inorganic chemistry", "hard"),
    ("artificial intelligence", "hard"), ("neuroscience", "hard"),
    ("cybersecurity", "hard"), ("bioinformatics", "hard"),
    ("machine learning", "hard"),
    
    # MEDIUM LEVEL (25 topics) - Intermediate level
    ("algebra", "medium"), ("chemistry", "medium"), ("physics", "medium"),
    ("biology", "medium"), ("computer science", "medium"), ("mathematics", "medium"),
    ("psychology", "medium"), ("sociology", "medium"), ("economics", "medium"),
    ("history", "medium"), ("geography", "medium"), ("philosophy", "medium"),
    ("literature", "medium"), ("political science", "medium"), ("anthropology", "medium"),
    ("linguistics", "medium"), ("statistics", "medium"), ("geology", "medium"),
    ("astronomy", "medium"), ("environmental science", "medium"),
    ("business", "medium"), ("engineering", "medium"), ("medicine", "medium"),
    ("law", "medium"), ("education", "medium"),
    
    # EASY LEVEL (25 topics) - Basic level
    ("basic math", "easy"), ("basic science", "easy"), ("general knowledge", "easy"),
    ("world history", "easy"), ("geography basics", "easy"), ("literature basics", "easy"),
    ("science basics", "easy"), ("math basics", "easy"), ("health", "easy"),
    ("technology basics", "easy"), ("art", "easy"), ("music", "easy"),
    ("sports", "easy"), ("culture", "easy"), ("language", "easy"),
    ("current events", "easy"), ("social studies", "easy"), ("nature", "easy"),
    ("food", "easy"), ("travel", "easy"), ("entertainment", "easy"),
    ("lifestyle", "easy"), ("communication", "easy"), ("ethics", "easy"),
    ("critical thinking", "easy")
]

async def test_single_topic(topic, difficulty, test_num, total_tests):
    """Test MCQ generation for a single topic"""
    print(f"\n🧪 Test {test_num:3d}/{total_tests}: [{difficulty.upper()}] {topic}")
    
    try:
        from src.knowledge_app.core.unified_inference_manager import generate_mcq_unified
        
        start_time = time.time()
        
        # Generate MCQ using unified inference (will use enhanced DeepSeek for expert)
        result = generate_mcq_unified(
            topic=topic,
            difficulty=difficulty,
            question_type="mixed",
            timeout=120.0 if difficulty == "expert" else 60.0
        )
        
        duration = time.time() - start_time
        
        if not result:
            print(f"   ❌ GENERATION FAILED ({duration:.1f}s)")
            return False, f"Failed generation"
        
        question = result.get('question', '')
        options = result.get('options', [])
        correct = result.get('correct_answer', '') or result.get('correct', '')
        explanation = result.get('explanation', '')
        
        # Basic validation
        validation_checks = {
            'has_question': bool(question.strip()),
            'has_question_mark': question.endswith('?'),
            'has_4_options': len(options) == 4,
            'has_correct': correct in ['A', 'B', 'C', 'D'],
            'sufficient_length': len(question) >= (100 if difficulty == "expert" else 60),
            'substantial_options': all(len(str(opt).strip()) >= 10 for opt in options)
        }
        
        passed_checks = sum(validation_checks.values())
        total_checks = len(validation_checks)
        success = passed_checks >= (total_checks * 0.8)  # 80% pass threshold
        
        # Show brief results
        if success:
            print(f"   ✅ PASS - {passed_checks}/{total_checks} checks ({duration:.1f}s)")
            if difficulty == "expert":
                print(f"      📝 PhD-level: {question[:60]}...")
        else:
            print(f"   ❌ FAIL - {passed_checks}/{total_checks} checks ({duration:.1f}s)")
            failed = [k for k, v in validation_checks.items() if not v]
            print(f"      ❌ Issues: {', '.join(failed[:2])}")
        
        return success, f"{passed_checks}/{total_checks} checks, {duration:.1f}s"
        
    except Exception as e:
        print(f"   ❌ ERROR - {e}")
        return False, f"Exception: {e}"

async def run_100_test_suite():
    """Run the complete 100-test suite"""
    print("🚀 100-TEST SUITE WITH ENHANCED PhD-LEVEL PIPELINE")
    print("=" * 80)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialize system
    print("\n🔧 Initializing enhanced system...")
    try:
        from src.knowledge_app.core.unified_inference_manager import initialize_unified_inference
        
        success = initialize_unified_inference({
            'timeout': 120.0,
            'mode': 'auto',
            'prefer_local': True
        })
        
        if not success:
            print("❌ System initialization failed")
            return False
        
        print("✅ Enhanced system ready for 100-test suite")
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return False
    
    # Run all 100 tests
    print(f"\n🎯 Running 100 tests across all difficulty levels...")
    print(f"   📊 Expert (PhD-level): 25 tests")
    print(f"   📊 Hard: 25 tests") 
    print(f"   📊 Medium: 25 tests")
    print(f"   📊 Easy: 25 tests")
    
    start_time = time.time()
    results_by_difficulty = {"expert": [], "hard": [], "medium": [], "easy": []}
    total_passed = 0
    
    for i, (topic, difficulty) in enumerate(TEST_TOPICS_100, 1):
        success, message = await test_single_topic(topic, difficulty, i, len(TEST_TOPICS_100))
        results_by_difficulty[difficulty].append(success)
        if success:
            total_passed += 1
    
    duration = time.time() - start_time
    overall_pass_rate = (total_passed / len(TEST_TOPICS_100)) * 100
    
    # Calculate pass rates by difficulty
    difficulty_stats = {}
    for difficulty, results in results_by_difficulty.items():
        passed = sum(results)
        total = len(results)
        pass_rate = (passed / total) * 100 if total > 0 else 0
        difficulty_stats[difficulty] = {
            'passed': passed,
            'total': total,
            'pass_rate': pass_rate
        }
    
    # Results summary
    print("\n" + "=" * 80)
    print("🏆 100-TEST SUITE RESULTS")
    print("=" * 80)
    
    print(f"📊 Overall Performance:")
    print(f"   Total Tests: {len(TEST_TOPICS_100)}")
    print(f"   Tests Passed: {total_passed}")
    print(f"   Overall Pass Rate: {overall_pass_rate:.1f}%")
    print(f"   Duration: {duration/60:.1f} minutes")
    print(f"   Average per test: {duration/len(TEST_TOPICS_100):.1f} seconds")
    
    print(f"\n📈 Results by Difficulty:")
    for difficulty in ["expert", "hard", "medium", "easy"]:
        stats = difficulty_stats[difficulty]
        print(f"   {difficulty.upper():<8}: {stats['passed']:2d}/{stats['total']:2d} ({stats['pass_rate']:5.1f}%)")
    
    # Success assessment
    expert_pass_rate = difficulty_stats["expert"]["pass_rate"]
    
    print(f"\n🎯 SUCCESS ASSESSMENT:")
    if overall_pass_rate >= 85:
        print(f"   🎉 EXCELLENT: {overall_pass_rate:.1f}% overall success!")
        if expert_pass_rate >= 70:
            print(f"   🧠 PhD-LEVEL SUCCESS: {expert_pass_rate:.1f}% expert pass rate!")
            print("   🚀 PRODUCTION READY: System suitable for paying customers!")
            return True
        else:
            print(f"   ⚠️ Expert mode needs work: {expert_pass_rate:.1f}% pass rate")
    elif overall_pass_rate >= 75:
        print(f"   👏 GOOD: {overall_pass_rate:.1f}% overall success!")
        print("   🔧 Minor improvements needed for production")
        return True
    elif overall_pass_rate >= 65:
        print(f"   📊 ACCEPTABLE: {overall_pass_rate:.1f}% overall success")
        print("   ⚠️ Needs optimization before production launch")
        return False
    else:
        print(f"   ❌ UNACCEPTABLE: {overall_pass_rate:.1f}% overall success")
        print("   🔧 Major improvements required")
        return False

async def main():
    """Main test runner"""
    try:
        success = await run_100_test_suite()
        return 0 if success else 1
    except Exception as e:
        print(f"❌ 100-test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
