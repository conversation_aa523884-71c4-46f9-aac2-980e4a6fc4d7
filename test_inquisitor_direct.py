#!/usr/bin/env python3
"""
Test the Inquisitor's Mandate prompt directly
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_inquisitor_direct():
    """Test the Inquisitor's Mandate prompt directly with Ollama"""
    print("🎯 Testing Inquisitor's Mandate directly...")
    
    try:
        from knowledge_app.core.intelligent_prompt_generator import get_intelligent_prompt_generator
        import requests
        
        # Get the intelligent prompt (now using Inquisitor's Mandate)
        prompt_generator = get_intelligent_prompt_generator()
        result = prompt_generator.generate_intelligent_prompt(
            raw_input="atoms",
            difficulty="medium",
            question_type="mixed"
        )
        
        prompt = result.get('prompt', '')
        print(f"📝 Inquisitor's Mandate prompt:")
        print("-" * 50)
        print(prompt)
        print("-" * 50)
        
        # Test with <PERSON>lla<PERSON> directly
        payload = {
            "model": "mathstral:latest",
            "prompt": prompt,
            "format": "json",
            "stream": False,
            "options": {
                "temperature": 0.4,
                "num_predict": 1800
            }
        }
        
        print(f"🚀 Sending Inquisitor's Mandate to Ollama...")
        response = requests.post("http://localhost:11434/api/generate", json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            raw_response = result.get('response', '')
            
            print(f"📄 Raw response from Inquisitor's Mandate:")
            print("-" * 50)
            print(raw_response)
            print("-" * 50)
            
            # Try to parse it
            try:
                parsed = json.loads(raw_response)
                print(f"✅ JSON parsing successful!")
                print(f"📋 Parsed data: {parsed}")
                
                # Test our validation
                from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator
                generator = OllamaJSONGenerator()
                
                is_valid = generator._is_valid_question(parsed)
                print(f"🔍 Our validation result: {'✅' if is_valid else '❌'}")
                
                if is_valid:
                    normalized = generator._normalize_question(parsed)
                    print(f"🔧 Normalized: {normalized}")
                    print(f"🎉 INQUISITOR'S MANDATE IS WORKING PERFECTLY!")
                    return True
                else:
                    print(f"❌ Validation failed - checking why...")
                    print(f"   Has question: {'question' in parsed}")
                    print(f"   Has options: {'options' in parsed}")
                    print(f"   Has correct field: {'correct_answer' in parsed or 'correct' in parsed}")
                    if 'options' in parsed:
                        print(f"   Options count: {len(parsed.get('options', []))}")
                        print(f"   Options type: {type(parsed.get('options', []))}")
                        print(f"   Options: {parsed.get('options', [])}")
                    if 'question' in parsed:
                        print(f"   Question ends with ?: {parsed['question'].strip().endswith('?')}")
                    if 'correct' in parsed:
                        print(f"   Correct value: '{parsed['correct']}'")
                    return False
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                return False
        else:
            print(f"❌ Ollama request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🎯 Inquisitor's Mandate Direct Test")
    print("=" * 60)
    
    success = test_inquisitor_direct()
    
    if success:
        print(f"\n🎉 SUCCESS! Inquisitor's Mandate is working perfectly!")
        print(f"💡 The issue must be elsewhere in the pipeline")
        return 0
    else:
        print(f"\n❌ Inquisitor's Mandate has issues")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
