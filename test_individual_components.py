#!/usr/bin/env python3
"""
Test individual components to find what's hanging
"""

import sys
import os
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_individual_components():
    """Test each component individually to find the hanging one"""
    print("🔍 TESTING INDIVIDUAL COMPONENTS")
    print("=" * 50)
    
    # Test 1: GlobalModelSingleton
    print("\n1️⃣ Testing GlobalModelSingleton...")
    try:
        start_time = time.time()
        from knowledge_app.core.global_model_singleton import get_global_model
        
        global_model = get_global_model()
        elapsed = time.time() - start_time
        
        print(f"   ✅ GlobalModelSingleton created in {elapsed:.2f}s")
        print(f"   🚀 Is loaded: {global_model.is_loaded}")
        
        if elapsed > 10:
            print(f"   ⚠️ WARNING: Took {elapsed:.2f}s - this might be causing the hang")
            
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"   ❌ GlobalModelSingleton failed in {elapsed:.2f}s: {e}")
    
    # Test 2: EnhancedLMStudioGenerator
    print("\n2️⃣ Testing EnhancedLMStudioGenerator...")
    try:
        start_time = time.time()
        from knowledge_app.core.enhanced_lmstudio_generator import EnhancedLMStudioGenerator
        
        config = {}
        lmstudio_gen = EnhancedLMStudioGenerator(config)
        elapsed_create = time.time() - start_time
        
        print(f"   ✅ EnhancedLMStudioGenerator created in {elapsed_create:.2f}s")
        
        # Test initialization
        init_start = time.time()
        success = lmstudio_gen.initialize()
        elapsed_init = time.time() - init_start
        
        print(f"   🚀 Initialized: {success} in {elapsed_init:.2f}s")
        
        if elapsed_init > 10:
            print(f"   ⚠️ WARNING: Initialization took {elapsed_init:.2f}s - this might be causing the hang")
            
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"   ❌ EnhancedLMStudioGenerator failed in {elapsed:.2f}s: {e}")
    
    # Test 3: OfflineMCQGenerator (we know this works)
    print("\n3️⃣ Testing OfflineMCQGenerator...")
    try:
        start_time = time.time()
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator
        
        config = {}
        offline_gen = OfflineMCQGenerator(config)
        elapsed_create = time.time() - start_time
        
        print(f"   ✅ OfflineMCQGenerator created in {elapsed_create:.2f}s")
        
        # Test initialization
        init_start = time.time()
        success = offline_gen.initialize()
        elapsed_init = time.time() - init_start
        
        print(f"   🚀 Initialized: {success} in {elapsed_init:.2f}s")
        
        if elapsed_init > 10:
            print(f"   ⚠️ WARNING: Initialization took {elapsed_init:.2f}s")
            
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"   ❌ OfflineMCQGenerator failed in {elapsed:.2f}s: {e}")
    
    # Test 4: Cloud initialization
    print("\n4️⃣ Testing Cloud API initialization...")
    try:
        start_time = time.time()
        
        # Simulate what the UnifiedInferenceManager does
        from knowledge_app.core.online_mcq_generator import OnlineMCQGenerator
        
        config = {}
        online_gen = OnlineMCQGenerator(config)
        elapsed = time.time() - start_time
        
        print(f"   ✅ OnlineMCQGenerator created in {elapsed:.2f}s")
        
        if elapsed > 10:
            print(f"   ⚠️ WARNING: Took {elapsed:.2f}s - this might be causing the hang")
            
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"   ❌ OnlineMCQGenerator failed in {elapsed:.2f}s: {e}")
    
    print("\n" + "=" * 50)
    print("🔍 COMPONENT TEST COMPLETE")
    print("\nLook for components that take >10 seconds - those are likely causing the hang")

if __name__ == "__main__":
    test_individual_components()
