#!/usr/bin/env python3
"""
Test script to verify that expert mode generates diverse questions
and doesn't repeat the same question multiple times.

This test specifically addresses the issue where numerical expert questions
were showing the same hardcoded question repeatedly.
"""

import asyncio
import sys
import os
import time
import json
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_expert_question_diversity():
    """Test that expert mode generates different questions each time"""
    print("🧪 Testing Expert Mode Question Diversity")
    print("=" * 50)
    
    try:
        # Import the MCQ manager
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        print("1️⃣ Initializing MCQ Manager...")
        mcq_manager = get_mcq_manager()
        
        # Test parameters for numerical expert questions
        quiz_params = {
            "topic": "numerical",
            "difficulty": "expert", 
            "game_mode": "serious",
            "submode": "mixed",
            "num_questions": 1
        }
        
        print(f"2️⃣ Testing with parameters: {quiz_params}")
        print("\n🔄 Generating 5 expert questions to check for diversity...")
        
        generated_questions = []
        question_texts = []
        
        # Generate 5 questions and check for uniqueness
        for i in range(5):
            print(f"\n   Generating question {i+1}/5...")
            
            try:
                # Generate question
                result = await mcq_manager.generate_quiz_async(quiz_params)
                
                if result and hasattr(result, 'question'):
                    question_text = result.question.strip()
                    generated_questions.append({
                        'index': i+1,
                        'question': question_text,
                        'options': getattr(result, 'options', []),
                        'correct_answer': getattr(result, 'correct_answer', ''),
                        'explanation': getattr(result, 'explanation', ''),
                        'generation_method': getattr(result, 'generation_method', 'unknown')
                    })
                    question_texts.append(question_text.lower())
                    
                    print(f"   ✅ Question {i+1}: {question_text[:80]}...")
                    print(f"   🔧 Method: {getattr(result, 'generation_method', 'unknown')}")
                    
                else:
                    print(f"   ❌ Question {i+1}: Generation failed")
                    
            except Exception as e:
                print(f"   ❌ Question {i+1}: Error - {e}")
            
            # Small delay between generations
            await asyncio.sleep(1)
        
        print("\n3️⃣ Analyzing Results...")
        print("=" * 30)
        
        if not generated_questions:
            print("❌ No questions were generated successfully")
            return False
        
        # Check for duplicates
        unique_questions = set(question_texts)
        total_questions = len(generated_questions)
        unique_count = len(unique_questions)
        
        print(f"📊 Total questions generated: {total_questions}")
        print(f"📊 Unique questions: {unique_count}")
        print(f"📊 Duplicate questions: {total_questions - unique_count}")
        
        # Success criteria
        if unique_count == total_questions:
            print("✅ SUCCESS: All questions are unique!")
            success = True
        elif unique_count >= total_questions * 0.8:  # At least 80% unique
            print("⚠️ PARTIAL SUCCESS: Most questions are unique")
            success = True
        else:
            print("❌ FAILURE: Too many duplicate questions")
            success = False
        
        # Show detailed analysis
        print("\n4️⃣ Detailed Question Analysis:")
        print("-" * 40)
        
        for i, q in enumerate(generated_questions):
            print(f"\nQuestion {q['index']}:")
            print(f"  Text: {q['question'][:100]}...")
            print(f"  Method: {q['generation_method']}")
            print(f"  Options: {len(q['options'])} choices")
            
            # Check if this question appears elsewhere
            duplicates = [j for j, other_q in enumerate(generated_questions) 
                         if j != i and other_q['question'].lower() == q['question'].lower()]
            if duplicates:
                print(f"  ⚠️ DUPLICATE: Same as question(s) {[d+1 for d in duplicates]}")
            else:
                print(f"  ✅ UNIQUE")
        
        # Save results for debugging
        results_file = Path("test_results_expert_diversity.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'test_timestamp': time.time(),
                'quiz_params': quiz_params,
                'total_questions': total_questions,
                'unique_questions': unique_count,
                'success': success,
                'questions': generated_questions
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_cache_behavior():
    """Test that expert questions are not cached"""
    print("\n🧪 Testing Expert Mode Cache Behavior")
    print("=" * 50)
    
    try:
        from knowledge_app.core.inference import CloudInference
        from knowledge_app.core.app_config import AppConfig
        
        # Create inference instance
        config = AppConfig()
        inference = CloudInference(config)
        
        print("1️⃣ Testing cache behavior for expert vs non-expert questions...")
        
        # Test expert question (should not be cached)
        print("\n   Testing expert difficulty (should not cache)...")
        expert_q1 = inference.generate_quiz_question("numerical", "expert")
        expert_q2 = inference.generate_quiz_question("numerical", "expert")
        
        # Test medium question (should be cached)
        print("   Testing medium difficulty (should cache)...")
        medium_q1 = inference.generate_quiz_question("numerical", "medium")
        medium_q2 = inference.generate_quiz_question("numerical", "medium")
        
        # Analyze results
        expert_same = (expert_q1 and expert_q2 and 
                      expert_q1.get('question', '') == expert_q2.get('question', ''))
        medium_same = (medium_q1 and medium_q2 and 
                      medium_q1.get('question', '') == medium_q2.get('question', ''))
        
        print(f"\n2️⃣ Cache Analysis:")
        print(f"   Expert questions identical: {expert_same}")
        print(f"   Medium questions identical: {medium_same}")
        
        if not expert_same and medium_same:
            print("✅ SUCCESS: Expert questions not cached, medium questions cached")
            return True
        elif not expert_same and not medium_same:
            print("⚠️ PARTIAL: Expert questions not cached, but medium also not cached")
            return True
        else:
            print("❌ FAILURE: Expert questions appear to be cached")
            return False
            
    except Exception as e:
        print(f"❌ Cache test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Expert Mode Question Diversity Test Suite")
    print("=" * 60)
    
    # Test 1: Question diversity
    diversity_success = await test_expert_question_diversity()
    
    # Test 2: Cache behavior
    cache_success = await test_cache_behavior()
    
    # Overall results
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    print(f"   Question Diversity Test: {'✅ PASS' if diversity_success else '❌ FAIL'}")
    print(f"   Cache Behavior Test: {'✅ PASS' if cache_success else '❌ FAIL'}")
    
    overall_success = diversity_success and cache_success
    print(f"\n🎯 OVERALL: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
    
    if overall_success:
        print("\n🎉 Expert mode question repetition issue has been FIXED!")
        print("   - Questions are now generated fresh each time")
        print("   - No caching for expert difficulty")
        print("   - Proper deduplication system in place")
    else:
        print("\n⚠️ Some issues remain. Check the detailed output above.")
    
    return overall_success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
