#   T r a i n i n g   O r c h e s t r a t o r 
 
 
 
 " " " 
 
 =؀  T r a i n i n g   O r c h e s t r a t o r   -   E n t e r p r i s e - G r a d e   T r a i n i n g   M a n a g e m e n t 
 
 
 
 F i x e s   t h e   c r i t i c a l   t h r e a d i n g   m o d e l   v i o l a t i o n   i n   w e b e n g i n e _ a p p . p y   w h e r e   Q T h r e a d s 
 
 w e r e   b e i n g   c r e a t e d   f r o m   b a c k g r o u n d   t h r e a d s .   T h i s   o r c h e s t r a t o r   e n s u r e s   a l l   Q t 
 
 o b j e c t s   a r e   c r e a t e d   a n d   m a n a g e d   f r o m   t h e   m a i n   U I   t h r e a d . 
 
 
 
 K e y   a r c h i t e c t u r a l   f i x e s : 
 
 1 .   P r o p e r   Q t   t h r e a d i n g   m o d e l   c o m p l i a n c e 
 
 2 .   S t r u c t u r e d   p r o g r e s s   r e p o r t i n g 
 
 3 .   U s e r   c a n c e l l a t i o n   c o n t r o l     
 
 4 .   S i n g l e   s o u r c e   o f   t r u t h   f o r   t r a i n i n g   s t a t e 
 
 5 .   C o m p r e h e n s i v e   e r r o r   h a n d l i n g 
 
 " " " 
 
 
 
 i m p o r t   l o g g i n g 
 
 i m p o r t   t h r e a d i n g 
 
 i m p o r t   j s o n 
 
 i m p o r t   t i m e 
 
 f r o m   p a t h l i b   i m p o r t   P a t h 
 
 f r o m   t y p i n g   i m p o r t   D i c t ,   A n y ,   O p t i o n a l 
 
 f r o m   P y Q t 5 . Q t C o r e   i m p o r t   Q O b j e c t ,   p y q t S i g n a l ,   p y q t S l o t ,   Q M e t a O b j e c t ,   Q t ,   Q _ A R G 
 
 
 
 l o g g e r   =   l o g g i n g . g e t L o g g e r ( _ _ n a m e _ _ ) 
 
 
 
 
 
 c l a s s   W o r k e r S i g n a l s ( Q O b j e c t ) : 
 
         " " " T h r e a d - s a f e   s i g n a l s   f o r   b a c k g r o u n d   w o r k e r   c o m m u n i c a t i o n " " " 
 
         p r o c e s s i n g _ f i n i s h e d   =   p y q t S i g n a l ( s t r )     #   d a t a s e t _ p a t h 
 
         p r o c e s s i n g _ e r r o r   =   p y q t S i g n a l ( s t r )           #   e r r o r _ m e s s a g e 
 
         p r o c e s s i n g _ p r o g r e s s   =   p y q t S i g n a l ( s t r )     #   s t a t u s _ m e s s a g e 
 
 
 
 
 
 c l a s s   T r a i n i n g O r c h e s t r a t o r ( Q O b j e c t ) : 
 
         " " " 
 
         =؀  E n t e r p r i s e   T r a i n i n g   O r c h e s t r a t o r 
 
         
 
         M a n a g e s   t h e   c o m p l e t e   t r a i n i n g   l i f e c y c l e   w i t h   p r o p e r   Q t   t h r e a d i n g   m o d e l . 
 
         A l l   Q T h r e a d   o b j e c t s   a r e   c r e a t e d   a n d   m a n a g e d   f r o m   t h e   m a i n   U I   t h r e a d . 
 
         " " " 
 
         
 
         #   S t r u c t u r e d   p r o g r e s s   s i g n a l s   f o r   m o d e r n   U I 
 
         t r a i n i n g _ s t a r t e d   =   p y q t S i g n a l ( d i c t )           #   t r a i n i n g   i n f o 
 
         p r o g r e s s _ u p d a t e   =   p y q t S i g n a l ( d i c t )             #   s t r u c t u r e d   p r o g r e s s   d a t a     
 
         t r a i n i n g _ c o m p l e t e d   =   p y q t S i g n a l ( d i c t )       #   c o m p l e t i o n   i n f o 
 
         t r a i n i n g _ c a n c e l l e d   =   p y q t S i g n a l ( s t r )         #   c a n c e l l a t i o n   r e a s o n 
 
         e r r o r _ o c c u r r e d   =   p y q t S i g n a l ( s t r )                 #   e r r o r   m e s s a g e 
 
         
 
         d e f   _ _ i n i t _ _ ( s e l f ,   p a r e n t = N o n e ) : 
 
                 s u p e r ( ) . _ _ i n i t _ _ ( p a r e n t ) 
 
                 
 
                 #   C R I T I C A L   F I X :   E n s u r e   w e ' r e   i n i t i a l i z e d   o n   m a i n   U I   t h r e a d 
 
                 i f   t h r e a d i n g . c u r r e n t _ t h r e a d ( )   ! =   Q C o r e A p p l i c a t i o n . i n s t a n c e ( ) . t h r e a d ( ) : 
 
                         r a i s e   R u n t i m e E r r o r ( " L'  C R I T I C A L :   T r a i n i n g O r c h e s t r a t o r   m u s t   b e   c r e a t e d   o n   m a i n   U I   t h r e a d " ) 
 
                 
 
                 #   T r a i n i n g   s t a t e   m a n a g e m e n t 
 
                 s e l f . c u r r e n t _ t r a i n e r   =   N o n e 
 
                 s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                 s e l f . c a n c e l l a t i o n _ r e q u e s t e d   =   F a l s e 
 
                 
 
                 #   =؀  P h a s e   3 :   T r a i n i n g   h i s t o r y   a n d   e v a l u a t i o n 
 
                 s e l f . c u r r e n t _ t r a i n i n g _ r u n _ i d   =   N o n e 
 
                 s e l f . t r a i n i n g _ h i s t o r y _ s t o r a g e   =   N o n e 
 
                 s e l f . m o d e l _ e v a l u a t o r   =   N o n e 
 
                 s e l f . f i r e _ e s t i m a t o r   =   N o n e 
 
                 s e l f . h o l d o u t _ d a t a s e t _ p a t h   =   N o n e 
 
                 
 
                 #   C R I T I C A L   F I X :   T h r e a d - s a f e   w o r k e r   s i g n a l s   f o r   d o c u m e n t   p r o c e s s i n g 
 
                 s e l f . w o r k e r _ s i g n a l s   =   W o r k e r S i g n a l s ( ) 
 
                 s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ f i n i s h e d . c o n n e c t ( 
 
                         s e l f . _ o n _ p r o c e s s i n g _ f i n i s h e d ,   
 
                         Q t . Q u e u e d C o n n e c t i o n     #   E n s u r e   t h r e a d - s a f e   s i g n a l   d e l i v e r y 
 
                 ) 
 
                 s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ e r r o r . c o n n e c t ( 
 
                         s e l f . _ o n _ p r o c e s s i n g _ e r r o r ,   
 
                         Q t . Q u e u e d C o n n e c t i o n 
 
                 ) 
 
                 s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ p r o g r e s s . c o n n e c t ( 
 
                         s e l f . _ o n _ p r o c e s s i n g _ p r o g r e s s ,   
 
                         Q t . Q u e u e d C o n n e c t i o n 
 
                 ) 
 
                 
 
                 l o g g e r . i n f o ( " '  P h a s e   3   T r a i n i n g O r c h e s t r a t o r   i n i t i a l i z e d   w i t h   t h r e a d - s a f e   s i g n a l s " ) 
 
         
 
         d e f   _ s a n i t i z e _ t r a i n i n g _ p a r a m s ( s e l f ,   p a r a m s ) : 
 
                 " " " R e c u r s i v e l y   r e m o v e   n u l l   b y t e s   f r o m   a l l   s t r i n g s   i n   t h e   p a r a m e t e r s " " " 
 
                 i f   i s i n s t a n c e ( p a r a m s ,   d i c t ) : 
 
                         r e t u r n   { k e y :   s e l f . _ s a n i t i z e _ t r a i n i n g _ p a r a m s ( v a l u e )   f o r   k e y ,   v a l u e   i n   p a r a m s . i t e m s ( ) } 
 
                 e l i f   i s i n s t a n c e ( p a r a m s ,   l i s t ) : 
 
                         r e t u r n   [ s e l f . _ s a n i t i z e _ t r a i n i n g _ p a r a m s ( i t e m )   f o r   i t e m   i n   p a r a m s ] 
 
                 e l i f   i s i n s t a n c e ( p a r a m s ,   s t r ) : 
 
                         r e t u r n   p a r a m s . r e p l a c e ( ' \ x 0 0 ' ,   ' ' ) . s t r i p ( ) 
 
                 e l s e : 
 
                         r e t u r n   p a r a m s 
 
         
 
         @ p y q t S l o t ( s t r ) 
 
         d e f   s t a r t _ t r a i n i n g ( s e l f ,   t r a i n i n g _ p a r a m s _ j s o n :   s t r ) : 
 
                 " " " 
 
                 =؀  P h a s e   3 :   S t a r t   e n t e r p r i s e   t r a i n i n g   p r o c e s s   w i t h   F I R E   e s t i m a t i o n   a n d   e v a l u a t i o n 
 
                 
 
                 T h i s   m e t h o d   r u n s   o n   t h e   m a i n   U I   t h r e a d   a n d   o r c h e s t r a t e s   t h e   e n t i r e 
 
                 t r a i n i n g   p i p e l i n e   w i t h   F I R E   e s t i m a t i o n ,   u s e r   c o n t r o l ,   a n d   m o d e l   e v a l u a t i o n . 
 
                 " " " 
 
                 t r y : 
 
                         i f   s e l f . i s _ t r a i n i n g _ a c t i v e : 
 
                                 s e l f . e r r o r _ o c c u r r e d . e m i t ( " L'  T r a i n i n g   a l r e a d y   i n   p r o g r e s s " ) 
 
                                 r e t u r n 
 
                         
 
                         #   C R I T I C A L   F I X :   R e m o v e   n u l l   b y t e s   f r o m   J S O N   s t r i n g   b e f o r e   p a r s i n g 
 
                         c l e a n e d _ j s o n   =   t r a i n i n g _ p a r a m s _ j s o n . r e p l a c e ( ' \ x 0 0 ' ,   ' ' )   i f   t r a i n i n g _ p a r a m s _ j s o n   e l s e   ' ' 
 
                         i f   n o t   c l e a n e d _ j s o n : 
 
                                 r a i s e   V a l u e E r r o r ( " E m p t y   t r a i n i n g   p a r a m e t e r s   p r o v i d e d " ) 
 
                                 
 
                         t r a i n i n g _ p a r a m s   =   j s o n . l o a d s ( c l e a n e d _ j s o n ) 
 
                         
 
                         #   C R I T I C A L   F I X :   R e c u r s i v e l y   s a n i t i z e   a l l   s t r i n g s   i n   t h e   p a r a m e t e r s 
 
                         t r a i n i n g _ p a r a m s   =   s e l f . _ s a n i t i z e _ t r a i n i n g _ p a r a m s ( t r a i n i n g _ p a r a m s ) 
 
                         
 
                         #   =؀  P h a s e   3 :   E n h a n c e d   p a r a m e t e r   v a l i d a t i o n   a n d   l o g g i n g 
 
                         s e l e c t e d _ f i l e s   =   t r a i n i n g _ p a r a m s . g e t ( ' s e l e c t e d _ f i l e s ' ,   [ ] ) 
 
                         a d a p t e r _ n a m e   =   t r a i n i n g _ p a r a m s . g e t ( ' a d a p t e r _ n a m e ' ,   f ' a d a p t e r _ { i n t ( t i m e . t i m e ( ) ) } ' ) 
 
                         b a s e _ m o d e l   =   t r a i n i n g _ p a r a m s . g e t ( ' b a s e _ m o d e l ' ,   ' m i c r o s o f t / D i a l o G P T - s m a l l ' ) 
 
                         p r e s e t   =   t r a i n i n g _ p a r a m s . g e t ( ' t r a i n i n g _ p r e s e t ' ,   ' s t a n d a r d _ t r a i n i n g ' ) 
 
                         
 
                         l o g g e r . i n f o ( f " =؀  S t a r t i n g   P h a s e   3   e n t e r p r i s e   t r a i n i n g : " ) 
 
                         l o g g e r . i n f o ( f "       =  S e l e c t e d   f i l e s :   { l e n ( s e l e c t e d _ f i l e s ) }   f i l e s " ) 
 
                         l o g g e r . i n f o ( f "       <    A d a p t e r   n a m e :   { a d a p t e r _ n a m e } " ) 
 
                         l o g g e r . i n f o ( f "       >  B a s e   m o d e l :   { b a s e _ m o d e l } " ) 
 
                         l o g g e r . i n f o ( f "       &    P r e s e t :   { p r e s e t } " ) 
 
                         
 
                         #   S t o r e   e n h a n c e d   c o n f i g   f o r   l a t e r   u s e 
 
                         s e l f . _ c u r r e n t _ t r a i n i n g _ c o n f i g   =   { 
 
                                 " s e l e c t e d _ f i l e s " :   s e l e c t e d _ f i l e s , 
 
                                 " a d a p t e r _ n a m e " :   a d a p t e r _ n a m e , 
 
                                 " b a s e _ m o d e l " :   b a s e _ m o d e l , 
 
                                 " t r a i n i n g _ p r e s e t " :   p r e s e t , 
 
                                 " s t a r t _ t i m e " :   t i m e . t i m e ( ) 
 
                         } 
 
                         
 
                         #   =؀  P h a s e   3 :   C r e a t e   t r a i n i n g   h i s t o r y   r e c o r d 
 
                         s e l f . c u r r e n t _ t r a i n i n g _ r u n _ i d   =   s e l f . _ c r e a t e _ t r a i n i n g _ r e c o r d ( t r a i n i n g _ p a r a m s ) 
 
                         
 
                         #   R e s e t   s t a t e 
 
                         s e l f . i s _ t r a i n i n g _ a c t i v e   =   T r u e 
 
                         s e l f . c a n c e l l a t i o n _ r e q u e s t e d   =   F a l s e 
 
                         
 
                         #   =؀  P h a s e   3 :   E n h a n c e d   t r a i n i n g   s t a r t   s i g n a l   w i t h   d e t a i l e d   i n f o 
 
                         t r a i n i n g _ i n f o   =   { 
 
                                 " s t a t u s " :   " i n i t i a l i z i n g " , 
 
                                 " m e s s a g e " :   f " S t a r t i n g   t r a i n i n g   f o r   ' { a d a p t e r _ n a m e } ' . . . " , 
 
                                 " p r o g r e s s _ p e r c e n t " :   0 , 
 
                                 " p h a s e " :   " i n i t i a l i z a t i o n " , 
 
                                 " f i l e s _ c o u n t " :   l e n ( s e l e c t e d _ f i l e s )   i f   s e l e c t e d _ f i l e s   e l s e   " a l l " , 
 
                                 " p r e s e t " :   p r e s e t , 
 
                                 " b a s e _ m o d e l " :   b a s e _ m o d e l , 
 
                                 " a d a p t e r _ n a m e " :   a d a p t e r _ n a m e , 
 
                                 " c a n _ c a n c e l " :   T r u e , 
 
                                 " e s t i m a t e d _ d u r a t i o n " :   " C a l c u l a t i n g . . . " ,     #   W i l l   b e   u p d a t e d   b y   F I R E 
 
                                 " t i m e s t a m p " :   t i m e . t i m e ( ) , 
 
                                 " t r a i n i n g _ r u n _ i d " :   s e l f . c u r r e n t _ t r a i n i n g _ r u n _ i d 
 
                         } 
 
                         s e l f . t r a i n i n g _ s t a r t e d . e m i t ( t r a i n i n g _ i n f o ) 
 
                         
 
                         #   =%  P h a s e   3 :   S t a r t   w i t h   F I R E   e s t i m a t i o n 
 
                         s e l f . _ s t a r t _ f i r e _ e s t i m a t i o n _ a n d _ p r o c e s s i n g ( t r a i n i n g _ p a r a m s ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  T r a i n i n g   o r c h e s t r a t o r   s t a r t u p   f a i l e d :   { e } " ) 
 
                         s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                         s e l f . e r r o r _ o c c u r r e d . e m i t ( f " F a i l e d   t o   s t a r t   t r a i n i n g :   { s t r ( e ) } " ) 
 
         
 
         @ p y q t S l o t ( ) 
 
         d e f   c a n c e l _ t r a i n i n g ( s e l f ) : 
 
                 " " " 
 
                 =  C a n c e l   t h e   c u r r e n t   t r a i n i n g   p r o c e s s 
 
                 
 
                 P r o v i d e s   u s e r   c o n t r o l   o v e r   l o n g - r u n n i n g   t r a i n i n g   o p e r a t i o n s . 
 
                 " " " 
 
                 t r y : 
 
                         i f   n o t   s e l f . i s _ t r a i n i n g _ a c t i v e : 
 
                                 l o g g e r . w a r n i n g ( " &  N o   a c t i v e   t r a i n i n g   t o   c a n c e l " ) 
 
                                 r e t u r n 
 
                                 
 
                         l o g g e r . i n f o ( " =  U s e r   r e q u e s t e d   t r a i n i n g   c a n c e l l a t i o n " ) 
 
                         s e l f . c a n c e l l a t i o n _ r e q u e s t e d   =   T r u e 
 
                         
 
                         #   C a n c e l   a c t i v e   t r a i n e r   i f   e x i s t s 
 
                         i f   s e l f . c u r r e n t _ t r a i n e r   a n d   s e l f . c u r r e n t _ t r a i n e r . i s R u n n i n g ( ) : 
 
                                 l o g g e r . i n f o ( " =  S t o p p i n g   a c t i v e   t r a i n e r   t h r e a d " ) 
 
                                 s e l f . c u r r e n t _ t r a i n e r . s t o p ( ) 
 
                                 
 
                                 #   W a i t   f o r   g r a c e f u l   s h u t d o w n 
 
                                 i f   s e l f . c u r r e n t _ t r a i n e r . w a i t ( 5 0 0 0 ) :     #   5   s e c o n d   t i m e o u t 
 
                                         l o g g e r . i n f o ( " '  T r a i n e r   s t o p p e d   g r a c e f u l l y " ) 
 
                                 e l s e : 
 
                                         l o g g e r . w a r n i n g ( " &  T r a i n e r   f o r c e d   t e r m i n a t i o n " ) 
 
                                         s e l f . c u r r e n t _ t r a i n e r . t e r m i n a t e ( ) 
 
                                         
 
                         s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                         s e l f . t r a i n i n g _ c a n c e l l e d . e m i t ( " T r a i n i n g   c a n c e l l e d   b y   u s e r " ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   d u r i n g   t r a i n i n g   c a n c e l l a t i o n :   { e } " ) 
 
                         s e l f . e r r o r _ o c c u r r e d . e m i t ( f " E r r o r   c a n c e l l i n g   t r a i n i n g :   { s t r ( e ) } " ) 
 
         
 
         d e f   _ s t a r t _ d o c u m e n t _ p r o c e s s i n g ( s e l f ,   t r a i n i n g _ p a r a m s :   D i c t [ s t r ,   A n y ] ) : 
 
                 " " " 
 
                 =  S t a r t   d o c u m e n t   p r o c e s s i n g   i n   b a c k g r o u n d   t h r e a d 
 
                 
 
                 T h i s   p r o p e r l y   i s o l a t e s   I / O   o p e r a t i o n s   f r o m   t h e   m a i n   U I   t h r e a d   w h i l e 
 
                 m a i n t a i n i n g   t h r e a d - s a f e   c o m m u n i c a t i o n . 
 
                 " " " 
 
                 d e f   p r o c e s s _ d o c u m e n t s _ w o r k e r ( ) : 
 
                         " " " B a c k g r o u n d   w o r k e r   f o r   d o c u m e n t   p r o c e s s i n g " " " 
 
                         i m p o r t   l o g g i n g 
 
                         w o r k e r _ l o g g e r   =   l o g g i n g . g e t L o g g e r ( f " { _ _ n a m e _ _ } . w o r k e r " ) 
 
                         
 
                         t r y : 
 
                                 f r o m   . d o c u m e n t _ p r o c e s s o r   i m p o r t   A d v a n c e d D o c u m e n t P r o c e s s o r 
 
                                 
 
                                 #   C h e c k   f o r   c a n c e l l a t i o n 
 
                                 i f   s e l f . c a n c e l l a t i o n _ r e q u e s t e d : 
 
                                         s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ e r r o r . e m i t ( " P r o c e s s i n g   c a n c e l l e d   b y   u s e r " ) 
 
                                         r e t u r n 
 
                                 
 
                                 w o r k e r _ l o g g e r . i n f o ( " =  S t a r t i n g   d o c u m e n t   p r o c e s s i n g   i n   b a c k g r o u n d   t h r e a d " ) 
 
                                 s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ p r o g r e s s . e m i t ( " =  D i s c o v e r i n g   u p l o a d e d   f i l e s . . . " ) 
 
                                 
 
                                 #   =؀  P h a s e   2 :   H a n d l e   u s e r - s e l e c t e d   f i l e s   v s   a l l   f i l e s 
 
                                 s e l e c t e d _ f i l e s   =   t r a i n i n g _ p a r a m s . g e t ( ' s e l e c t e d _ f i l e s ' ,   [ ] ) 
 
                                 u p l o a d e d _ f i l e s   =   [ ] 
 
                                 
 
                                 i f   s e l e c t e d _ f i l e s : 
 
                                         #   U s e r   s e l e c t e d   s p e c i f i c   f i l e s 
 
                                         w o r k e r _ l o g g e r . i n f o ( f " =  P r o c e s s i n g   u s e r - s e l e c t e d   f i l e s :   { l e n ( s e l e c t e d _ f i l e s ) }   f i l e s " ) 
 
                                         s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ p r o g r e s s . e m i t ( f " =  P r o c e s s i n g   { l e n ( s e l e c t e d _ f i l e s ) }   s e l e c t e d   f i l e s . . . " ) 
 
                                         
 
                                         #   V a l i d a t e   s e l e c t e d   f i l e s   e x i s t 
 
                                         f o r   f i l e _ n a m e   i n   s e l e c t e d _ f i l e s : 
 
                                                 #   C h e c k   b o t h   p o s s i b l e   d i r e c t o r i e s 
 
                                                 f i l e _ p a t h s   =   [ 
 
                                                         P a t h ( " u p l o a d e d _ b o o k s " )   /   f i l e _ n a m e , 
 
                                                         P a t h ( " d a t a / u p l o a d e d _ b o o k s " )   /   f i l e _ n a m e 
 
                                                 ] 
 
                                                 
 
                                                 f o r   f i l e _ p a t h   i n   f i l e _ p a t h s : 
 
                                                         i f   f i l e _ p a t h . e x i s t s ( )   a n d   f i l e _ p a t h . s u f f i x . l o w e r ( )   i n   [ ' . p d f ' ,   ' . t x t ' ] : 
 
                                                                 u p l o a d e d _ f i l e s . a p p e n d ( f i l e _ p a t h ) 
 
                                                                 w o r k e r _ l o g g e r . i n f o ( f " '  F o u n d   s e l e c t e d   f i l e :   { f i l e _ n a m e } " ) 
 
                                                                 b r e a k 
 
                                                 e l s e : 
 
                                                         w o r k e r _ l o g g e r . w a r n i n g ( f " &  S e l e c t e d   f i l e   n o t   f o u n d :   { f i l e _ n a m e } " ) 
 
                                 e l s e : 
 
                                         #   U s e   a l l   a v a i l a b l e   f i l e s   ( e x i s t i n g   b e h a v i o r ) 
 
                                         w o r k e r _ l o g g e r . i n f o ( " =  N o   s p e c i f i c   f i l e s   s e l e c t e d ,   u s i n g   a l l   a v a i l a b l e   f i l e s " ) 
 
                                         s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ p r o g r e s s . e m i t ( " =  D i s c o v e r i n g   a l l   u p l o a d e d   f i l e s . . . " ) 
 
                                         
 
                                         #   C h e c k   r o o t   l e v e l   u p l o a d e d _ b o o k s   d i r e c t o r y 
 
                                         r o o t _ b o o k s _ d i r   =   P a t h ( " u p l o a d e d _ b o o k s " ) 
 
                                         i f   r o o t _ b o o k s _ d i r . e x i s t s ( ) : 
 
                                                 u p l o a d e d _ f i l e s . e x t e n d ( l i s t ( r o o t _ b o o k s _ d i r . g l o b ( " * . p d f " ) )   +   l i s t ( r o o t _ b o o k s _ d i r . g l o b ( " * . t x t " ) ) ) 
 
                                                 w o r k e r _ l o g g e r . i n f o ( f " =  F o u n d   { l e n ( u p l o a d e d _ f i l e s ) }   f i l e s   i n   r o o t   u p l o a d e d _ b o o k s " ) 
 
                                         
 
                                         #   A l s o   c h e c k   d a t a / u p l o a d e d _ b o o k s   d i r e c t o r y 
 
                                         d a t a _ b o o k s _ d i r   =   P a t h ( " d a t a / u p l o a d e d _ b o o k s " ) 
 
                                         i f   d a t a _ b o o k s _ d i r . e x i s t s ( ) : 
 
                                                 a d d i t i o n a l _ f i l e s   =   l i s t ( d a t a _ b o o k s _ d i r . g l o b ( " * . p d f " ) )   +   l i s t ( d a t a _ b o o k s _ d i r . g l o b ( " * . t x t " ) ) 
 
                                                 e x i s t i n g _ n a m e s   =   { f . n a m e   f o r   f   i n   u p l o a d e d _ f i l e s } 
 
                                                 f o r   f i l e   i n   a d d i t i o n a l _ f i l e s : 
 
                                                         i f   f i l e . n a m e   n o t   i n   e x i s t i n g _ n a m e s : 
 
                                                                 u p l o a d e d _ f i l e s . a p p e n d ( f i l e ) 
 
                                                 w o r k e r _ l o g g e r . i n f o ( f " =  F o u n d   { l e n ( a d d i t i o n a l _ f i l e s ) }   a d d i t i o n a l   f i l e s   i n   d a t a / u p l o a d e d _ b o o k s " ) 
 
                                 
 
                                 i f   n o t   u p l o a d e d _ f i l e s : 
 
                                         i f   s e l e c t e d _ f i l e s : 
 
                                                 s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ e r r o r . e m i t ( f " N o n e   o f   t h e   s e l e c t e d   f i l e s   w e r e   f o u n d :   { s e l e c t e d _ f i l e s } " ) 
 
                                         e l s e : 
 
                                                 s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ e r r o r . e m i t ( " N o   s u p p o r t e d   f i l e s   f o u n d   i n   u p l o a d e d   b o o k s   d i r e c t o r i e s " ) 
 
                                         r e t u r n 
 
                                 
 
                                 i f   s e l f . c a n c e l l a t i o n _ r e q u e s t e d : 
 
                                         s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ e r r o r . e m i t ( " P r o c e s s i n g   c a n c e l l e d   b y   u s e r " ) 
 
                                         r e t u r n 
 
                                         
 
                                 #   =؀  P h a s e   2 :   E n h a n c e d   p r o g r e s s   r e p o r t i n g 
 
                                 f i l e _ s e l e c t i o n _ t y p e   =   " s e l e c t e d "   i f   s e l e c t e d _ f i l e s   e l s e   " a l l   a v a i l a b l e " 
 
                                 w o r k e r _ l o g g e r . i n f o ( f " =  T o t a l   { l e n ( u p l o a d e d _ f i l e s ) }   { f i l e _ s e l e c t i o n _ t y p e }   f i l e s   t o   p r o c e s s " ) 
 
                                 
 
                                 #   L i s t   t h e   f i l e s   b e i n g   p r o c e s s e d 
 
                                 f o r   i ,   f i l e _ p a t h   i n   e n u m e r a t e ( u p l o a d e d _ f i l e s [ : 5 ] ) :     #   S h o w   f i r s t   5   f i l e s 
 
                                         w o r k e r _ l o g g e r . i n f o ( f "       { i + 1 } .   { f i l e _ p a t h . n a m e } " ) 
 
                                 i f   l e n ( u p l o a d e d _ f i l e s )   >   5 : 
 
                                         w o r k e r _ l o g g e r . i n f o ( f "       . . .   a n d   { l e n ( u p l o a d e d _ f i l e s )   -   5 }   m o r e   f i l e s " ) 
 
                                 
 
                                 s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ p r o g r e s s . e m i t ( f " =  P r o c e s s i n g   { l e n ( u p l o a d e d _ f i l e s ) }   { f i l e _ s e l e c t i o n _ t y p e }   f i l e s . . . " ) 
 
                                 
 
                                 #   C r e a t e   p r o c e s s i n g   d i r e c t o r y 
 
                                 p r o c e s s i n g _ d i r   =   P a t h ( " d a t a / p r o c e s s e d _ d o c s " ) 
 
                                 p r o c e s s i n g _ d i r . m k d i r ( e x i s t _ o k = T r u e ) 
 
                                 
 
                                 #   P r o c e s s   d o c u m e n t s 
 
                                 p r o c e s s o r   =   A d v a n c e d D o c u m e n t P r o c e s s o r ( ) 
 
                                 f i l e _ p a t h s   =   [ s t r ( f )   f o r   f   i n   u p l o a d e d _ f i l e s ] 
 
                                 
 
                                 #   P r o c e s s   w i t h   p r o g r e s s   u p d a t e s 
 
                                 r e s u l t   =   p r o c e s s o r . p r o c e s s _ d o c u m e n t s _ a d v a n c e d ( 
 
                                         f i l e _ p a t h s ,   
 
                                         s t r ( p r o c e s s i n g _ d i r ) , 
 
                                         c h u n k _ s i z e = 5 0 0 
 
                                 ) 
 
                                 
 
                                 i f   s e l f . c a n c e l l a t i o n _ r e q u e s t e d : 
 
                                         s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ e r r o r . e m i t ( " P r o c e s s i n g   c a n c e l l e d   b y   u s e r " ) 
 
                                         r e t u r n 
 
                                 
 
                                 i f   n o t   r e s u l t   o r   ' s t a t s '   n o t   i n   r e s u l t : 
 
                                         s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ e r r o r . e m i t ( " D o c u m e n t   p r o c e s s i n g   f a i l e d   t o   r e t u r n   v a l i d   r e s u l t s " ) 
 
                                         r e t u r n 
 
                                 
 
                                 i f   r e s u l t [ ' s t a t s ' ] [ ' t o t a l _ c h u n k s ' ]   = =   0 : 
 
                                         s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ e r r o r . e m i t ( " N o   t r a i n i n g   d a t a   c o u l d   b e   g e n e r a t e d   f r o m   t h e   u p l o a d e d   b o o k s " ) 
 
                                         r e t u r n 
 
                                 
 
                                 #   =؀  P h a s e   2 :   E n h a n c e d   p r o c e s s i n g   c o m p l e t i o n   r e p o r t i n g 
 
                                 s u c c e s s f u l _ f i l e s   =   r e s u l t [ ' s t a t s ' ] [ ' s u c c e s s f u l _ f i l e s ' ] 
 
                                 t o t a l _ c h u n k s   =   r e s u l t [ ' s t a t s ' ] [ ' t o t a l _ c h u n k s ' ] 
 
                                 f a i l e d _ f i l e s   =   r e s u l t [ ' s t a t s ' ] . g e t ( ' f a i l e d _ f i l e s ' ,   0 ) 
 
                                 
 
                                 w o r k e r _ l o g g e r . i n f o ( f " =  P r o c e s s i n g   c o m p l e t e : " ) 
 
                                 w o r k e r _ l o g g e r . i n f o ( f "       '  S u c c e s s f u l l y   p r o c e s s e d :   { s u c c e s s f u l _ f i l e s }   f i l e s " ) 
 
                                 w o r k e r _ l o g g e r . i n f o ( f "       =  G e n e r a t e d   c h u n k s :   { t o t a l _ c h u n k s } " ) 
 
                                 i f   f a i l e d _ f i l e s   >   0 : 
 
                                         w o r k e r _ l o g g e r . i n f o ( f "       L'  F a i l e d   f i l e s :   { f a i l e d _ f i l e s } " ) 
 
                                 
 
                                 #   R e p o r t   p r o c e s s i n g   c o m p l e t i o n 
 
                                 s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ p r o g r e s s . e m i t ( 
 
                                         f " '  P r o c e s s e d   { s u c c e s s f u l _ f i l e s }   f i l e s   !  { t o t a l _ c h u n k s }   t r a i n i n g   c h u n k s " 
 
                                 ) 
 
                                 
 
                                 #   V e r i f y   t r a i n i n g   d a t a   f i l e   e x i s t s 
 
                                 t r a i n i n g _ d a t a _ p a t h   =   p r o c e s s i n g _ d i r   /   " t r a i n i n g _ d a t a s e t . j s o n l " 
 
                                 i f   n o t   t r a i n i n g _ d a t a _ p a t h . e x i s t s ( ) : 
 
                                         s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ e r r o r . e m i t ( f " T r a i n i n g   d a t a   f i l e   n o t   f o u n d :   { t r a i n i n g _ d a t a _ p a t h } " ) 
 
                                         r e t u r n 
 
                                 
 
                                 #   C h e c k   f i l e   s i z e   a n d   c o n t e n t 
 
                                 f i l e _ s i z e   =   t r a i n i n g _ d a t a _ p a t h . s t a t ( ) . s t _ s i z e 
 
                                 w o r k e r _ l o g g e r . i n f o ( f " =  T r a i n i n g   d a t a s e t :   { f i l e _ s i z e   /   1 0 2 4 : . 1 f }   K B " ) 
 
                                 
 
                                 #   S i g n a l   s u c c e s s f u l   c o m p l e t i o n   w i t h   m e t a d a t a 
 
                                 c o m p l e t i o n _ d a t a   =   { 
 
                                         " d a t a s e t _ p a t h " :   s t r ( t r a i n i n g _ d a t a _ p a t h ) , 
 
                                         " s t a t s " :   { 
 
                                                 " s u c c e s s f u l _ f i l e s " :   s u c c e s s f u l _ f i l e s , 
 
                                                 " t o t a l _ c h u n k s " :   t o t a l _ c h u n k s , 
 
                                                 " f a i l e d _ f i l e s " :   f a i l e d _ f i l e s , 
 
                                                 " d a t a s e t _ s i z e _ k b " :   f i l e _ s i z e   /   1 0 2 4 
 
                                         } 
 
                                 } 
 
                                 
 
                                 s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ f i n i s h e d . e m i t ( j s o n . d u m p s ( c o m p l e t i o n _ d a t a ) ) 
 
                                 
 
                         e x c e p t   E x c e p t i o n   a s   e : 
 
                                 w o r k e r _ l o g g e r . e r r o r ( f " L'  D o c u m e n t   p r o c e s s i n g   w o r k e r   f a i l e d :   { e } " ) 
 
                                 s e l f . w o r k e r _ s i g n a l s . p r o c e s s i n g _ e r r o r . e m i t ( f " D o c u m e n t   p r o c e s s i n g   f a i l e d :   { s t r ( e ) } " ) 
 
                 
 
                 #   S t a r t   b a c k g r o u n d   t h r e a d 
 
                 t h r e a d   =   t h r e a d i n g . T h r e a d ( t a r g e t = p r o c e s s _ d o c u m e n t s _ w o r k e r ,   d a e m o n = T r u e ) 
 
                 t h r e a d . s t a r t ( ) 
 
         
 
         @ p y q t S l o t ( s t r ) 
 
         d e f   _ o n _ p r o c e s s i n g _ f i n i s h e d ( s e l f ,   c o m p l e t i o n _ d a t a :   s t r ) : 
 
                 " " " 
 
                 '  P h a s e   3 :   H a n d l e   d o c u m e n t   p r o c e s s i n g   w i t h   h o l d o u t   d a t a s e t   c r e a t i o n   f o r   e v a l u a t i o n 
 
                 
 
                 T h i s   r u n s   o n   t h e   m a i n   U I   t h r e a d   a n d   i s   s a f e   t o   c r e a t e   Q T h r e a d   o b j e c t s . 
 
                 " " " 
 
                 t r y : 
 
                         i f   s e l f . c a n c e l l a t i o n _ r e q u e s t e d : 
 
                                 l o g g e r . i n f o ( " =  T r a i n i n g   c a n c e l l e d   d u r i n g   p r o c e s s i n g " ) 
 
                                 s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                                 s e l f . t r a i n i n g _ c a n c e l l e d . e m i t ( " T r a i n i n g   c a n c e l l e d   d u r i n g   d o c u m e n t   p r o c e s s i n g " ) 
 
                                 r e t u r n 
 
                         
 
                         #   P a r s e   c o m p l e t i o n   d a t a 
 
                         t r y : 
 
                                 c o m p l e t i o n _ i n f o   =   j s o n . l o a d s ( c o m p l e t i o n _ d a t a ) 
 
                                 d a t a s e t _ p a t h   =   c o m p l e t i o n _ i n f o [ " d a t a s e t _ p a t h " ] 
 
                                 s t a t s   =   c o m p l e t i o n _ i n f o [ " s t a t s " ] 
 
                         e x c e p t   ( j s o n . J S O N D e c o d e E r r o r ,   K e y E r r o r ) : 
 
                                 #   F a l l b a c k   f o r   o l d   f o r m a t 
 
                                 d a t a s e t _ p a t h   =   c o m p l e t i o n _ d a t a 
 
                                 s t a t s   =   { } 
 
                                 
 
                         l o g g e r . i n f o ( f " '  D o c u m e n t   p r o c e s s i n g   c o m p l e t e d :   { d a t a s e t _ p a t h } " ) 
 
                         
 
                         #   <د  P h a s e   3 :   C r e a t e   h o l d o u t   d a t a s e t   f o r   m o d e l   e v a l u a t i o n 
 
                         s e l f . _ c r e a t e _ h o l d o u t _ d a t a s e t _ a s y n c ( d a t a s e t _ p a t h ,   s t a t s ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   h a n d l i n g   p r o c e s s i n g   c o m p l e t i o n :   { e } " ) 
 
                         s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                         s e l f . e r r o r _ o c c u r r e d . e m i t ( f " E r r o r   s t a r t i n g   t r a i n i n g :   { s t r ( e ) } " ) 
 
         
 
         d e f   _ c r e a t e _ h o l d o u t _ d a t a s e t _ a s y n c ( s e l f ,   d a t a s e t _ p a t h :   s t r ,   s t a t s :   D i c t [ s t r ,   A n y ] ) : 
 
                 " " " C r e a t e   h o l d o u t   d a t a s e t   i n   b a c k g r o u n d   t h r e a d " " " 
 
                 t r y : 
 
                         l o g g e r . i n f o ( " <د  C r e a t i n g   h o l d o u t   d a t a s e t   f o r   m o d e l   e v a l u a t i o n . . . " ) 
 
                         
 
                         d e f   h o l d o u t _ c r e a t i o n _ w o r k e r ( ) : 
 
                                 t r y : 
 
                                         i m p o r t   l o g g i n g 
 
                                         h o l d o u t _ l o g g e r   =   l o g g i n g . g e t L o g g e r ( f " { _ _ n a m e _ _ } . h o l d o u t _ w o r k e r " ) 
 
                                         
 
                                         #   I n i t i a l i z e   m o d e l   e v a l u a t o r 
 
                                         i f   n o t   s e l f . m o d e l _ e v a l u a t o r : 
 
                                                 f r o m   . m o d e l _ e v a l u a t o r   i m p o r t   M o d e l E v a l u a t o r 
 
                                                 s e l f . m o d e l _ e v a l u a t o r   =   M o d e l E v a l u a t o r ( ) 
 
                                         
 
                                         #   C r e a t e   h o l d o u t   d a t a s e t   ( 1 0 %   o f   d a t a ) 
 
                                         t r a i n i n g _ d a t a s e t _ p a t h ,   h o l d o u t _ d a t a s e t _ p a t h ,   s p l i t _ i n f o   =   s e l f . m o d e l _ e v a l u a t o r . c r e a t e _ h o l d o u t _ d a t a s e t ( 
 
                                                 d a t a s e t _ p a t h ,   h o l d o u t _ r a t i o = 0 . 1 
 
                                         ) 
 
                                         
 
                                         #   S t o r e   h o l d o u t   p a t h   f o r   l a t e r   e v a l u a t i o n 
 
                                         s e l f . h o l d o u t _ d a t a s e t _ p a t h   =   h o l d o u t _ d a t a s e t _ p a t h 
 
                                         
 
                                         h o l d o u t _ l o g g e r . i n f o ( f " =  D a t a s e t   s p l i t   c r e a t e d : " ) 
 
                                         h o l d o u t _ l o g g e r . i n f o ( f "       =  T r a i n i n g :   { s p l i t _ i n f o . g e t ( ' t r a i n i n g _ s a m p l e s ' ,   0 ) }   s a m p l e s " ) 
 
                                         h o l d o u t _ l o g g e r . i n f o ( f "       <د  H o l d o u t :   { s p l i t _ i n f o . g e t ( ' h o l d o u t _ s a m p l e s ' ,   0 ) }   s a m p l e s " ) 
 
                                         
 
                                         #   E m i t   c o m p l e t i o n   s i g n a l   t o   c o n t i n u e   w i t h   t r a i n i n g 
 
                                         Q M e t a O b j e c t . i n v o k e M e t h o d ( 
 
                                                 s e l f ,   " _ o n _ h o l d o u t _ c r e a t i o n _ c o m p l e t e " , 
 
                                                 Q t . Q u e u e d C o n n e c t i o n , 
 
                                                 Q _ A R G ( s t r ,   j s o n . d u m p s ( { 
 
                                                         " t r a i n i n g _ d a t a s e t _ p a t h " :   t r a i n i n g _ d a t a s e t _ p a t h , 
 
                                                         " h o l d o u t _ d a t a s e t _ p a t h " :   h o l d o u t _ d a t a s e t _ p a t h , 
 
                                                         " s p l i t _ i n f o " :   s p l i t _ i n f o , 
 
                                                         " s t a t s " :   s t a t s 
 
                                                 } ) ) 
 
                                         ) 
 
                                         
 
                                 e x c e p t   E x c e p t i o n   a s   e : 
 
                                         h o l d o u t _ l o g g e r . e r r o r ( f " L'  H o l d o u t   d a t a s e t   c r e a t i o n   f a i l e d :   { e } " ) 
 
                                         #   C o n t i n u e   w i t h   o r i g i n a l   d a t a s e t 
 
                                         Q M e t a O b j e c t . i n v o k e M e t h o d ( 
 
                                                 s e l f ,   " _ o n _ h o l d o u t _ c r e a t i o n _ e r r o r " , 
 
                                                 Q t . Q u e u e d C o n n e c t i o n , 
 
                                                 Q _ A R G ( s t r ,   j s o n . d u m p s ( { 
 
                                                         " o r i g i n a l _ d a t a s e t _ p a t h " :   d a t a s e t _ p a t h , 
 
                                                         " e r r o r " :   s t r ( e ) , 
 
                                                         " s t a t s " :   s t a t s 
 
                                                 } ) ) 
 
                                         ) 
 
                         
 
                         #   S t a r t   h o l d o u t   c r e a t i o n   t h r e a d 
 
                         t h r e a d   =   t h r e a d i n g . T h r e a d ( t a r g e t = h o l d o u t _ c r e a t i o n _ w o r k e r ,   d a e m o n = T r u e ) 
 
                         t h r e a d . s t a r t ( ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  F a i l e d   t o   s t a r t   h o l d o u t   c r e a t i o n :   { e } " ) 
 
                         #   F a l l b a c k   t o   o r i g i n a l   d a t a s e t 
 
                         s e l f . _ c o n t i n u e _ w i t h _ t r a i n i n g ( d a t a s e t _ p a t h ,   s t a t s ) 
 
         
 
         @ p y q t S l o t ( s t r ) 
 
         d e f   _ o n _ h o l d o u t _ c r e a t i o n _ c o m p l e t e ( s e l f ,   h o l d o u t _ d a t a :   s t r ) : 
 
                 " " " H a n d l e   s u c c e s s f u l   h o l d o u t   d a t a s e t   c r e a t i o n " " " 
 
                 t r y : 
 
                         d a t a   =   j s o n . l o a d s ( h o l d o u t _ d a t a ) 
 
                         t r a i n i n g _ d a t a s e t _ p a t h   =   d a t a [ " t r a i n i n g _ d a t a s e t _ p a t h " ] 
 
                         h o l d o u t _ d a t a s e t _ p a t h   =   d a t a [ " h o l d o u t _ d a t a s e t _ p a t h " ] 
 
                         s p l i t _ i n f o   =   d a t a [ " s p l i t _ i n f o " ] 
 
                         s t a t s   =   d a t a [ " s t a t s " ] 
 
                         
 
                         l o g g e r . i n f o ( f " '  H o l d o u t   d a t a s e t   c r e a t e d :   { s p l i t _ i n f o . g e t ( ' h o l d o u t _ s a m p l e s ' ,   0 ) }   s a m p l e s   r e s e r v e d " ) 
 
                         
 
                         #   S t o r e   h o l d o u t   p a t h   f o r   e v a l u a t i o n 
 
                         s e l f . h o l d o u t _ d a t a s e t _ p a t h   =   h o l d o u t _ d a t a s e t _ p a t h 
 
                         
 
                         #   C o n t i n u e   w i t h   t r a i n i n g   u s i n g   t h e   s p l i t   t r a i n i n g   d a t a s e t 
 
                         s e l f . _ c o n t i n u e _ w i t h _ t r a i n i n g ( t r a i n i n g _ d a t a s e t _ p a t h ,   s t a t s ,   s p l i t _ i n f o ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   p r o c e s s i n g   h o l d o u t   c r e a t i o n :   { e } " ) 
 
                         #   F a l l b a c k   w i t h o u t   h o l d o u t 
 
                         s e l f . h o l d o u t _ d a t a s e t _ p a t h   =   N o n e 
 
                         s e l f . _ c o n t i n u e _ w i t h _ t r a i n i n g ( d a t a . g e t ( " t r a i n i n g _ d a t a s e t _ p a t h " ,   " " ) ,   s t a t s ) 
 
         
 
         @ p y q t S l o t ( s t r ) 
 
         d e f   _ o n _ h o l d o u t _ c r e a t i o n _ e r r o r ( s e l f ,   e r r o r _ d a t a :   s t r ) : 
 
                 " " " H a n d l e   h o l d o u t   d a t a s e t   c r e a t i o n   e r r o r s " " " 
 
                 t r y : 
 
                         d a t a   =   j s o n . l o a d s ( e r r o r _ d a t a ) 
 
                         o r i g i n a l _ d a t a s e t _ p a t h   =   d a t a [ " o r i g i n a l _ d a t a s e t _ p a t h " ] 
 
                         e r r o r   =   d a t a [ " e r r o r " ] 
 
                         s t a t s   =   d a t a [ " s t a t s " ] 
 
                         
 
                         l o g g e r . w a r n i n g ( f " &  H o l d o u t   c r e a t i o n   f a i l e d :   { e r r o r } " ) 
 
                         l o g g e r . i n f o ( " =  C o n t i n u i n g   w i t h   f u l l   d a t a s e t   ( n o   h o l d o u t   e v a l u a t i o n ) " ) 
 
                         
 
                         #   N o   h o l d o u t   e v a l u a t i o n   w i l l   b e   p o s s i b l e 
 
                         s e l f . h o l d o u t _ d a t a s e t _ p a t h   =   N o n e 
 
                         
 
                         #   C o n t i n u e   w i t h   o r i g i n a l   d a t a s e t 
 
                         s e l f . _ c o n t i n u e _ w i t h _ t r a i n i n g ( o r i g i n a l _ d a t a s e t _ p a t h ,   s t a t s ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   h a n d l i n g   h o l d o u t   c r e a t i o n   e r r o r :   { e } " ) 
 
         
 
         d e f   _ c o n t i n u e _ w i t h _ t r a i n i n g ( s e l f ,   d a t a s e t _ p a t h :   s t r ,   s t a t s :   D i c t [ s t r ,   A n y ] ,   s p l i t _ i n f o :   D i c t [ s t r ,   A n y ]   =   N o n e ) : 
 
                 " " " C o n t i n u e   w i t h   t r a i n i n g   a f t e r   d a t a s e t   p r e p a r a t i o n " " " 
 
                 t r y : 
 
                         #   =؀  P h a s e   3 :   E n h a n c e d   p r o g r e s s   u p d a t e   w i t h   p r o c e s s i n g   s t a t s 
 
                         a d a p t e r _ n a m e   =   g e t a t t r ( s e l f ,   ' _ c u r r e n t _ t r a i n i n g _ c o n f i g ' ,   { } ) . g e t ( ' a d a p t e r _ n a m e ' ,   ' a d a p t e r ' ) 
 
                         
 
                         #   P r e p a r e   m e s s a g e   b a s e d   o n   w h e t h e r   h o l d o u t   w a s   c r e a t e d 
 
                         i f   s p l i t _ i n f o   a n d   s e l f . h o l d o u t _ d a t a s e t _ p a t h : 
 
                                 m e s s a g e   =   f " =  P r o c e s s e d   { s t a t s . g e t ( ' s u c c e s s f u l _ f i l e s ' ,   ' ? ' ) }   f i l e s   !  { s p l i t _ i n f o . g e t ( ' t r a i n i n g _ s a m p l e s ' ,   ' ? ' ) }   t r a i n i n g   +   { s p l i t _ i n f o . g e t ( ' h o l d o u t _ s a m p l e s ' ,   ' ? ' ) }   h o l d o u t   s a m p l e s .   S t a r t i n g   ' { a d a p t e r _ n a m e } '   t r a i n i n g . . . " 
 
                         e l s e : 
 
                                 m e s s a g e   =   f " =  P r o c e s s e d   { s t a t s . g e t ( ' s u c c e s s f u l _ f i l e s ' ,   ' ? ' ) }   f i l e s   !  { s t a t s . g e t ( ' t o t a l _ c h u n k s ' ,   ' ? ' ) }   c h u n k s .   S t a r t i n g   ' { a d a p t e r _ n a m e } '   t r a i n i n g . . . " 
 
                         
 
                         p r o g r e s s _ i n f o   =   { 
 
                                 " s t a t u s " :   " p r o c e s s i n g _ c o m p l e t e " , 
 
                                 " m e s s a g e " :   m e s s a g e , 
 
                                 " p r o g r e s s _ p e r c e n t " :   2 5 , 
 
                                 " p h a s e " :   " m o d e l _ t r a i n i n g " , 
 
                                 " d a t a s e t _ p a t h " :   d a t a s e t _ p a t h , 
 
                                 " p r o c e s s i n g _ s t a t s " :   s t a t s , 
 
                                 " h o l d o u t _ a v a i l a b l e " :   s e l f . h o l d o u t _ d a t a s e t _ p a t h   i s   n o t   N o n e , 
 
                                 " c a n _ c a n c e l " :   T r u e , 
 
                                 " t i m e s t a m p " :   t i m e . t i m e ( ) 
 
                         } 
 
                         s e l f . p r o g r e s s _ u p d a t e . e m i t ( p r o g r e s s _ i n f o ) 
 
                         
 
                         #   N o w   s a f e   t o   c r e a t e   Q T h r e a d   o n   m a i n   t h r e a d 
 
                         s e l f . _ s t a r t _ g o l d e n _ p a t h _ t r a i n i n g ( d a t a s e t _ p a t h ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   c o n t i n u i n g   w i t h   t r a i n i n g :   { e } " ) 
 
                         s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                         s e l f . e r r o r _ o c c u r r e d . e m i t ( f " E r r o r   s t a r t i n g   t r a i n i n g :   { s t r ( e ) } " ) 
 
         
 
         @ p y q t S l o t ( s t r ) 
 
         d e f   _ o n _ p r o c e s s i n g _ e r r o r ( s e l f ,   e r r o r _ m e s s a g e :   s t r ) : 
 
                 " " " H a n d l e   d o c u m e n t   p r o c e s s i n g   e r r o r s " " " 
 
                 l o g g e r . e r r o r ( f " L'  D o c u m e n t   p r o c e s s i n g   e r r o r :   { e r r o r _ m e s s a g e } " ) 
 
                 s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                 s e l f . e r r o r _ o c c u r r e d . e m i t ( f " D o c u m e n t   p r o c e s s i n g   f a i l e d :   { e r r o r _ m e s s a g e } " ) 
 
         
 
         @ p y q t S l o t ( s t r ) 
 
         d e f   _ o n _ p r o c e s s i n g _ p r o g r e s s ( s e l f ,   m e s s a g e :   s t r ) : 
 
                 " " " =؀  P h a s e   2 :   H a n d l e   d o c u m e n t   p r o c e s s i n g   p r o g r e s s   u p d a t e s   w i t h   e n h a n c e d   i n f o " " " 
 
                 #   C a l c u l a t e   p r o g r e s s   b a s e d   o n   m e s s a g e   c o n t e n t 
 
                 p r o g r e s s _ p e r c e n t   =   1 0     #   D e f a u l t   p r o c e s s i n g   p h a s e   i s   1 0 - 2 5 % 
 
                 
 
                 #   T r y   t o   e x t r a c t   m o r e   s p e c i f i c   p r o g r e s s   f r o m   m e s s a g e 
 
                 i f   " f i l e s "   i n   m e s s a g e . l o w e r ( ) : 
 
                         p r o g r e s s _ p e r c e n t   =   1 5 
 
                 e l i f   " c h u n k s "   i n   m e s s a g e . l o w e r ( ) : 
 
                         p r o g r e s s _ p e r c e n t   =   2 0 
 
                 e l i f   " '"   i n   m e s s a g e :     #   C o m p l e t i o n   i n d i c a t o r 
 
                         p r o g r e s s _ p e r c e n t   =   2 4 
 
                         
 
                 p r o g r e s s _ i n f o   =   { 
 
                         " s t a t u s " :   " p r o c e s s i n g " , 
 
                         " m e s s a g e " :   m e s s a g e , 
 
                         " p r o g r e s s _ p e r c e n t " :   p r o g r e s s _ p e r c e n t , 
 
                         " p h a s e " :   " d o c u m e n t _ p r o c e s s i n g " , 
 
                         " c a n _ c a n c e l " :   T r u e , 
 
                         " t i m e s t a m p " :   t i m e . t i m e ( ) 
 
                 } 
 
                 s e l f . p r o g r e s s _ u p d a t e . e m i t ( p r o g r e s s _ i n f o ) 
 
         
 
         d e f   _ s t a r t _ g o l d e n _ p a t h _ t r a i n i n g ( s e l f ,   d a t a s e t _ p a t h :   s t r ) : 
 
                 " " " 
 
                 =؀  S t a r t   G o l d e n   P a t h   t r a i n i n g   w i t h   p r o p e r   Q T h r e a d   m a n a g e m e n t 
 
                 
 
                 T h i s   m e t h o d   r u n s   o n   t h e   m a i n   U I   t h r e a d   a n d   s a f e l y   c r e a t e s   Q T h r e a d   o b j e c t s . 
 
                 " " " 
 
                 t r y : 
 
                         i f   s e l f . c a n c e l l a t i o n _ r e q u e s t e d : 
 
                                 l o g g e r . i n f o ( " =  T r a i n i n g   c a n c e l l e d   b e f o r e   s t a r t i n g " ) 
 
                                 s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                                 s e l f . t r a i n i n g _ c a n c e l l e d . e m i t ( " T r a i n i n g   c a n c e l l e d   b e f o r e   m o d e l   t r a i n i n g " ) 
 
                                 r e t u r n 
 
                                 
 
                         #   G e t   t r a i n i n g   c o n f i g u r a t i o n 
 
                         t r a i n i n g _ c o n f i g   =   s e l f . _ g e t _ t r a i n i n g _ c o n f i g ( ) 
 
                         t r a i n i n g _ c o n f i g [ " t r a i n i n g _ d a t a _ p a t h " ]   =   d a t a s e t _ p a t h 
 
                         
 
                         #   V a l i d a t e   c o n f i g u r a t i o n 
 
                         i f   n o t   s e l f . _ v a l i d a t e _ t r a i n i n g _ c o n f i g ( t r a i n i n g _ c o n f i g ) : 
 
                                 s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                                 s e l f . e r r o r _ o c c u r r e d . e m i t ( " I n v a l i d   t r a i n i n g   c o n f i g u r a t i o n " ) 
 
                                 r e t u r n 
 
                         
 
                         #   I m p o r t   a n d   c r e a t e   t r a i n e r   o n   m a i n   t h r e a d   ( S A F E ) 
 
                         t r y : 
 
                                 f r o m   . g o l d e n _ p a t h _ t r a i n e r   i m p o r t   G o l d e n P a t h T r a i n e r 
 
                         e x c e p t   I m p o r t E r r o r   a s   e : 
 
                                 s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                                 s e l f . e r r o r _ o c c u r r e d . e m i t ( f " C a n n o t   i m p o r t   G o l d e n P a t h T r a i n e r :   { s t r ( e ) } " ) 
 
                                 r e t u r n 
 
                         
 
                         #   C l e a n   u p   p r e v i o u s   t r a i n e r   i f   e x i s t s 
 
                         i f   s e l f . c u r r e n t _ t r a i n e r : 
 
                                 l o g g e r . i n f o ( " >  C l e a n i n g   u p   p r e v i o u s   t r a i n e r " ) 
 
                                 i f   s e l f . c u r r e n t _ t r a i n e r . i s R u n n i n g ( ) : 
 
                                         s e l f . c u r r e n t _ t r a i n e r . s t o p ( ) 
 
                                         s e l f . c u r r e n t _ t r a i n e r . w a i t ( 3 0 0 0 ) 
 
                                 s e l f . c u r r e n t _ t r a i n e r   =   N o n e 
 
                         
 
                         #   C r e a t e   n e w   t r a i n e r   ( S A F E   -   o n   m a i n   t h r e a d ) 
 
                         t r y : 
 
                                 s e l f . c u r r e n t _ t r a i n e r   =   G o l d e n P a t h T r a i n e r ( t r a i n i n g _ c o n f i g ) 
 
                         e x c e p t   E x c e p t i o n   a s   e : 
 
                                 s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                                 s e l f . e r r o r _ o c c u r r e d . e m i t ( f " F a i l e d   t o   c r e a t e   t r a i n e r :   { s t r ( e ) } " ) 
 
                                 r e t u r n 
 
                         
 
                         #   C o n n e c t   t r a i n e r   s i g n a l s   f o r   s t r u c t u r e d   p r o g r e s s 
 
                         s e l f . c u r r e n t _ t r a i n e r . p r o g r e s s . c o n n e c t ( s e l f . _ o n _ t r a i n e r _ p r o g r e s s ) 
 
                         s e l f . c u r r e n t _ t r a i n e r . f i n i s h e d . c o n n e c t ( s e l f . _ o n _ t r a i n e r _ f i n i s h e d ) 
 
                         
 
                         #   C o n n e c t   G P U   u t i l i z a t i o n   i f   a v a i l a b l e 
 
                         i f   h a s a t t r ( s e l f . c u r r e n t _ t r a i n e r ,   ' g p u _ u t i l i z a t i o n ' ) : 
 
                                 s e l f . c u r r e n t _ t r a i n e r . g p u _ u t i l i z a t i o n . c o n n e c t ( s e l f . _ o n _ g p u _ u t i l i z a t i o n ) 
 
                         
 
                         #   S t a r t   t r a i n i n g 
 
                         l o g g e r . i n f o ( " =؀  S t a r t i n g   G o l d e n   P a t h   t r a i n e r   o n   m a i n   t h r e a d " ) 
 
                         s e l f . c u r r e n t _ t r a i n e r . s t a r t ( ) 
 
                         
 
                         #   E m i t   t r a i n i n g   s t a r t   p r o g r e s s 
 
                         p r o g r e s s _ i n f o   =   { 
 
                                 " s t a t u s " :   " t r a i n i n g _ a c t i v e " , 
 
                                 " m e s s a g e " :   " G o l d e n   P a t h   t r a i n i n g   s t a r t e d . . . " , 
 
                                 " p r o g r e s s _ p e r c e n t " :   3 0 , 
 
                                 " p h a s e " :   " m o d e l _ t r a i n i n g " , 
 
                                 " c a n _ c a n c e l " :   T r u e 
 
                         } 
 
                         s e l f . p r o g r e s s _ u p d a t e . e m i t ( p r o g r e s s _ i n f o ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   s t a r t i n g   G o l d e n   P a t h   t r a i n i n g :   { e } " ) 
 
                         s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                         s e l f . e r r o r _ o c c u r r e d . e m i t ( f " F a i l e d   t o   s t a r t   t r a i n i n g :   { s t r ( e ) } " ) 
 
         
 
         @ p y q t S l o t ( s t r ) 
 
         d e f   _ o n _ t r a i n e r _ p r o g r e s s ( s e l f ,   m e s s a g e :   s t r ) : 
 
                 " " " H a n d l e   t r a i n e r   p r o g r e s s   u p d a t e s   w i t h   s t r u c t u r e d   d a t a " " " 
 
                 p r o g r e s s _ i n f o   =   { 
 
                         " s t a t u s " :   " t r a i n i n g _ a c t i v e " , 
 
                         " m e s s a g e " :   m e s s a g e , 
 
                         " p r o g r e s s _ p e r c e n t " :   5 0 ,     #   T r a i n i n g   i s   3 0 - 9 0 % 
 
                         " p h a s e " :   " m o d e l _ t r a i n i n g " , 
 
                         " c a n _ c a n c e l " :   T r u e 
 
                 } 
 
                 s e l f . p r o g r e s s _ u p d a t e . e m i t ( p r o g r e s s _ i n f o ) 
 
         
 
         @ p y q t S l o t ( b o o l ,   s t r ) 
 
         d e f   _ o n _ t r a i n e r _ f i n i s h e d ( s e l f ,   s u c c e s s :   b o o l ,   m e s s a g e :   s t r ) : 
 
                 " " " <د  P h a s e   3 :   H a n d l e   t r a i n i n g   c o m p l e t i o n   w i t h   m o d e l   e v a l u a t i o n " " " 
 
                 t r y : 
 
                         s e l f . i s _ t r a i n i n g _ a c t i v e   =   F a l s e 
 
                         
 
                         i f   s u c c e s s : 
 
                                 l o g g e r . i n f o ( f " '  T r a i n i n g   c o m p l e t e d   s u c c e s s f u l l y :   { m e s s a g e } " ) 
 
                                 
 
                                 #   <د  P h a s e   3 :   S t a r t   m o d e l   e v a l u a t i o n   i f   h o l d o u t   d a t a s e t   a v a i l a b l e 
 
                                 i f   s e l f . h o l d o u t _ d a t a s e t _ p a t h   a n d   s e l f . m o d e l _ e v a l u a t o r : 
 
                                         l o g g e r . i n f o ( " <د  S t a r t i n g   a u t o m a t e d   m o d e l   e v a l u a t i o n . . . " ) 
 
                                         s e l f . _ s t a r t _ m o d e l _ e v a l u a t i o n _ a s y n c ( m e s s a g e ) 
 
                                 e l s e : 
 
                                         l o g g e r . i n f o ( " &  N o   h o l d o u t   d a t a s e t   a v a i l a b l e ,   s k i p p i n g   e v a l u a t i o n " ) 
 
                                         s e l f . _ c o m p l e t e _ t r a i n i n g _ w i t h _ r e s u l t s ( s u c c e s s ,   m e s s a g e ,   N o n e ) 
 
                         e l s e : 
 
                                 l o g g e r . e r r o r ( f " L'  T r a i n i n g   f a i l e d :   { m e s s a g e } " ) 
 
                                 #   C o m p l e t e   t r a i n i n g   r e c o r d   w i t h   f a i l u r e 
 
                                 s e l f . _ c o m p l e t e _ t r a i n i n g _ w i t h _ r e s u l t s ( s u c c e s s ,   m e s s a g e ,   N o n e ) 
 
                         
 
                         #   C l e a n   u p   t r a i n e r 
 
                         i f   s e l f . c u r r e n t _ t r a i n e r : 
 
                                 i f   s e l f . c u r r e n t _ t r a i n e r . i s R u n n i n g ( ) : 
 
                                         s e l f . c u r r e n t _ t r a i n e r . w a i t ( 1 0 0 0 ) 
 
                                 s e l f . c u r r e n t _ t r a i n e r   =   N o n e 
 
                                 
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   i n   t r a i n i n g   c o m p l e t i o n   h a n d l e r :   { e } " ) 
 
                         s e l f . e r r o r _ o c c u r r e d . e m i t ( f " E r r o r   h a n d l i n g   t r a i n i n g   c o m p l e t i o n :   { s t r ( e ) } " ) 
 
         
 
         d e f   _ s t a r t _ m o d e l _ e v a l u a t i o n _ a s y n c ( s e l f ,   t r a i n i n g _ m e s s a g e :   s t r ) : 
 
                 " " " S t a r t   m o d e l   e v a l u a t i o n   i n   b a c k g r o u n d   t h r e a d " " " 
 
                 t r y : 
 
                         l o g g e r . i n f o ( " <د  S t a r t i n g   a u t o m a t e d   m o d e l   e v a l u a t i o n . . . " ) 
 
                         
 
                         d e f   e v a l u a t i o n _ w o r k e r ( ) : 
 
                                 t r y : 
 
                                         i m p o r t   l o g g i n g 
 
                                         e v a l _ l o g g e r   =   l o g g i n g . g e t L o g g e r ( f " { _ _ n a m e _ _ } . e v a l _ w o r k e r " ) 
 
                                         
 
                                         #   G e t   t r a i n i n g   c o n f i g u r a t i o n   f o r   e v a l u a t i o n 
 
                                         t r a i n i n g _ c o n f i g   =   g e t a t t r ( s e l f ,   ' _ c u r r e n t _ t r a i n i n g _ c o n f i g ' ,   { } ) 
 
                                         b a s e _ m o d e l   =   t r a i n i n g _ c o n f i g . g e t ( ' b a s e _ m o d e l ' ,   ' m i c r o s o f t / D i a l o G P T - s m a l l ' ) 
 
                                         a d a p t e r _ n a m e   =   t r a i n i n g _ c o n f i g . g e t ( ' a d a p t e r _ n a m e ' ,   ' u n k n o w n _ a d a p t e r ' ) 
 
                                         
 
                                         #   D e t e r m i n e   a d a p t e r   p a t h 
 
                                         a d a p t e r _ p a t h   =   f " l o r a _ a d a p t e r s _ m i s t r a l / { a d a p t e r _ n a m e } " 
 
                                         
 
                                         e v a l _ l o g g e r . i n f o ( f " <د  E v a l u a t i n g   m o d e l   p e r f o r m a n c e : " ) 
 
                                         e v a l _ l o g g e r . i n f o ( f "       >  B a s e   m o d e l :   { b a s e _ m o d e l } " ) 
 
                                         e v a l _ l o g g e r . i n f o ( f "       =  A d a p t e r :   { a d a p t e r _ p a t h } " ) 
 
                                         e v a l _ l o g g e r . i n f o ( f "       =  H o l d o u t   d a t a s e t :   { s e l f . h o l d o u t _ d a t a s e t _ p a t h } " ) 
 
                                         
 
                                         #   P e r f o r m   e v a l u a t i o n 
 
                                         e v a l u a t i o n _ r e s u l t   =   s e l f . m o d e l _ e v a l u a t o r . e v a l u a t e _ m o d e l _ p e r f o r m a n c e ( 
 
                                                 b a s e _ m o d e l _ i d = b a s e _ m o d e l , 
 
                                                 t r a i n e d _ a d a p t e r _ p a t h = a d a p t e r _ p a t h , 
 
                                                 h o l d o u t _ d a t a s e t _ p a t h = s e l f . h o l d o u t _ d a t a s e t _ p a t h 
 
                                         ) 
 
                                         
 
                                         e v a l _ l o g g e r . i n f o ( f " =  E v a l u a t i o n   c o m p l e t e : " ) 
 
                                         e v a l _ l o g g e r . i n f o ( f "       =  B a s e   m o d e l   s c o r e :   { e v a l u a t i o n _ r e s u l t . b a s e _ m o d e l _ s c o r e : . 2 f } " ) 
 
                                         e v a l _ l o g g e r . i n f o ( f "       =؀  T r a i n e d   m o d e l   s c o r e :   { e v a l u a t i o n _ r e s u l t . t r a i n e d _ m o d e l _ s c o r e : . 2 f } " ) 
 
                                         e v a l _ l o g g e r . i n f o ( f "       <د  I m p r o v e m e n t :   { e v a l u a t i o n _ r e s u l t . i m p r o v e m e n t _ p e r c e n t a g e : + . 1 f } % " ) 
 
                                         
 
                                         #   E m i t   e v a l u a t i o n   c o m p l e t i o n 
 
                                         Q M e t a O b j e c t . i n v o k e M e t h o d ( 
 
                                                 s e l f ,   " _ o n _ m o d e l _ e v a l u a t i o n _ c o m p l e t e " , 
 
                                                 Q t . Q u e u e d C o n n e c t i o n , 
 
                                                 Q _ A R G ( s t r ,   j s o n . d u m p s ( { 
 
                                                         " t r a i n i n g _ m e s s a g e " :   t r a i n i n g _ m e s s a g e , 
 
                                                         " e v a l u a t i o n _ r e s u l t " :   { 
 
                                                                 " b a s e _ m o d e l _ s c o r e " :   e v a l u a t i o n _ r e s u l t . b a s e _ m o d e l _ s c o r e , 
 
                                                                 " t r a i n e d _ m o d e l _ s c o r e " :   e v a l u a t i o n _ r e s u l t . t r a i n e d _ m o d e l _ s c o r e , 
 
                                                                 " i m p r o v e m e n t _ p e r c e n t a g e " :   e v a l u a t i o n _ r e s u l t . i m p r o v e m e n t _ p e r c e n t a g e , 
 
                                                                 " h o l d o u t _ q u e s t i o n s _ c o u n t " :   e v a l u a t i o n _ r e s u l t . h o l d o u t _ q u e s t i o n s _ c o u n t , 
 
                                                                 " e v a l u a t i o n _ m e t h o d " :   e v a l u a t i o n _ r e s u l t . e v a l u a t i o n _ m e t h o d , 
 
                                                                 " s u c c e s s " :   e v a l u a t i o n _ r e s u l t . s u c c e s s , 
 
                                                                 " e r r o r _ m e s s a g e " :   e v a l u a t i o n _ r e s u l t . e r r o r _ m e s s a g e 
 
                                                         } , 
 
                                                         " a d a p t e r _ p a t h " :   a d a p t e r _ p a t h 
 
                                                 } ) ) 
 
                                         ) 
 
                                         
 
                                 e x c e p t   E x c e p t i o n   a s   e : 
 
                                         e v a l _ l o g g e r . e r r o r ( f " L'  M o d e l   e v a l u a t i o n   f a i l e d :   { e } " ) 
 
                                         #   C o m p l e t e   w i t h o u t   e v a l u a t i o n 
 
                                         Q M e t a O b j e c t . i n v o k e M e t h o d ( 
 
                                                 s e l f ,   " _ o n _ m o d e l _ e v a l u a t i o n _ e r r o r " , 
 
                                                 Q t . Q u e u e d C o n n e c t i o n , 
 
                                                 Q _ A R G ( s t r ,   j s o n . d u m p s ( { 
 
                                                         " t r a i n i n g _ m e s s a g e " :   t r a i n i n g _ m e s s a g e , 
 
                                                         " e r r o r " :   s t r ( e ) 
 
                                                 } ) ) 
 
                                         ) 
 
                         
 
                         #   S t a r t   e v a l u a t i o n   t h r e a d 
 
                         t h r e a d   =   t h r e a d i n g . T h r e a d ( t a r g e t = e v a l u a t i o n _ w o r k e r ,   d a e m o n = T r u e ) 
 
                         t h r e a d . s t a r t ( ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  F a i l e d   t o   s t a r t   m o d e l   e v a l u a t i o n :   { e } " ) 
 
                         #   C o m p l e t e   w i t h o u t   e v a l u a t i o n 
 
                         s e l f . _ c o m p l e t e _ t r a i n i n g _ w i t h _ r e s u l t s ( T r u e ,   t r a i n i n g _ m e s s a g e ,   N o n e ) 
 
         
 
         @ p y q t S l o t ( s t r ) 
 
         d e f   _ o n _ m o d e l _ e v a l u a t i o n _ c o m p l e t e ( s e l f ,   e v a l u a t i o n _ d a t a :   s t r ) : 
 
                 " " " H a n d l e   c o m p l e t e d   m o d e l   e v a l u a t i o n " " " 
 
                 t r y : 
 
                         d a t a   =   j s o n . l o a d s ( e v a l u a t i o n _ d a t a ) 
 
                         t r a i n i n g _ m e s s a g e   =   d a t a [ " t r a i n i n g _ m e s s a g e " ] 
 
                         e v a l u a t i o n _ r e s u l t   =   d a t a [ " e v a l u a t i o n _ r e s u l t " ] 
 
                         a d a p t e r _ p a t h   =   d a t a [ " a d a p t e r _ p a t h " ] 
 
                         
 
                         l o g g e r . i n f o ( f " '  M o d e l   e v a l u a t i o n   c o m p l e t e d : " ) 
 
                         l o g g e r . i n f o ( f "       =  I m p r o v e m e n t :   { e v a l u a t i o n _ r e s u l t [ ' i m p r o v e m e n t _ p e r c e n t a g e ' ] : + . 1 f } % " ) 
 
                         
 
                         #   S a v e   e v a l u a t i o n   t o   d a t a b a s e 
 
                         i f   s e l f . c u r r e n t _ t r a i n i n g _ r u n _ i d   a n d   s e l f . t r a i n i n g _ h i s t o r y _ s t o r a g e : 
 
                                 s e l f . t r a i n i n g _ h i s t o r y _ s t o r a g e . s a v e _ m o d e l _ e v a l u a t i o n ( 
 
                                         s e l f . c u r r e n t _ t r a i n i n g _ r u n _ i d ,   e v a l u a t i o n _ r e s u l t 
 
                                 ) 
 
                         
 
                         #   C o m p l e t e   t r a i n i n g   w i t h   e v a l u a t i o n   r e s u l t s 
 
                         s e l f . _ c o m p l e t e _ t r a i n i n g _ w i t h _ r e s u l t s ( T r u e ,   t r a i n i n g _ m e s s a g e ,   e v a l u a t i o n _ r e s u l t ,   a d a p t e r _ p a t h ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   p r o c e s s i n g   e v a l u a t i o n   c o m p l e t i o n :   { e } " ) 
 
                         s e l f . _ c o m p l e t e _ t r a i n i n g _ w i t h _ r e s u l t s ( T r u e ,   " T r a i n i n g   c o m p l e t e d " ,   N o n e ) 
 
         
 
         @ p y q t S l o t ( s t r ) 
 
         d e f   _ o n _ m o d e l _ e v a l u a t i o n _ e r r o r ( s e l f ,   e r r o r _ d a t a :   s t r ) : 
 
                 " " " H a n d l e   m o d e l   e v a l u a t i o n   e r r o r s " " " 
 
                 t r y : 
 
                         d a t a   =   j s o n . l o a d s ( e r r o r _ d a t a ) 
 
                         t r a i n i n g _ m e s s a g e   =   d a t a [ " t r a i n i n g _ m e s s a g e " ] 
 
                         e r r o r   =   d a t a [ " e r r o r " ] 
 
                         
 
                         l o g g e r . w a r n i n g ( f " &  M o d e l   e v a l u a t i o n   f a i l e d :   { e r r o r } " ) 
 
                         
 
                         #   C o m p l e t e   t r a i n i n g   w i t h o u t   e v a l u a t i o n 
 
                         s e l f . _ c o m p l e t e _ t r a i n i n g _ w i t h _ r e s u l t s ( T r u e ,   t r a i n i n g _ m e s s a g e ,   N o n e ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   h a n d l i n g   e v a l u a t i o n   e r r o r :   { e } " ) 
 
                         s e l f . _ c o m p l e t e _ t r a i n i n g _ w i t h _ r e s u l t s ( T r u e ,   " T r a i n i n g   c o m p l e t e d " ,   N o n e ) 
 
         
 
         d e f   _ c o m p l e t e _ t r a i n i n g _ w i t h _ r e s u l t s ( s e l f ,   s u c c e s s :   b o o l ,   m e s s a g e :   s t r ,   e v a l u a t i o n _ r e s u l t :   O p t i o n a l [ D i c t ]   =   N o n e ,   a d a p t e r _ p a t h :   s t r   =   N o n e ) : 
 
                 " " " C o m p l e t e   t r a i n i n g   w i t h   f i n a l   r e s u l t s   a n d   d a t a b a s e   u p d a t e s " " " 
 
                 t r y : 
 
                         #   <د  P h a s e   3 :   U p d a t e   t r a i n i n g   h i s t o r y   w i t h   f i n a l   r e s u l t s 
 
                         i f   s e l f . c u r r e n t _ t r a i n i n g _ r u n _ i d   a n d   s e l f . t r a i n i n g _ h i s t o r y _ s t o r a g e : 
 
                                 #   G a t h e r   f i n a l   m e t r i c s 
 
                                 f i n a l _ m e t r i c s   =   { 
 
                                         " a d a p t e r _ p a t h " :   a d a p t e r _ p a t h , 
 
                                         " d a t a s e t _ s i z e _ m b " :   0 ,     #   C o u l d   b e   c a l c u l a t e d   f r o m   d a t a s e t 
 
                                         " f i n a l _ l o s s " :   0 . 0 ,           #   C o u l d   b e   e x t r a c t e d   f r o m   t r a i n e r 
 
                                         " f i n a l _ a c c u r a c y " :   0 . 0     #   C o u l d   b e   e x t r a c t e d   f r o m   t r a i n e r 
 
                                 } 
 
                                 
 
                                 #   A d d   e v a l u a t i o n   m e t r i c s   i f   a v a i l a b l e 
 
                                 i f   e v a l u a t i o n _ r e s u l t : 
 
                                         f i n a l _ m e t r i c s [ " e v a l u a t i o n _ s c o r e " ]   =   e v a l u a t i o n _ r e s u l t . g e t ( " t r a i n e d _ m o d e l _ s c o r e " ) 
 
                                         f i n a l _ m e t r i c s [ " i m p r o v e m e n t _ s c o r e " ]   =   e v a l u a t i o n _ r e s u l t . g e t ( " i m p r o v e m e n t _ p e r c e n t a g e " ) 
 
                                 
 
                                 #   C o m p l e t e   t h e   t r a i n i n g   r e c o r d 
 
                                 e r r o r _ m e s s a g e   =   N o n e   i f   s u c c e s s   e l s e   m e s s a g e 
 
                                 s e l f . t r a i n i n g _ h i s t o r y _ s t o r a g e . c o m p l e t e _ t r a i n i n g _ r u n ( 
 
                                         s e l f . c u r r e n t _ t r a i n i n g _ r u n _ i d ,   s u c c e s s ,   f i n a l _ m e t r i c s ,   e r r o r _ m e s s a g e 
 
                                 ) 
 
                         
 
                         #   P r e p a r e   c o m p l e t i o n   m e s s a g e   w i t h   e v a l u a t i o n   r e s u l t s 
 
                         i f   s u c c e s s   a n d   e v a l u a t i o n _ r e s u l t : 
 
                                 i m p r o v e m e n t   =   e v a l u a t i o n _ r e s u l t . g e t ( " i m p r o v e m e n t _ p e r c e n t a g e " ,   0 ) 
 
                                 i f   i m p r o v e m e n t   >   0 : 
 
                                         f i n a l _ m e s s a g e   =   f " { m e s s a g e }   <د  M o d e l   i m p r o v e d   b y   { i m p r o v e m e n t : + . 1 f } %   o n   e v a l u a t i o n ! " 
 
                                 e l s e : 
 
                                         f i n a l _ m e s s a g e   =   f " { m e s s a g e }   &  M o d e l   s h o w e d   { i m p r o v e m e n t : . 1 f } %   c h a n g e   o n   e v a l u a t i o n . " 
 
                         e l s e : 
 
                                 f i n a l _ m e s s a g e   =   m e s s a g e 
 
                         
 
                         #   E m i t   f i n a l   c o m p l e t i o n 
 
                         c o m p l e t i o n _ i n f o   =   { 
 
                                 " s u c c e s s " :   s u c c e s s , 
 
                                 " m e s s a g e " :   f i n a l _ m e s s a g e , 
 
                                 " p r o g r e s s _ p e r c e n t " :   1 0 0   i f   s u c c e s s   e l s e   0 , 
 
                                 " p h a s e " :   " c o m p l e t e d "   i f   s u c c e s s   e l s e   " f a i l e d " , 
 
                                 " e v a l u a t i o n _ r e s u l t " :   e v a l u a t i o n _ r e s u l t , 
 
                                 " t r a i n i n g _ r u n _ i d " :   s e l f . c u r r e n t _ t r a i n i n g _ r u n _ i d 
 
                         } 
 
                         s e l f . t r a i n i n g _ c o m p l e t e d . e m i t ( c o m p l e t i o n _ i n f o ) 
 
                         
 
                         #   R e s e t   s t a t e 
 
                         s e l f . c u r r e n t _ t r a i n i n g _ r u n _ i d   =   N o n e 
 
                         s e l f . h o l d o u t _ d a t a s e t _ p a t h   =   N o n e 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   c o m p l e t i n g   t r a i n i n g   w i t h   r e s u l t s :   { e } " ) 
 
                         #   F a l l b a c k   c o m p l e t i o n 
 
                         c o m p l e t i o n _ i n f o   =   { 
 
                                 " s u c c e s s " :   s u c c e s s , 
 
                                 " m e s s a g e " :   m e s s a g e , 
 
                                 " p r o g r e s s _ p e r c e n t " :   1 0 0   i f   s u c c e s s   e l s e   0 , 
 
                                 " p h a s e " :   " c o m p l e t e d "   i f   s u c c e s s   e l s e   " f a i l e d " 
 
                         } 
 
                         s e l f . t r a i n i n g _ c o m p l e t e d . e m i t ( c o m p l e t i o n _ i n f o ) 
 
         
 
         @ p y q t S l o t ( f l o a t ) 
 
         d e f   _ o n _ g p u _ u t i l i z a t i o n ( s e l f ,   u t i l i z a t i o n :   f l o a t ) : 
 
                 " " " H a n d l e   G P U   u t i l i z a t i o n   u p d a t e s " " " 
 
                 t r y : 
 
                         p r o g r e s s _ i n f o   =   { 
 
                                 " s t a t u s " :   " t r a i n i n g _ a c t i v e " , 
 
                                 " m e s s a g e " :   f " G P U   U t i l i z a t i o n :   { u t i l i z a t i o n : . 1 f } % " , 
 
                                 " p r o g r e s s _ p e r c e n t " :   5 0 , 
 
                                 " p h a s e " :   " m o d e l _ t r a i n i n g " , 
 
                                 " g p u _ u t i l i z a t i o n " :   u t i l i z a t i o n , 
 
                                 " c a n _ c a n c e l " :   T r u e 
 
                         } 
 
                         s e l f . p r o g r e s s _ u p d a t e . e m i t ( p r o g r e s s _ i n f o ) 
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . w a r n i n g ( f " &  E r r o r   u p d a t i n g   G P U   u t i l i z a t i o n :   { e } " ) 
 
         
 
         d e f   _ g e t _ t r a i n i n g _ c o n f i g ( s e l f )   - >   D i c t [ s t r ,   A n y ] : 
 
                 " " " 
 
                 =؀  P h a s e   2 :   G e t   e n h a n c e d   t r a i n i n g   c o n f i g u r a t i o n   w i t h   u s e r   c u s t o m i z a t i o n 
 
                 
 
                 U s e s   t h e   u s e r - s e l e c t e d   p r e s e t   a n d   a p p l i e s   c u s t o m i z a t i o n s   l i k e   a d a p t e r   n a m e , 
 
                 b a s e   m o d e l ,   a n d   s e l e c t e d   f i l e s . 
 
                 " " " 
 
                 t r y : 
 
                         #   G e t   u s e r ' s   s e l e c t e d   p r e s e t 
 
                         p r e s e t _ n a m e   =   g e t a t t r ( s e l f ,   ' _ c u r r e n t _ t r a i n i n g _ c o n f i g ' ,   { } ) . g e t ( ' t r a i n i n g _ p r e s e t ' ,   ' s t a n d a r d _ t r a i n i n g ' ) 
 
                         a d a p t e r _ n a m e   =   g e t a t t r ( s e l f ,   ' _ c u r r e n t _ t r a i n i n g _ c o n f i g ' ,   { } ) . g e t ( ' a d a p t e r _ n a m e ' ,   ' m y _ a d a p t e r ' ) 
 
                         b a s e _ m o d e l   =   g e t a t t r ( s e l f ,   ' _ c u r r e n t _ t r a i n i n g _ c o n f i g ' ,   { } ) . g e t ( ' b a s e _ m o d e l ' ,   ' m i c r o s o f t / D i a l o G P T - s m a l l ' ) 
 
                         
 
                         l o g g e r . i n f o ( f " ='  B u i l d i n g   c o n f i g   f o r   p r e s e t :   { p r e s e t _ n a m e } " ) 
 
                         
 
                         #   G e t   p r e s e t   c o n f i g u r a t i o n 
 
                         c o n f i g   =   s e l f . _ g e t _ p r e s e t _ c o n f i g ( p r e s e t _ n a m e ) 
 
                         
 
                         #   =؀  P h a s e   2 :   A p p l y   u s e r   c u s t o m i z a t i o n s 
 
                         c o n f i g [ " b a s e _ m o d e l " ]   =   b a s e _ m o d e l 
 
                         c o n f i g [ " o u t p u t _ d i r " ]   =   f " l o r a _ a d a p t e r s _ m i s t r a l / { a d a p t e r _ n a m e } " 
 
                         
 
                         #   A d d   m e t a d a t a   f o r   t r a c k i n g 
 
                         c o n f i g [ " m e t a d a t a " ]   =   { 
 
                                 " a d a p t e r _ n a m e " :   a d a p t e r _ n a m e , 
 
                                 " c r e a t e d _ b y " :   " t r a i n i n g _ o r c h e s t r a t o r " , 
 
                                 " p r e s e t _ u s e d " :   p r e s e t _ n a m e , 
 
                                 " b a s e _ m o d e l " :   b a s e _ m o d e l , 
 
                                 " c r e a t i o n _ t i m e " :   t i m e . t i m e ( ) 
 
                         } 
 
                         
 
                         l o g g e r . i n f o ( f " '  T r a i n i n g   c o n f i g   p r e p a r e d :   { a d a p t e r _ n a m e }   o n   { b a s e _ m o d e l } " ) 
 
                         r e t u r n   c o n f i g 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   l o a d i n g   t r a i n i n g   c o n f i g :   { e } " ) 
 
                         r e t u r n   s e l f . _ g e t _ f a l l b a c k _ c o n f i g ( ) 
 
         
 
         d e f   _ g e t _ p r e s e t _ c o n f i g ( s e l f ,   p r e s e t _ n a m e :   s t r )   - >   D i c t [ s t r ,   A n y ] : 
 
                 " " " G e t   c o n f i g u r a t i o n   f o r   a   s p e c i f i c   p r e s e t " " " 
 
                 t r y : 
 
                         f r o m   . a p p _ c o n f i g   i m p o r t   A p p C o n f i g 
 
                         a p p _ c o n f i g   =   A p p C o n f i g ( ) 
 
                         
 
                         #   T r y   t o   g e t   f r o m   c o n f i g   f i l e 
 
                         c o n f i g   =   a p p _ c o n f i g . g e t _ c o n f i g _ v a l u e ( f " t r a i n i n g . p r e s e t s . { p r e s e t _ n a m e } " ,   { } ) 
 
                         
 
                         i f   c o n f i g   a n d   i s i n s t a n c e ( c o n f i g ,   d i c t ) : 
 
                                 r e t u r n   s e l f . _ v a l i d a t e _ a n d _ c o m p l e t e _ c o n f i g ( c o n f i g ) 
 
                         e l s e : 
 
                                 l o g g e r . w a r n i n g ( f " &  P r e s e t   ' { p r e s e t _ n a m e } '   n o t   f o u n d ,   u s i n g   h a r d c o d e d   p r e s e t s " ) 
 
                                 r e t u r n   s e l f . _ g e t _ h a r d c o d e d _ p r e s e t ( p r e s e t _ n a m e ) 
 
                                 
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   l o a d i n g   p r e s e t   ' { p r e s e t _ n a m e } ' :   { e } " ) 
 
                         r e t u r n   s e l f . _ g e t _ h a r d c o d e d _ p r e s e t ( p r e s e t _ n a m e ) 
 
         
 
         d e f   _ g e t _ h a r d c o d e d _ p r e s e t ( s e l f ,   p r e s e t _ n a m e :   s t r )   - >   D i c t [ s t r ,   A n y ] : 
 
                 " " " G e t   h a r d c o d e d   p r e s e t   c o n f i g u r a t i o n s   a s   f a l l b a c k " " " 
 
                 p r e s e t s   =   { 
 
                         " q u i c k _ t r a i n i n g " :   { 
 
                                 " b a s e _ m o d e l " :   " m i c r o s o f t / D i a l o G P T - s m a l l " , 
 
                                 " l o r a " :   { " r " :   8 ,   " a l p h a " :   1 6 ,   " d r o p o u t " :   0 . 1 ,   " t a r g e t _ m o d u l e s " :   [ " c _ a t t n " ] } , 
 
                                 " t r a i n i n g " :   { " e p o c h s " :   1 ,   " b a t c h _ s i z e " :   2 ,   " l e a r n i n g _ r a t e " :   0 . 0 0 0 1 ,   " m a x _ s t e p s " :   1 0 0 } 
 
                         } , 
 
                         " s t a n d a r d _ t r a i n i n g " :   { 
 
                                 " b a s e _ m o d e l " :   " m i c r o s o f t / D i a l o G P T - s m a l l " ,     
 
                                 " l o r a " :   { " r " :   1 6 ,   " a l p h a " :   3 2 ,   " d r o p o u t " :   0 . 1 ,   " t a r g e t _ m o d u l e s " :   [ " c _ a t t n " ,   " c _ p r o j " ] } , 
 
                                 " t r a i n i n g " :   { " e p o c h s " :   2 ,   " b a t c h _ s i z e " :   4 ,   " l e a r n i n g _ r a t e " :   0 . 0 0 0 2 ,   " m a x _ s t e p s " :   5 0 0 } 
 
                         } , 
 
                         " i n t e n s i v e _ t r a i n i n g " :   { 
 
                                 " b a s e _ m o d e l " :   " m i c r o s o f t / D i a l o G P T - s m a l l " , 
 
                                 " l o r a " :   { " r " :   3 2 ,   " a l p h a " :   6 4 ,   " d r o p o u t " :   0 . 1 ,   " t a r g e t _ m o d u l e s " :   [ " c _ a t t n " ,   " c _ p r o j " ,   " c _ f c " ] } , 
 
                                 " t r a i n i n g " :   { " e p o c h s " :   5 ,   " b a t c h _ s i z e " :   8 ,   " l e a r n i n g _ r a t e " :   0 . 0 0 0 3 ,   " m a x _ s t e p s " :   1 0 0 0 } 
 
                         } 
 
                 } 
 
                 
 
                 r e t u r n   p r e s e t s . g e t ( p r e s e t _ n a m e ,   p r e s e t s [ " s t a n d a r d _ t r a i n i n g " ] ) 
 
         
 
         d e f   _ e s t i m a t e _ t r a i n i n g _ d u r a t i o n ( s e l f ,   p r e s e t :   s t r )   - >   s t r : 
 
                 " " " E s t i m a t e   t r a i n i n g   d u r a t i o n   b a s e d   o n   p r e s e t " " " 
 
                 e s t i m a t e s   =   { 
 
                         " q u i c k _ t r a i n i n g " :   " 5 - 1 5   m i n u t e s " , 
 
                         " s t a n d a r d _ t r a i n i n g " :   " 1 5 - 4 5   m i n u t e s " ,   
 
                         " i n t e n s i v e _ t r a i n i n g " :   " 1 - 3   h o u r s " 
 
                 } 
 
                 r e t u r n   e s t i m a t e s . g e t ( p r e s e t ,   " 1 5 - 4 5   m i n u t e s " ) 
 
         
 
         d e f   _ g e t _ f a l l b a c k _ c o n f i g ( s e l f )   - >   D i c t [ s t r ,   A n y ] : 
 
                 " " " R o b u s t   f a l l b a c k   c o n f i g u r a t i o n " " " 
 
                 r e t u r n   { 
 
                         " b a s e _ m o d e l " :   " m i c r o s o f t / D i a l o G P T - s m a l l " , 
 
                         " l o r a " :   { 
 
                                 " r " :   1 6 , 
 
                                 " a l p h a " :   3 2 , 
 
                                 " d r o p o u t " :   0 . 1 , 
 
                                 " t a r g e t _ m o d u l e s " :   [ " c _ a t t n " ,   " c _ p r o j " ] 
 
                         } , 
 
                         " t r a i n i n g " :   { 
 
                                 " e p o c h s " :   2 , 
 
                                 " b a t c h _ s i z e " :   2 , 
 
                                 " l e a r n i n g _ r a t e " :   0 . 0 0 0 1 , 
 
                                 " g r a d i e n t _ a c c u m u l a t i o n _ s t e p s " :   4 , 
 
                                 " w a r m u p _ s t e p s " :   5 0 , 
 
                                 " m a x _ s t e p s " :   5 0 0 , 
 
                                 " s a v e _ s t e p s " :   1 0 0 , 
 
                                 " l o g g i n g _ s t e p s " :   1 0 
 
                         } , 
 
                         " o u t p u t _ d i r " :   " l o r a _ a d a p t e r s _ m i s t r a l / o r c h e s t r a t o r _ a d a p t e r " 
 
                 } 
 
         
 
         d e f   _ v a l i d a t e _ a n d _ c o m p l e t e _ c o n f i g ( s e l f ,   c o n f i g :   D i c t [ s t r ,   A n y ] )   - >   D i c t [ s t r ,   A n y ] : 
 
                 " " " V a l i d a t e   a n d   c o m p l e t e   t r a i n i n g   c o n f i g u r a t i o n " " " 
 
                 #   E n s u r e   r e q u i r e d   s e c t i o n s   e x i s t 
 
                 i f   " b a s e _ m o d e l "   n o t   i n   c o n f i g : 
 
                         c o n f i g [ " b a s e _ m o d e l " ]   =   " m i c r o s o f t / D i a l o G P T - s m a l l " 
 
                         
 
                 i f   " l o r a "   n o t   i n   c o n f i g : 
 
                         c o n f i g [ " l o r a " ]   =   { " r " :   1 6 ,   " a l p h a " :   3 2 ,   " d r o p o u t " :   0 . 1 ,   " t a r g e t _ m o d u l e s " :   [ " c _ a t t n " ] } 
 
                         
 
                 i f   " t r a i n i n g "   n o t   i n   c o n f i g : 
 
                         c o n f i g [ " t r a i n i n g " ]   =   { " e p o c h s " :   3 ,   " b a t c h _ s i z e " :   4 ,   " l e a r n i n g _ r a t e " :   0 . 0 0 0 2 } 
 
                         
 
                 i f   " o u t p u t _ d i r "   n o t   i n   c o n f i g : 
 
                         c o n f i g [ " o u t p u t _ d i r " ]   =   " l o r a _ a d a p t e r s _ m i s t r a l / v a l i d a t e d _ a d a p t e r " 
 
                         
 
                 r e t u r n   c o n f i g 
 
         
 
         d e f   _ v a l i d a t e _ t r a i n i n g _ c o n f i g ( s e l f ,   c o n f i g :   D i c t [ s t r ,   A n y ] )   - >   b o o l : 
 
                 " " " V a l i d a t e   t r a i n i n g   c o n f i g u r a t i o n " " " 
 
                 r e q u i r e d _ f i e l d s   =   [ " b a s e _ m o d e l " ,   " l o r a " ,   " t r a i n i n g " ,   " o u t p u t _ d i r " ] 
 
                 m i s s i n g _ f i e l d s   =   [ f i e l d   f o r   f i e l d   i n   r e q u i r e d _ f i e l d s   i f   f i e l d   n o t   i n   c o n f i g ] 
 
                 
 
                 i f   m i s s i n g _ f i e l d s : 
 
                         l o g g e r . e r r o r ( f " L'  M i s s i n g   r e q u i r e d   t r a i n i n g   c o n f i g   f i e l d s :   { m i s s i n g _ f i e l d s } " ) 
 
                         r e t u r n   F a l s e 
 
                         
 
                 r e t u r n   T r u e 
 
         
 
         @ p r o p e r t y 
 
         d e f   i s _ t r a i n i n g _ r u n n i n g ( s e l f )   - >   b o o l : 
 
                 " " " C h e c k   i f   t r a i n i n g   i s   c u r r e n t l y   r u n n i n g " " " 
 
                 r e t u r n   s e l f . i s _ t r a i n i n g _ a c t i v e 
 
         
 
         #   =؀  P h a s e   3 :   F I R E   E s t i m a t i o n   a n d   T r a i n i n g   H i s t o r y   M e t h o d s 
 
         
 
         d e f   _ c r e a t e _ t r a i n i n g _ r e c o r d ( s e l f ,   t r a i n i n g _ p a r a m s :   D i c t [ s t r ,   A n y ] )   - >   s t r : 
 
                 " " " C r e a t e   t r a i n i n g   h i s t o r y   r e c o r d   i n   d a t a b a s e " " " 
 
                 t r y : 
 
                         i f   n o t   s e l f . t r a i n i n g _ h i s t o r y _ s t o r a g e : 
 
                                 f r o m   . q u e s t i o n _ h i s t o r y _ s t o r a g e   i m p o r t   Q u e s t i o n H i s t o r y S t o r a g e 
 
                                 s e l f . t r a i n i n g _ h i s t o r y _ s t o r a g e   =   Q u e s t i o n H i s t o r y S t o r a g e ( ) 
 
                         
 
                         r u n _ i d   =   s e l f . t r a i n i n g _ h i s t o r y _ s t o r a g e . c r e a t e _ t r a i n i n g _ r u n ( t r a i n i n g _ p a r a m s ) 
 
                         l o g g e r . i n f o ( f " =  C r e a t e d   t r a i n i n g   r e c o r d :   { r u n _ i d } " ) 
 
                         r e t u r n   r u n _ i d 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  F a i l e d   t o   c r e a t e   t r a i n i n g   r e c o r d :   { e } " ) 
 
                         r e t u r n   f " e r r o r _ { i n t ( t i m e . t i m e ( ) ) } " 
 
         
 
         d e f   _ s t a r t _ f i r e _ e s t i m a t i o n _ a n d _ p r o c e s s i n g ( s e l f ,   t r a i n i n g _ p a r a m s :   D i c t [ s t r ,   A n y ] ) : 
 
                 " " " 
 
                 =%  P h a s e   3 :   S t a r t   F I R E   e s t i m a t i o n   b e f o r e   d o c u m e n t   p r o c e s s i n g 
 
                 
 
                 T h i s   p r o v i d e s   t h e   u s e r   w i t h   t r a i n i n g   t i m e   e s t i m a t e s   b e f o r e   c o m m i t t i n g   t o   t r a i n i n g . 
 
                 " " " 
 
                 t r y : 
 
                         l o g g e r . i n f o ( " =%  S t a r t i n g   F I R E   e s t i m a t i o n   a n d   d o c u m e n t   a n a l y s i s . . . " ) 
 
                         
 
                         #   S t a r t   F I R E   e s t i m a t i o n   i n   b a c k g r o u n d   t h r e a d 
 
                         d e f   f i r e _ e s t i m a t i o n _ w o r k e r ( ) : 
 
                                 t r y : 
 
                                         i m p o r t   l o g g i n g 
 
                                         f i r e _ l o g g e r   =   l o g g i n g . g e t L o g g e r ( f " { _ _ n a m e _ _ } . f i r e _ w o r k e r " ) 
 
                                         
 
                                         #   I n i t i a l i z e   F I R E   e s t i m a t o r 
 
                                         f r o m   . f i r e _ v 2 1 _ e s t i m a t o r   i m p o r t   F I R E v 2 1 E s t i m a t o r 
 
                                         s e l f . f i r e _ e s t i m a t o r   =   F I R E v 2 1 E s t i m a t o r ( ) 
 
                                         
 
                                         #   A n a l y z e   s e l e c t e d   f i l e s   f o r   e s t i m a t i o n 
 
                                         s e l e c t e d _ f i l e s   =   t r a i n i n g _ p a r a m s . g e t ( ' s e l e c t e d _ f i l e s ' ,   [ ] ) 
 
                                         p r e s e t   =   t r a i n i n g _ p a r a m s . g e t ( ' t r a i n i n g _ p r e s e t ' ,   ' s t a n d a r d _ t r a i n i n g ' ) 
 
                                         
 
                                         #   C a l c u l a t e   d a t a s e t   s i z e   e s t i m a t e 
 
                                         t o t a l _ s i z e _ m b   =   0 
 
                                         f i l e _ c o u n t   =   0 
 
                                         
 
                                         i f   s e l e c t e d _ f i l e s : 
 
                                                 f o r   f i l e _ n a m e   i n   s e l e c t e d _ f i l e s : 
 
                                                         #   C h e c k   b o t h   p o s s i b l e   d i r e c t o r i e s 
 
                                                         f i l e _ p a t h s   =   [ 
 
                                                                 P a t h ( " u p l o a d e d _ b o o k s " )   /   f i l e _ n a m e , 
 
                                                                 P a t h ( " d a t a / u p l o a d e d _ b o o k s " )   /   f i l e _ n a m e 
 
                                                         ] 
 
                                                         
 
                                                         f o r   f i l e _ p a t h   i n   f i l e _ p a t h s : 
 
                                                                 i f   f i l e _ p a t h . e x i s t s ( ) : 
 
                                                                         t o t a l _ s i z e _ m b   + =   f i l e _ p a t h . s t a t ( ) . s t _ s i z e   /   ( 1 0 2 4   *   1 0 2 4 ) 
 
                                                                         f i l e _ c o u n t   + =   1 
 
                                                                         b r e a k 
 
                                         e l s e : 
 
                                                 #   U s e   a l l   a v a i l a b l e   f i l e s 
 
                                                 f o r   b o o k s _ d i r   i n   [ P a t h ( " u p l o a d e d _ b o o k s " ) ,   P a t h ( " d a t a / u p l o a d e d _ b o o k s " ) ] : 
 
                                                         i f   b o o k s _ d i r . e x i s t s ( ) : 
 
                                                                 f o r   f i l e _ p a t h   i n   b o o k s _ d i r . g l o b ( " * . p d f " ) : 
 
                                                                         t o t a l _ s i z e _ m b   + =   f i l e _ p a t h . s t a t ( ) . s t _ s i z e   /   ( 1 0 2 4   *   1 0 2 4 ) 
 
                                                                         f i l e _ c o u n t   + =   1 
 
                                                                 f o r   f i l e _ p a t h   i n   b o o k s _ d i r . g l o b ( " * . t x t " ) : 
 
                                                                         t o t a l _ s i z e _ m b   + =   f i l e _ p a t h . s t a t ( ) . s t _ s i z e   /   ( 1 0 2 4   *   1 0 2 4 ) 
 
                                                                         f i l e _ c o u n t   + =   1 
 
                                         
 
                                         #   C r e a t e   e s t i m a t i o n   c o n f i g   f o r   F I R E 
 
                                         e s t i m a t i o n _ c o n f i g   =   { 
 
                                                 " t o t a l _ s i z e _ m b " :   t o t a l _ s i z e _ m b , 
 
                                                 " f i l e _ c o u n t " :   f i l e _ c o u n t , 
 
                                                 " p r e s e t " :   p r e s e t , 
 
                                                 " b a s e _ m o d e l " :   t r a i n i n g _ p a r a m s . g e t ( ' b a s e _ m o d e l ' ,   ' m i c r o s o f t / D i a l o G P T - s m a l l ' ) 
 
                                         } 
 
                                         
 
                                         #   G e t   F I R E   p r e d i c t i o n   u s i n g   c o n f i g u r a t i o n - b a s e d   e s t i m a t i o n 
 
                                         f i r e _ l o g g e r . i n f o ( f " =%  A n a l y z i n g   { f i l e _ c o u n t }   f i l e s   ( { t o t a l _ s i z e _ m b : . 1 f }   M B )   w i t h   p r e s e t   ' { p r e s e t } ' " ) 
 
                                         
 
                                         #   C a l c u l a t e   e s t i m a t e   b a s e d   o n   p r e s e t   a n d   d a t a   s i z e 
 
                                         b a s e _ t i m e _ h o u r s   =   0 . 5     #   B a s e   t r a i n i n g   t i m e 
 
                                         
 
                                         #   A d j u s t   b a s e d   o n   p r e s e t 
 
                                         i f   p r e s e t   = =   " a g g r e s s i v e _ t r a i n i n g " : 
 
                                                 t i m e _ m u l t i p l i e r   =   3 . 0 
 
                                         e l i f   p r e s e t   = =   " s t a n d a r d _ t r a i n i n g " : 
 
                                                 t i m e _ m u l t i p l i e r   =   2 . 0 
 
                                         e l s e :     #   c o n s e r v a t i v e 
 
                                                 t i m e _ m u l t i p l i e r   =   1 . 0 
 
                                         
 
                                         #   A d j u s t   b a s e d   o n   d a t a   s i z e 
 
                                         i f   t o t a l _ s i z e _ m b   >   1 0 0 : 
 
                                                 t i m e _ m u l t i p l i e r   * =   2 . 0 
 
                                         e l i f   t o t a l _ s i z e _ m b   >   5 0 : 
 
                                                 t i m e _ m u l t i p l i e r   * =   1 . 5 
 
                                         e l i f   t o t a l _ s i z e _ m b   >   1 0 : 
 
                                                 t i m e _ m u l t i p l i e r   * =   1 . 2 
 
                                         
 
                                         e s t i m a t e d _ h o u r s   =   b a s e _ t i m e _ h o u r s   *   t i m e _ m u l t i p l i e r 
 
                                         
 
                                         f i r e _ l o g g e r . i n f o ( f " =%  F I R E   E s t i m a t i o n :   ~ { e s t i m a t e d _ h o u r s : . 1 f }   h o u r s   f o r   t r a i n i n g " ) 
 
                                         
 
                                         #   U p d a t e   t r a i n i n g   r e c o r d   w i t h   F I R E   e s t i m a t e 
 
                                         i f   s e l f . c u r r e n t _ t r a i n i n g _ r u n _ i d   a n d   s e l f . t r a i n i n g _ h i s t o r y _ s t o r a g e : 
 
                                                 s e l f . t r a i n i n g _ h i s t o r y _ s t o r a g e . u p d a t e _ t r a i n i n g _ r u n _ f i r e _ e s t i m a t e ( 
 
                                                         s e l f . c u r r e n t _ t r a i n i n g _ r u n _ i d ,   e s t i m a t e d _ h o u r s 
 
                                                 ) 
 
                                         
 
                                         #   E m i t   F I R E   e s t i m a t i o n   r e s u l t   t o   U I 
 
                                         Q M e t a O b j e c t . i n v o k e M e t h o d ( 
 
                                                 s e l f ,   " _ o n _ f i r e _ e s t i m a t i o n _ c o m p l e t e " , 
 
                                                 Q t . Q u e u e d C o n n e c t i o n , 
 
                                                 Q _ A R G ( s t r ,   j s o n . d u m p s ( { 
 
                                                         " e s t i m a t e d _ h o u r s " :   e s t i m a t e d _ h o u r s , 
 
                                                         " t o t a l _ s i z e _ m b " :   t o t a l _ s i z e _ m b , 
 
                                                         " f i l e _ c o u n t " :   f i l e _ c o u n t , 
 
                                                         " p r e s e t " :   p r e s e t 
 
                                                 } ) ) 
 
                                         ) 
 
                                         
 
                                         #   C o n t i n u e   w i t h   d o c u m e n t   p r o c e s s i n g 
 
                                         t i m e . s l e e p ( 1 )     #   B r i e f   p a u s e   t o   s h o w   e s t i m a t e 
 
                                         s e l f . _ s t a r t _ d o c u m e n t _ p r o c e s s i n g ( t r a i n i n g _ p a r a m s ) 
 
                                         
 
                                 e x c e p t   E x c e p t i o n   a s   e : 
 
                                         f i r e _ l o g g e r . e r r o r ( f " L'  F I R E   e s t i m a t i o n   f a i l e d :   { e } " ) 
 
                                         #   C o n t i n u e   w i t h   t r a i n i n g   a n y w a y 
 
                                         Q M e t a O b j e c t . i n v o k e M e t h o d ( 
 
                                                 s e l f ,   " _ o n _ f i r e _ e s t i m a t i o n _ e r r o r " , 
 
                                                 Q t . Q u e u e d C o n n e c t i o n , 
 
                                                 Q _ A R G ( s t r ,   s t r ( e ) ) 
 
                                         ) 
 
                                         s e l f . _ s t a r t _ d o c u m e n t _ p r o c e s s i n g ( t r a i n i n g _ p a r a m s ) 
 
                         
 
                         #   S t a r t   F I R E   w o r k e r   t h r e a d 
 
                         t h r e a d   =   t h r e a d i n g . T h r e a d ( t a r g e t = f i r e _ e s t i m a t i o n _ w o r k e r ,   d a e m o n = T r u e ) 
 
                         t h r e a d . s t a r t ( ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  F a i l e d   t o   s t a r t   F I R E   e s t i m a t i o n :   { e } " ) 
 
                         #   F a l l b a c k   t o   n o r m a l   p r o c e s s i n g 
 
                         s e l f . _ s t a r t _ d o c u m e n t _ p r o c e s s i n g ( t r a i n i n g _ p a r a m s ) 
 
         
 
         @ p y q t S l o t ( s t r ) 
 
         d e f   _ o n _ f i r e _ e s t i m a t i o n _ c o m p l e t e ( s e l f ,   e s t i m a t i o n _ d a t a :   s t r ) : 
 
                 " " " H a n d l e   F I R E   e s t i m a t i o n   c o m p l e t i o n " " " 
 
                 t r y : 
 
                         d a t a   =   j s o n . l o a d s ( e s t i m a t i o n _ d a t a ) 
 
                         e s t i m a t e d _ h o u r s   =   d a t a [ " e s t i m a t e d _ h o u r s " ] 
 
                         
 
                         l o g g e r . i n f o ( f " =%  F I R E   e s t i m a t i o n   c o m p l e t e :   ~ { e s t i m a t e d _ h o u r s : . 1 f }   h o u r s " ) 
 
                         
 
                         #   F o r m a t   d u r a t i o n   f o r   d i s p l a y 
 
                         i f   e s t i m a t e d _ h o u r s   <   1 : 
 
                                 d u r a t i o n _ t e x t   =   f " ~ { i n t ( e s t i m a t e d _ h o u r s   *   6 0 ) }   m i n u t e s " 
 
                         e l s e : 
 
                                 d u r a t i o n _ t e x t   =   f " ~ { e s t i m a t e d _ h o u r s : . 1 f }   h o u r s " 
 
                         
 
                         #   U p d a t e   U I   w i t h   F I R E   e s t i m a t e 
 
                         p r o g r e s s _ i n f o   =   { 
 
                                 " s t a t u s " :   " f i r e _ e s t i m a t i o n _ c o m p l e t e " , 
 
                                 " m e s s a g e " :   f " =%  F I R E   A n a l y s i s :   E s t i m a t e d   t r a i n i n g   t i m e   { d u r a t i o n _ t e x t } " , 
 
                                 " p r o g r e s s _ p e r c e n t " :   5 , 
 
                                 " p h a s e " :   " f i r e _ e s t i m a t i o n " , 
 
                                 " e s t i m a t e d _ d u r a t i o n " :   d u r a t i o n _ t e x t , 
 
                                 " f i r e _ d a t a " :   d a t a , 
 
                                 " c a n _ c a n c e l " :   T r u e , 
 
                                 " t i m e s t a m p " :   t i m e . t i m e ( ) 
 
                         } 
 
                         s e l f . p r o g r e s s _ u p d a t e . e m i t ( p r o g r e s s _ i n f o ) 
 
                         
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         l o g g e r . e r r o r ( f " L'  E r r o r   h a n d l i n g   F I R E   e s t i m a t i o n :   { e } " ) 
 
         
 
         @ p y q t S l o t ( s t r ) 
 
         d e f   _ o n _ f i r e _ e s t i m a t i o n _ e r r o r ( s e l f ,   e r r o r _ m e s s a g e :   s t r ) : 
 
                 " " " H a n d l e   F I R E   e s t i m a t i o n   e r r o r s " " " 
 
                 l o g g e r . w a r n i n g ( f " &  F I R E   e s t i m a t i o n   e r r o r :   { e r r o r _ m e s s a g e } " ) 
 
                 
 
                 #   C o n t i n u e   w i t h   f a l l b a c k   e s t i m a t e 
 
                 p r o g r e s s _ i n f o   =   { 
 
                         " s t a t u s " :   " f i r e _ e s t i m a t i o n _ f a l l b a c k " , 
 
                         " m e s s a g e " :   " &  U s i n g   f a l l b a c k   t i m e   e s t i m a t i o n " , 
 
                         " p r o g r e s s _ p e r c e n t " :   5 , 
 
                         " p h a s e " :   " f i r e _ e s t i m a t i o n " , 
 
                         " e s t i m a t e d _ d u r a t i o n " :   " ~ 2   h o u r s   ( f a l l b a c k ) " , 
 
                         " c a n _ c a n c e l " :   T r u e , 
 
                         " t i m e s t a m p " :   t i m e . t i m e ( ) 
 
                 } 
 
                 s e l f . p r o g r e s s _ u p d a t e . e m i t ( p r o g r e s s _ i n f o ) 
 
 