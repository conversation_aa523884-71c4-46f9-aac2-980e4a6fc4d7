#!/usr/bin/env python3
"""
Simple test to verify Lo<PERSON> is disabled and normal Ollama models work
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_simple_topic_generation():
    """Test simple topic generation without LoRA"""
    print("🚫 NO-LORA SIMPLE TEST")
    print("=" * 50)
    
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        mcq_manager = get_mcq_manager()
        print("✅ MCQ Manager initialized")
        
        # Test with simple topic
        test_topic = "AI"
        
        quiz_params = {
            "topic": test_topic,
            "difficulty": "medium",
            "game_mode": "casual",
            "submode": "mixed",
            "num_questions": 1,
            "mode": "auto"
        }
        
        print(f"🧪 Testing simple generation for: '{test_topic}'")
        print("This should:")
        print("1. Skip LoRA adapter checks")
        print("2. Use normal Ollama models")
        print("3. Generate question successfully")
        print()
        
        # Generate quiz
        result = mcq_manager.generate_quiz(quiz_params)
        
        if result and hasattr(result, 'question'):
            print("✅ SUCCESS: Question generated without LoRA!")
            print(f"   Question: {result.question[:100]}...")
            print(f"   Options: {len(result.options)} choices")
            return True
        else:
            print("❌ FAILED: No question generated")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ollama_direct():
    """Test Ollama connection directly"""
    print("\n🔗 OLLAMA DIRECT CONNECTION TEST")
    print("=" * 50)
    
    try:
        import requests
        
        # Test Ollama connection
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = [m['name'] for m in response.json().get('models', [])]
            print(f"✅ Ollama running with {len(models)} models")
            print(f"   Available models: {models[:5]}")
            return True
        else:
            print(f"❌ Ollama not responding: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        return False

def test_semantic_mapping_simple():
    """Test semantic mapping without LoRA"""
    print("\n🧠 SEMANTIC MAPPING (NO LORA) TEST")
    print("=" * 50)
    
    try:
        from knowledge_app.core.intelligent_semantic_mapper import get_semantic_mapper
        
        mapper = get_semantic_mapper()
        print("✅ Semantic mapper initialized")
        
        # Test simple mapping
        test_input = "AI"
        result = mapper.map_topic_semantically(test_input)
        
        print(f"✅ Semantic mapping result:")
        print(f"   Original: {result.original_input}")
        print(f"   Expanded: {result.expanded_topic}")
        print(f"   Type: {result.question_type}")
        print(f"   Instructions: {result.reasoning[:80]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Semantic mapping failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run simple no-LoRA tests"""
    print("🚫 NO-LORA TEST SUITE")
    print("=" * 60)
    
    tests = [
        ("Ollama Direct Connection", test_ollama_direct),
        ("Semantic Mapping (No LoRA)", test_semantic_mapping_simple),
        ("Simple Topic Generation", test_simple_topic_generation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🚀 Starting: {test_name}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 LoRA disabled successfully! Using normal Ollama models.")
    else:
        print("⚠️ Some issues remain. Check output above.")

if __name__ == "__main__":
    main()
