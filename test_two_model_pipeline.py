#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator
import json

def test_two_model_pipeline():
    print("🧪 Testing Two-Model Pipeline (DeepSeek R1 → Llama)")
    print("=" * 60)
    
    # Initialize generator
    generator = OllamaJSONGenerator()
    if not generator.initialize():
        print("❌ Failed to initialize generator!")
        return
    
    # Test parameters
    topic = "atoms"
    difficulty = "expert" 
    question_type = "numerical"
    
    print(f"📝 Topic: {topic}")
    print(f"🎯 Difficulty: {difficulty}")
    print(f"🔢 Type: {question_type}")
    print()
    
    # Generate question
    print("🚀 Starting two-model pipeline...")
    print("   Step 1: DeepSeek R1 generates complex content")
    print("   Step 2: Llama formats into clean JSON")
    print()
    
    questions = generator.generate_mcq(
        topic=topic,
        context="",
        num_questions=1,
        difficulty=difficulty,
        game_mode="quiz",
        question_type=question_type
    )
    
    if questions:
        print("✅ Two-model pipeline successful!")
        print()
        
        question = questions[0]
        print("📋 Generated Question:")
        print("-" * 40)
        print(f"Question: {question.get('question', 'N/A')}")
        print()
        
        options = question.get('options', [])
        if isinstance(options, list):
            for i, option in enumerate(options):
                letter = chr(65 + i)  # A, B, C, D
                print(f"{letter}. {option}")
        elif isinstance(options, dict):
            for key, value in options.items():
                print(f"{key}. {value}")
        
        print()
        print(f"Correct Answer: {question.get('correct_answer', 'N/A')}")
        print(f"Explanation: {question.get('explanation', 'N/A')}")
        
        # Analysis
        print()
        print("🔍 Pipeline Analysis:")
        print("-" * 40)
        
        question_text = question.get('question', '').lower()
        
        # Check for numerical content
        numerical_terms = ['calculate', 'number', 'value', 'formula', 'equation', 'units', 'mass', 'energy', 'wavelength', 'frequency', 'joules', 'nm', 'ev', 'constant']
        numerical_found = [term for term in numerical_terms if term in question_text]
        
        # Check for expert-level content  
        expert_terms = ['quantum', 'relativistic', 'orbital', 'electron', 'nuclear', 'atomic', 'molecular', 'spectroscopy', 'ionization', 'excitation', 'rydberg', 'fine structure', 'hyperfine']
        expert_found = [term for term in expert_terms if term in question_text]
        
        # Check for multiple choice options
        has_options = len(options) == 4 if isinstance(options, list) else len(options) == 4
        
        print(f"🔢 Numerical content: {len(numerical_found) > 0} - Found: {numerical_found}")
        print(f"🎓 Expert-level content: {len(expert_found) > 0} - Found: {expert_found}")
        print(f"📝 Multiple choice format: {has_options} - Count: {len(options)}")
        print(f"✅ Has correct answer: {question.get('correct_answer') is not None}")
        print(f"💡 Has explanation: {question.get('explanation') is not None}")
        
        # Check if this is the old basic question
        if "ground state electron configuration" in question_text:
            print("⚠️  WARNING: Still getting basic electron configuration question!")
            print("   This suggests the two-model pipeline may not be working")
        else:
            print("🎉 SUCCESS: Got a different question - pipeline working!")
        
    else:
        print("❌ Two-model pipeline failed!")
        
    print()
    print("🧪 Pipeline test completed!")

if __name__ == "__main__":
    test_two_model_pipeline()
