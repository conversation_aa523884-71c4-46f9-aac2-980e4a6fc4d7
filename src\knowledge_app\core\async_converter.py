#!/usr/bin/env python3
"""
🚀 ASYNC CONVERTER - Convert ALL blocking operations to non-blocking

This module provides async wrappers for every blocking operation in the codebase
to completely eliminate UI freezing.
"""


# Async converter functions will be defined below


import asyncio
import aiohttp
import aiofiles
import logging
import time
import json
import threading
from typing import Optional, Dict, Any, Callable, Union
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
import subprocess

logger = logging.getLogger(__name__)

class AsyncConverter:
    """
    🚀 Universal async converter for all blocking operations
    
    This class provides async versions of every blocking operation:
    - HTTP requests (requests.post/get)
    - File I/O (open, read, write)
    - Time delays (time.sleep)
    - Subprocess calls
    - Future waits
    """
    
    def __init__(self):
        # Thread pool for CPU-bound operations
        self._executor = ThreadPoolExecutor(
            max_workers=4,
            thread_name_prefix="AsyncConverter"
        )
        
        # HTTP session for async requests
        self._session: Optional[aiohttp.ClientSession] = None
        self._session_lock = threading.Lock()
        
        logger.info("🚀 AsyncConverter initialized")
    
    async def get_session(self) -> aiohttp.ClientSession:
        """Get or create async HTTP session"""
        if self._session is None or self._session.closed:
            with self._session_lock:
                if self._session is None or self._session.closed:
                    timeout = aiohttp.ClientTimeout(
                        total=60,      # 60 seconds total
                        connect=5,     # 5 seconds to connect
                        sock_read=30   # 30 seconds to read
                    )
                    self._session = aiohttp.ClientSession(timeout=timeout)
                    logger.info("✅ Async HTTP session created")
        return self._session
    
    async def async_post(self, url: str, json_data: Dict[str, Any] = None, 
                        timeout: float = 60) -> Optional[Dict[str, Any]]:
        """
        🚀 Async HTTP POST request - NEVER blocks UI
        
        Args:
            url: Request URL
            json_data: JSON payload
            timeout: Request timeout
            
        Returns:
            Response JSON or None if failed
        """
        try:
            session = await self.get_session()
            
            async with session.post(url, json=json_data) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    logger.error(f"❌ HTTP POST failed: {response.status}")
                    return None
                    
        except asyncio.TimeoutError:
            logger.error(f"❌ HTTP POST timeout: {url}")
            return None
        except Exception as e:
            logger.error(f"❌ HTTP POST error: {e}")
            return None
    
    async def async_get(self, url: str, timeout: float = 30) -> Optional[Dict[str, Any]]:
        """
        🚀 Async HTTP GET request - NEVER blocks UI
        
        Args:
            url: Request URL
            timeout: Request timeout
            
        Returns:
            Response JSON or None if failed
        """
        try:
            session = await self.get_session()
            
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    logger.error(f"❌ HTTP GET failed: {response.status}")
                    return None
                    
        except asyncio.TimeoutError:
            logger.error(f"❌ HTTP GET timeout: {url}")
            return None
        except Exception as e:
            logger.error(f"❌ HTTP GET error: {e}")
            return None
    
    async def async_time_sleep(self, seconds: float):
        """
        🚀 Async sleep - NEVER blocks UI

        Args:
            seconds: Sleep duration
        """
        await asyncio.sleep(seconds)
    
    async def async_file_read(self, file_path: Union[str, Path]) -> Optional[str]:
        """
        🚀 Async file read - NEVER blocks UI
        
        Args:
            file_path: Path to file
            
        Returns:
            File content or None if failed
        """
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                return content
        except Exception as e:
            logger.error(f"❌ Async file read failed: {e}")
            return None
    
    async def async_file_write(self, file_path: Union[str, Path], content: str) -> bool:
        """
        🚀 Async file write - NEVER blocks UI
        
        Args:
            file_path: Path to file
            content: Content to write
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure directory exists
            path = Path(file_path)
            path.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write(content)
                return True
        except Exception as e:
            logger.error(f"❌ Async file write failed: {e}")
            return False
    
    async def async_subprocess(self, command: list, timeout: float = 30) -> Optional[str]:
        """
        🚀 Async subprocess call - NEVER blocks UI
        
        Args:
            command: Command to execute
            timeout: Execution timeout
            
        Returns:
            Command output or None if failed
        """
        try:
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), 
                timeout=timeout
            )
            
            if process.returncode == 0:
                return stdout.decode('utf-8')
            else:
                logger.error(f"❌ Subprocess failed: {stderr.decode('utf-8')}")
                return None
                
        except asyncio.TimeoutError:
            logger.error(f"❌ Subprocess timeout: {command}")
            return None
        except Exception as e:
            logger.error(f"❌ Subprocess error: {e}")
            return None
    
    def sync_to_async(self, sync_func: Callable, *args, **kwargs):
        """
        🚀 Convert any synchronous function to async - NEVER blocks UI
        
        Args:
            sync_func: Synchronous function to convert
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Async version of the function
        """
        async def async_wrapper():
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                self._executor,
                lambda: sync_func(*args, **kwargs)
            )
        return async_wrapper()
    
    async def async_ollama_request(self, url: str, payload: Dict[str, Any], 
                                  timeout: float = 60) -> Optional[str]:
        """
        🚀 Async Ollama request with streaming support - NEVER blocks UI
        
        Args:
            url: Ollama API URL
            payload: Request payload
            timeout: Request timeout
            
        Returns:
            Generated text or None if failed
        """
        try:
            session = await self.get_session()
            
            start_time = time.time()
            logger.info(f"🚀 Starting async Ollama request...")
            
            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    result = data.get("response", "")
                    
                    elapsed = time.time() - start_time
                    logger.info(f"✅ Async Ollama request completed in {elapsed:.1f}s")
                    return result
                else:
                    logger.error(f"❌ Ollama request failed: {response.status}")
                    return None
                    
        except asyncio.TimeoutError:
            logger.error(f"❌ Ollama request timeout after {timeout}s")
            return None
        except Exception as e:
            logger.error(f"❌ Ollama request error: {e}")
            return None
    
    async def cleanup(self):
        """Clean up resources"""
        try:
            if self._session and not self._session.closed:
                await self._session.close()
            
            if self._executor:
                self._executor.shutdown(wait=False)
            
            logger.info("✅ AsyncConverter cleaned up")
            
        except Exception as e:
            logger.error(f"❌ AsyncConverter cleanup error: {e}")


# Global instance
_async_converter: Optional[AsyncConverter] = None
_converter_lock = threading.Lock()


def get_async_converter() -> AsyncConverter:
    """Get the global async converter instance"""
    global _async_converter
    
    if _async_converter is None:
        with _converter_lock:
            if _async_converter is None:
                _async_converter = AsyncConverter()
    
    return _async_converter


async def cleanup_async_converter():
    """Clean up the global converter"""
    global _async_converter

    if _async_converter is not None:
        await _async_converter.cleanup()
        _async_converter = None


# 🚀 CONVENIENCE FUNCTIONS - Direct replacements for blocking operations

async def async_requests_post(url: str, json_data: Dict[str, Any] = None,
                             timeout: float = 60) -> Optional[Dict[str, Any]]:
    """🚀 Direct replacement for await async_requests_post() - NEVER blocks UI"""
    converter = get_async_converter()
    return await converter.async_post(url, json_data, timeout)


async def async_requests_get(url: str, timeout: float = 30) -> Optional[Dict[str, Any]]:
    """🚀 Direct replacement for await async_requests_get() - NEVER blocks UI"""
    converter = get_async_converter()
    return await converter.async_get(url, timeout)


async def async_time_sleep(seconds: float):
    """🚀 Direct replacement for time.sleep() - NEVER blocks UI"""
    await asyncio.sleep(seconds)


async def async_subprocess(command: list, **kwargs):
    """🚀 Direct replacement for subprocess calls - NEVER blocks UI"""
    converter = get_async_converter()
    return await converter.async_subprocess(command, **kwargs)


async def async_file_read(file_path: Union[str, Path]) -> Optional[str]:
    """🚀 Direct replacement for open().read() - NEVER blocks UI"""
    converter = get_async_converter()
    return await converter.async_file_read(file_path)


async def async_file_write(file_path: Union[str, Path], content: str) -> bool:
    """🚀 Direct replacement for open().write() - NEVER blocks UI"""
    converter = get_async_converter()
    return await converter.async_file_write(file_path, content)


def make_async(sync_func: Callable):
    """
    🚀 Decorator to convert any synchronous function to async - NEVER blocks UI

    Usage:
        @make_async
        def blocking_function():
            await async_time_sleep(5)  # This will become non-blocking
            return "done"

        # Use as: result = await blocking_function()
    """
    async def async_wrapper(*args, **kwargs):
        converter = get_async_converter()
        return await converter.sync_to_async(sync_func, *args, **kwargs)

    return async_wrapper


class AsyncContextManager:
    """
    🚀 Context manager to ensure all operations within a block are async

    Usage:
        async with AsyncContextManager():
            # All operations here will be non-blocking
            result = await async_requests_post(url, data)
            await async_time_sleep(1)
            content = await async_file_read("file.txt")
    """

    def __init__(self):
        self.converter = get_async_converter()

    async def __aenter__(self):
        return self.converter

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Cleanup if needed
        pass
