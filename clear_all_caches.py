#!/usr/bin/env python3
"""
Clear ALL caches to force fresh PhD-level generation
"""

import os
import shutil
import json
from pathlib import Path

def clear_all_caches():
    """Clear every possible cache in the system"""
    
    print("🧹 CLEARING ALL CACHES TO FORCE FRESH GENERATION")
    print("=" * 60)
    
    # 1. Clear data cache directory
    cache_dirs = [
        "data/cache",
        "data/semantic_cache.json",
        "cache",
        "temp",
        "__pycache__"
    ]
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                if os.path.isfile(cache_dir):
                    os.remove(cache_dir)
                    print(f"✅ Deleted cache file: {cache_dir}")
                else:
                    shutil.rmtree(cache_dir)
                    print(f"✅ Deleted cache directory: {cache_dir}")
            except Exception as e:
                print(f"⚠️ Could not delete {cache_dir}: {e}")
    
    # 2. Clear Python cache files
    for root, dirs, files in os.walk("."):
        for dir_name in dirs[:]:
            if dir_name == "__pycache__":
                try:
                    shutil.rmtree(os.path.join(root, dir_name))
                    print(f"✅ Deleted Python cache: {os.path.join(root, dir_name)}")
                except Exception as e:
                    print(f"⚠️ Could not delete Python cache: {e}")
    
    # 3. Clear semantic cache
    semantic_cache_path = "data/semantic_cache.json"
    if os.path.exists(semantic_cache_path):
        try:
            with open(semantic_cache_path, 'w') as f:
                json.dump({}, f)
            print(f"✅ Cleared semantic cache: {semantic_cache_path}")
        except Exception as e:
            print(f"⚠️ Could not clear semantic cache: {e}")
    
    # 4. Clear any SQLite databases that might cache questions
    db_files = []
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(('.db', '.sqlite', '.sqlite3')):
                db_files.append(os.path.join(root, file))
    
    for db_file in db_files:
        try:
            os.remove(db_file)
            print(f"✅ Deleted database cache: {db_file}")
        except Exception as e:
            print(f"⚠️ Could not delete database: {e}")
    
    print("=" * 60)
    print("🎉 ALL CACHES CLEARED!")
    print("🚀 Next generation will be completely fresh")
    print("🎓 PhD-level questions should now work properly")

if __name__ == "__main__":
    clear_all_caches()
