"""
Knowledge App - Pure QtWebEngine Implementation
Modern web-based UI using QtWebEngine with zero QtWidgets bloatware
"""

from .core.async_converter import async_time_sleep
from .core.async_converter import async_file_read, async_file_write


import sys
import os
import json
import logging
import asyncio
import concurrent.futures
import warnings
from pathlib import Path
from typing import Optional, Dict, Any, List
import threading
import time

# SUPPRESS FAISS GPU WARNINGS BEFORE MCQ MANAGER IMPORT
warnings.filterwarnings("ignore", message=r".*Failed to load GPU Faiss.*", category=UserWarning)
warnings.filterwarnings("ignore", message=r".*GpuIndexIVFFlat.*not defined.*", category=UserWarning)
warnings.filterwarnings("ignore", message=r".*FAISS.*", category=UserWarning)

# SUPPRESS FAISS LOGGING TO ELIMINATE GPU ERROR MESSAGE
faiss_logger = logging.getLogger('faiss')
faiss_logger.setLevel(logging.ERROR)
faiss_loader_logger = logging.getLogger('faiss.loader')
faiss_loader_logger.setLevel(logging.ERROR)

from PyQt5.QtCore import QUrl, pyqtSlot, QObject, pyqtSignal, QTimer, QThread, QCoreApplication, QMetaObject, Qt, Q_ARG
from PyQt5.QtWidgets import QApplication
from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEnginePage
from PyQt5.QtWebChannel import QWebChannel
from .ui_responsiveness_monitor import start_ui_monitoring, add_freeze_recovery_action, get_ui_performance_stats

# Import DeepSeek integration
try:
    from .core.deepseek_integration import get_deepseek_pipeline
    DEEPSEEK_AVAILABLE = True
except ImportError:
    DEEPSEEK_AVAILABLE = False
    logger.warning("⚠️ DeepSeek integration not available")

# Get logger without duplicate configuration to prevent spacing issues
logger = logging.getLogger(__name__)


class TrainingThread(QThread):
    progress = pyqtSignal(str)
    completed = pyqtSignal(str)
    error = pyqtSignal(str)

    def __init__(self, training_manager, dataset_path, epochs, learning_rate):
        super().__init__()
        self.training_manager = training_manager
        self.dataset_path = dataset_path
        self.epochs = epochs
        self.learning_rate = learning_rate
        self._is_running = True

    async def run(self):
        try:
            self.progress.emit("Starting model training...")
            # Simulate training progress
            for i in range(self.epochs):
                if not self._is_running:
                    self.completed.emit("Training stopped by user.")
                    return
                # 🚀 CRITICAL FIX: Use async sleep to prevent UI blocking
                import asyncio
                await asyncio.sleep(1)  # Simulate work without blocking
                self.progress.emit(f"Epoch {i+1}/{self.epochs} completed.")
            self.completed.emit("Model training finished successfully!")
        except Exception as e:
            logger.error(f"Training thread error: {e}")
            self.error.emit(f"Training error: {e}")

    def stop(self):
        self._is_running = False


class FastQuestionGenerator(QThread):
    """High-performance parallel question generator thread - THREAD-SAFE VERSION"""
    
    questionGenerated = pyqtSignal('QVariant')
    batchCompleted = pyqtSignal(int)  # Number of questions generated
    
    def __init__(self, mcq_manager, quiz_params, num_questions=5):
        # CRITICAL FIX: Ensure this is only called from main UI thread
        if QCoreApplication.instance() is None:
            raise RuntimeError("❌ QThread can only be created after QApplication is initialized")
        
        if threading.current_thread() != QCoreApplication.instance().thread():
            raise RuntimeError("❌ CRITICAL: QThread must be created from main UI thread only")
        
        super().__init__()
        self.mcq_manager = mcq_manager
        self.quiz_params = quiz_params
        self.num_questions = num_questions
        self.generated_questions = []
        self.stop_requested = False
        
        logger.info("✅ FastQuestionGenerator created on main UI thread")


class WebEngineBridge(QObject):
    questionGenerated = pyqtSignal('QVariant')
    batchCompleted = pyqtSignal(int)
    trainingProgress = pyqtSignal(str)
    trainingComplete = pyqtSignal(str)
    trainingError = pyqtSignal(str)

    def __init__(self, parent=None, mcq_manager=None, training_manager=None):
        super().__init__(parent)
        self.mcq_manager = mcq_manager
        self.training_manager = training_manager
        self.question_generator_thread = None
        self.training_thread = None

    @pyqtSlot(result=str)
    def hello(self):
        return "Hello from Python!"

    @pyqtSlot(str, str)
    def generateQuestion(self, topic: str, difficulty: str):
        logger.info(f"Received request to generate question on {topic} with difficulty {difficulty}")
        quiz_params = {
            "topic": topic,
            "difficulty": difficulty,
            "game_mode": "casual",
            "submode": "mixed",
            "num_questions": 1
        }
        if self.question_generator_thread and self.question_generator_thread.isRunning():
            logger.warning("Question generation already in progress. Please wait.")
            return

        self.question_generator_thread = FastQuestionGenerator(self.mcq_manager, quiz_params, num_questions=1)
        self.question_generator_thread.questionGenerated.connect(self.questionGenerated.emit)
        self.question_generator_thread.batchCompleted.connect(self.batchCompleted.emit)
        self.question_generator_thread.start()

    @pyqtSlot(str, int, float)
    def startTraining(self, dataset_path: str, epochs: int, learning_rate: float):
        logger.info(f"Received request to start training with dataset: {dataset_path}, epochs: {epochs}, learning rate: {learning_rate}")
        if self.training_thread and self.training_thread.isRunning():
            logger.warning("Training already in progress. Please wait.")
            self.trainingError.emit("Training already in progress.")
            return

        if not self.training_manager:
            logger.error("Training manager not initialized.")
            self.trainingError.emit("Training manager not available.")
            return

        self.training_thread = TrainingThread(self.training_manager, dataset_path, epochs, learning_rate)
        self.training_thread.progress.connect(self.trainingProgress.emit)
        self.training_thread.completed.connect(self.trainingComplete.emit)
        self.training_thread.error.connect(self.trainingError.emit)
        self.training_thread.start()

    @pyqtSlot()
    def stopTraining(self):
        logger.info("Received request to stop training.")
        if self.training_thread and self.training_thread.isRunning():
            self.training_thread.stop()
            self.trainingProgress.emit("Stopping training...")
        else:
            logger.warning("No training in progress to stop.")
            self.trainingError.emit("No training in progress to stop.")
        
    def run(self):
        """Generate questions in parallel for maximum speed"""
        import logging
        logger = logging.getLogger(__name__)
        
        try:
            logger.info(f"🚀 Starting FAST parallel generation of {self.num_questions} questions")
            start_time = time.time()
            
            # Optimize MCQ manager for speed
            self._optimize_mcq_manager()
            
            # CRITICAL FIX: Use safer threading approach - no nested ThreadPoolExecutor
            # Generate questions sequentially to avoid threading violations
            completed = 0
            for i in range(self.num_questions):
                if self.stop_requested:
                    break
                    
                try:
                    # Generate single question without additional threading
                    question_data = self._generate_single_question_safe(i)
                    
                    if question_data:
                        # Emit question immediately when ready - thread-safe
                        self.questionGenerated.emit(question_data)
                        completed += 1
                        logger.info(f"⚡ Question {completed}/{self.num_questions} ready in {time.time() - start_time:.1f}s")
                    else:
                        logger.warning(f"⚠️ Question {i+1} returned no data")
                        
                except Exception as e:
                    logger.error(f"❌ Question {i+1} generation failed: {e}")
                    continue
                        
            elapsed = time.time() - start_time
            logger.info(f"🏁 Generation completed: {completed} questions in {elapsed:.1f}s ({elapsed/max(completed,1):.1f}s avg)")
            self.batchCompleted.emit(completed)
            
        except Exception as e:
            logger.error(f"❌ Fast question generator failed: {e}")
            self.batchCompleted.emit(0)
    
    def _generate_single_question_safe(self, index):
        """CRITICAL FIX: Thread-safe question generation without nested threading"""
        try:
            game_mode = self.quiz_params.get("game_mode", "casual")
            
            # Adjust topic variety based on game mode
            if game_mode == "serious":
                topics = [
                    f"Advanced {self.quiz_params['topic']}",
                    f"{self.quiz_params['topic']} theory",
                    f"{self.quiz_params['topic']} analysis",
                    f"Complex {self.quiz_params['topic']}",
                    f"{self.quiz_params['topic']} problem solving"
                ]
            else:
                topics = [
                    self.quiz_params["topic"],
                    f"{self.quiz_params['topic']} basics",
                    f"{self.quiz_params['topic']} concepts",
                    f"{self.quiz_params['topic']} fundamentals",
                    f"{self.quiz_params['topic']} applications"
                ]
            
            current_topic = topics[index % len(topics)]
            
            quiz_params = {
                "topic": current_topic,
                "difficulty": self.quiz_params["difficulty"],
                "game_mode": game_mode,
                "submode": self.quiz_params.get("submode", "mixed"),
                "num_questions": 1
            }
            
            # 🚀 CRITICAL FIX: Use async generation to prevent UI blocking
            try:
                # Use the thread-safe inference wrapper for truly async generation
                from .core.thread_safe_inference import get_thread_safe_inference
                thread_safe_inference = get_thread_safe_inference()

                # Generate question asynchronously with appropriate timeout
                difficulty = self.quiz_params["difficulty"]
                # Use longer timeouts for expert mode and complex questions
                if difficulty == "expert":
                    timeout_duration = 180.0  # 3 minutes for expert reasoning
                elif difficulty == "hard":
                    timeout_duration = 120.0  # 2 minutes for hard questions
                else:
                    timeout_duration = 90.0   # 1.5 minutes for easy/medium questions

                operation_id = thread_safe_inference.generate_mcq_async(
                    topic=current_topic,
                    difficulty=difficulty,
                    question_type=self.quiz_params.get("submode", "mixed"),
                    timeout=timeout_duration
                )

                # Wait for result with timeout (this runs in background thread)
                # Use same timeout as generation + buffer
                get_timeout = timeout_duration + 30.0  # Add 30s buffer for processing
                result = thread_safe_inference.get_result(operation_id, timeout=get_timeout)

                if result:
                    return self._format_question_data(result)
                else:
                    logger.error(f"❌ Async question generation returned None for topic '{current_topic}'")
                    return None

            except Exception as e:
                logger.error(f"❌ Async question generation failed for topic '{current_topic}': {e}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Safe question generation failed: {e}")
            return None
    
    def _optimize_mcq_manager(self):
        """Optimize MCQ manager settings for maximum speed"""
        try:
            if hasattr(self.mcq_manager, 'offline_generator') and self.mcq_manager.offline_generator:
                # Force GPU usage and optimize for speed
                ollama_interface = getattr(self.mcq_manager.offline_generator, 'ollama_interface', None)
                if ollama_interface:
                    # Optimize Ollama for speed - reduce quality slightly for much faster generation
                    ollama_interface.generation_params = {
                        'temperature': 0.8,  # Slightly higher for faster generation
                        'top_p': 0.9,
                        'top_k': 30,  # Reduced for speed
                        'num_predict': 400,  # Shorter responses for speed
                        'num_ctx': 2048,  # Smaller context for speed
                        'repeat_penalty': 1.1,
                        'seed': -1,  # Random seed for variety
                        # GPU optimization flags
                        'num_gpu': -1,  # Use all available GPU layers
                        'num_thread': 8,  # Optimize CPU threads
                    }
                    logger.info("🔧 Ollama optimized for maximum speed and GPU utilization")
                    
        except Exception as e:
            logger.error(f"❌ Failed to optimize MCQ manager: {e}")
    
    def _format_question_data(self, mcq_result):
        """Format MCQ result into question data"""
        try:
            if hasattr(mcq_result, 'question'):
                question_text = mcq_result.question
                options = mcq_result.options
                correct_answer = mcq_result.correct_answer
                explanation = getattr(mcq_result, 'explanation', 'No explanation available.')
            elif isinstance(mcq_result, dict):
                question_text = mcq_result.get('question', '')
                options = list(mcq_result.get('options', {}).values()) if 'options' in mcq_result else []
                correct_answer = mcq_result.get('options', {}).get(mcq_result.get('correct', 'A'), options[0] if options else '')
                explanation = mcq_result.get('explanation', 'No explanation available.')
            else:
                return None
                
            return {
                "question": question_text,
                "options": options,
                "correct_answer": correct_answer,
                "explanation": explanation,
                "correct_index": options.index(correct_answer) if correct_answer in options else 0
            }
        except Exception as e:
            logger.error(f"Error formatting question data: {e}")
            return None
    
    def stop(self):
        """Stop question generation"""
        self.stop_requested = True


class PythonBridge(QObject):
    """Bridge between Python backend and JavaScript frontend"""
    
    # Signals to update the web UI
    updateStatus = pyqtSignal(str)
    questionReceived = pyqtSignal('QVariant')
    quizCompleted = pyqtSignal('QVariant')
    errorOccurred = pyqtSignal(str)
    answerFeedback = pyqtSignal('QVariant')  # New signal for answer feedback
    apiTestResult = pyqtSignal(str, bool, str)  # provider, success, message
    topicProfileUpdated = pyqtSignal('QVariant')  # NEW SIGNAL: Topic analysis results
    
    # 🔥 FIRE Training Monitoring Signals
    fireTrainingStarted = pyqtSignal('QVariant')
    fireInitialEstimate = pyqtSignal('QVariant')
    fireRealtimeUpdate = pyqtSignal('QVariant')
    fireTrainingCompleted = pyqtSignal('QVariant')
    
    # 🚀 Phase 2: Enhanced Training Signals for Complete User Experience
    trainingProgressStructured = pyqtSignal('QVariant')  # Structured progress data
    trainingStatusChanged = pyqtSignal(str)              # Training status changes
    trainingMetricsUpdate = pyqtSignal('QVariant')       # Real-time training metrics
    trainingConfigSaved = pyqtSignal('QVariant')         # Training config persistence
    
    # 🎯 Phase 3: Enterprise Training Management Signals
    fireEstimationComplete = pyqtSignal('QVariant')      # FIRE estimation complete
    holdoutDatasetCreated = pyqtSignal('QVariant')       # Holdout dataset creation
    modelEvaluationComplete = pyqtSignal('QVariant')     # Model evaluation results
    trainingHistoryUpdated = pyqtSignal('QVariant')      # Training history changes

    # 🌊 Token Streaming Signals for Real-Time Generation Visualization
    tokenStreamStarted = pyqtSignal(str)                 # Stream session ID
    tokenStreamChunk = pyqtSignal(str, str)              # session_id, token_chunk
    tokenStreamCompleted = pyqtSignal(str, 'QVariant')   # session_id, final_question
    tokenStreamError = pyqtSignal(str, str)              # session_id, error_message
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # MINIMAL INITIALIZATION - Remove problematic imports that cause spacing issues
        import logging
        logger = logging.getLogger(__name__)
        
        # SIMPLE QUIZ STATE - NO COMPLEX BUFFER MANAGEMENT
        self.current_quiz = None
        self.quiz_questions = []  # All questions generated upfront
        self.current_question_index = 0
        self.mcq_manager = None
        self.mcq_manager_ready = False
        self.mcq_manager_initializing = False

        self.training_manager = None
        self.training_manager_ready = False
        self.training_manager_initializing = False

        self.training_manager = None
        self.training_manager_ready = False
        self.training_manager_initializing = False
        
        # Essential state variables
        self.question_buffer = []
        self.question_history = []
        self.fast_generator = None
        self.pending_generations = 0
        self.buffer_size = 5
        self.min_buffer_threshold = 2
        
        # Cache management
        self.cache_file = Path("data/cache/question_cache.json")
        self.persistent_cache = []
        self.cache_topic = None
        self.cache_difficulty = None
        self.cache_game_mode = None
        self.cache_submode = None
        self.answered_questions_history = []
        
        # Configuration
        self.config = {}  # Initialize with empty config
        
        self.generation_lock = threading.RLock()
        
        # Initialize directories
        Path("user_data").mkdir(exist_ok=True)
        Path("data/cache").mkdir(parents=True, exist_ok=True)
        
        # PLACEHOLDER ATTRIBUTES - Initialize lazily to avoid startup delays
        self.resource_manager = None
        self.topic_analyzer = None
        self.ui_monitor = None
        self.fire_integration = None

        # DeepSeek integration
        self.deepseek_pipeline = None
        self.deepseek_ready = False

        # 🌊 Token streaming integration
        self.streaming_engine = None
        self.streaming_ready = False

        # Silent initialization - no logging output

    def _get_training_manager(self):
        """Lazy initialization of training manager"""
        if self.training_manager is None and not self.training_manager_initializing:
            self.training_manager_initializing = True
            try:
                from .core.training_manager import TrainingManager
                self.training_manager = TrainingManager()
                self.training_manager_ready = True
                logger.info("✅ TrainingManager initialized successfully.")
            except Exception as e:
                logger.error(f"❌ Failed to initialize TrainingManager: {e}")
                self.training_manager_ready = False
            finally:
                self.training_manager_initializing = False
        return self.training_manager if self.training_manager_ready else None
    
    def _get_resource_manager(self):
        """Lazy initialization of resource manager"""
        if self.resource_manager is None:
            try:
                from .core.unified_resource_manager import get_unified_resource_manager
                self.resource_manager = get_unified_resource_manager()
                self.resource_manager.start_monitoring()
                self.app_resource_id = self.resource_manager.register_resource(self, "main_app")
            except Exception as e:
                pass  # Silent failure
        return self.resource_manager
    
    def _get_topic_analyzer(self):
        """Lazy initialization of topic analyzer"""
        if self.topic_analyzer is None:
            try:
                from .core.topic_analyzer import get_topic_analyzer
                self.topic_analyzer = get_topic_analyzer()
                logger.info("✅ Topic analyzer loaded successfully")
            except Exception as e:
                logger.error(f"❌ Failed to load topic analyzer: {e}")
                self.topic_analyzer = None
        return self.topic_analyzer

    def _get_deepseek_pipeline(self):
        """Lazy initialization of DeepSeek pipeline"""
        if self.deepseek_pipeline is None and DEEPSEEK_AVAILABLE:
            try:
                self.deepseek_pipeline = get_deepseek_pipeline()
                self.deepseek_ready = self.deepseek_pipeline.is_ready()
                if self.deepseek_ready:
                    logger.info("🧠 DeepSeek pipeline initialized successfully")
                else:
                    logger.warning("⚠️ DeepSeek pipeline not ready (missing models)")
            except Exception as e:
                logger.error(f"❌ Failed to initialize DeepSeek pipeline: {e}")
                self.deepseek_ready = False
        return self.deepseek_pipeline if self.deepseek_ready else None

    def _get_streaming_engine(self):
        """🌊 Lazy initialization of token streaming engine"""
        if self.streaming_engine is None:
            try:
                from .core.streaming_inference import get_streaming_engine

                # Configure streaming engine
                config = {
                    'stream_delay': 0.03,  # 30ms between tokens for smooth animation
                    'chunk_size': 2,       # 2 tokens per chunk
                    'enable_thinking_simulation': True
                }

                self.streaming_engine = get_streaming_engine(config)

                # Set up token callback to emit signals
                def token_callback(session_id: str, token_chunk: str):
                    if token_chunk == "STREAM_COMPLETE":
                        # Get final question and emit completion
                        session_info = self.streaming_engine.get_session_info(session_id)
                        if session_info:
                            final_question = self.streaming_engine.active_sessions[session_id].final_question
                            self.tokenStreamCompleted.emit(session_id, final_question)
                    elif token_chunk.startswith("ERROR:"):
                        self.tokenStreamError.emit(session_id, token_chunk[6:])
                    else:
                        self.tokenStreamChunk.emit(session_id, token_chunk)

                self.streaming_engine.set_token_callback(token_callback)
                self.streaming_ready = True

                logger.info("🌊 Token streaming engine initialized successfully")

            except Exception as e:
                logger.error(f"❌ Failed to initialize streaming engine: {e}")
                self.streaming_ready = False

        return self.streaming_engine if self.streaming_ready else None

    @pyqtSlot(str)
    def log(self, message):
        """Log messages from JavaScript"""
        logger.info(f"JS: {message}")

    @pyqtSlot(str, str, str, result=str)
    def startTokenStreaming(self, topic: str, difficulty: str, question_type: str) -> str:
        """🌊 Start token streaming for real-time question generation visualization"""
        try:
            logger.info(f"🌊 Starting token streaming: {topic} ({difficulty}) - {question_type}")

            # 🚀 EMERGENCY FIX: Always fallback to normal generation for now
            logger.warning("🚀 EMERGENCY: Token streaming disabled - falling back to normal generation")
            QTimer.singleShot(100, lambda: self._fallback_to_normal_generation(topic, difficulty, question_type))
            return json.dumps({
                "success": True,
                "fallback": True,
                "message": "Token streaming disabled, using normal generation"
            })

        except Exception as e:
            logger.error(f"❌ Token streaming error: {e}")
            # Always fallback to normal generation on any error
            QTimer.singleShot(100, lambda: self._fallback_to_normal_generation(topic, difficulty, question_type))
            return json.dumps({
                "success": True,
                "fallback": True,
                "message": f"Streaming error, using normal generation: {str(e)}"
            })

    def _fallback_to_normal_generation(self, topic: str, difficulty: str, question_type: str):
        """🚀 EMERGENCY FALLBACK: Generate question normally when streaming fails"""
        try:
            logger.info(f"🚀 FALLBACK: Generating question normally for {topic} ({difficulty})")

            # Close the streaming dialog
            self.page().runJavaScript("if (window.closeTokenStreamingDialog) window.closeTokenStreamingDialog();")

            # Trigger normal question generation
            self.page().runJavaScript(f"""
                // Force normal generation
                console.log('🚀 FALLBACK: Starting normal generation');
                if (window.generateQuestionNormally) {{
                    window.generateQuestionNormally('{topic}', '{difficulty}', '{question_type}');
                }} else {{
                    // Direct fallback
                    generateQuestion();
                }}
            """)

        except Exception as e:
            logger.error(f"❌ Fallback generation failed: {e}")
            # Last resort - just close the dialog
            self.page().runJavaScript("if (window.closeTokenStreamingDialog) window.closeTokenStreamingDialog();")

    @pyqtSlot(str)
    def analyzeTopic(self, topic: str):
        """
        🧠 Analyzes the topic in real-time and sends the profile back to the UI.
        Enhanced with AI spell correction and intelligent question type recommendations.
        🚀 CRITICAL FIX: Made async to prevent UI blocking
        """
        # 🚀 CRITICAL FIX: Move ALL topic analysis to background thread to prevent UI blocking
        def analyze_topic_background():
            try:
                logger.info(f"🔍 UI TOPIC ANALYSIS REQUEST: '{topic}'")

                if not topic or len(topic.strip()) < 2:
                    # If topic is too short, enable everything by default
                    profile = {
                        "is_conceptual_possible": True,
                        "is_numerical_possible": True,
                        "confidence": "low",
                        "detected_type": "unknown",
                        "original_topic": topic,
                        "corrected_topic": topic,
                        "corrections_made": [],
                        "spelling_corrected": False,
                        "ui_feedback": {
                            "show_correction_notice": False,
                            "correction_message": "",
                            "corrections_made": []
                        }
                    }
                    logger.debug("🧠 Topic too short - enabling all question types")
                else:
                    # Use the enhanced TopicAnalyzer with AI spell correction (lazy-loaded)
                    topic_analyzer = self._get_topic_analyzer()
                    logger.info(f"🧠 Topic analyzer available: {topic_analyzer is not None}")

                    if topic_analyzer:
                        logger.info(f"🧠 Semantic mapping enabled: {getattr(topic_analyzer, 'use_semantic_mapping', False)}")
                        logger.info(f"🧠 Semantic mapper available: {getattr(topic_analyzer, 'semantic_mapper', None) is not None}")

                        # 🚀 CRITICAL: This is the blocking operation that freezes UI
                        profile = topic_analyzer.get_topic_profile(topic.strip())
                        logger.info(f"🎯 TOPIC ANALYSIS RESULT: {profile}")
                    else:
                        logger.warning("⚠️ Topic analyzer not available - using smart fallback")
                        # Smart fallback with basic topic detection
                        profile = self._get_smart_fallback_profile(topic.strip())

                    # Log AI spell corrections if any were made
                    if profile.get('spelling_corrected', False):
                        logger.info(f"🤖 AI Corrections applied: {profile.get('corrections_made', [])}")
                        logger.info(f"📝 '{profile.get('original_topic', topic)}' → '{profile.get('corrected_topic', topic)}'")

                    logger.info(f"🧠 Topic '{topic}' analyzed: {profile['detected_type']} (confidence: {profile['confidence']})")

                # Emit the enhanced signal to the JavaScript frontend (thread-safe)
                QMetaObject.invokeMethod(
                    self, "_emit_topic_profile",
                    Qt.QueuedConnection,
                    Q_ARG(str, json.dumps(profile))
                )

            except Exception as e:
                logger.error(f"❌ Topic analysis failed for '{topic}': {e}")
                # Safe fallback - enable everything with AI correction placeholders
                fallback_profile = {
                    "is_conceptual_possible": True,
                    "is_numerical_possible": True,
                    "confidence": "low",
                    "detected_type": "error",
                    "original_topic": topic,
                    "corrected_topic": topic,
                    "corrections_made": [],
                    "spelling_corrected": False,
                    "ui_feedback": {
                        "show_correction_notice": False,
                        "correction_message": "",
                        "corrections_made": []
                    }
                }
                QMetaObject.invokeMethod(
                    self, "_emit_topic_profile",
                    Qt.QueuedConnection,
                    Q_ARG(str, json.dumps(fallback_profile))
                )

        # 🚀 CRITICAL FIX: Start background analysis thread to prevent UI blocking
        import threading
        thread = threading.Thread(target=analyze_topic_background, daemon=True)
        thread.start()
        logger.info(f"🚀 Topic analysis started in background thread for: '{topic}'")

    @pyqtSlot(str)
    def _emit_topic_profile(self, profile_json):
        """Thread-safe topic profile emission"""
        try:
            profile = json.loads(profile_json)
            logger.info(f"🚀 Emitting topic profile on main thread: {profile.get('detected_type', 'unknown')} (confidence: {profile.get('confidence', 'unknown')})")
            self.topicProfileUpdated.emit(profile)
        except Exception as e:
            logger.error(f"❌ Failed to emit topic profile: {e}")

    def _get_smart_fallback_profile(self, topic):
        """Smart fallback topic analysis when main analyzer fails"""
        topic_lower = topic.lower().strip()

        # Define numerical topics
        numerical_topics = {
            'math', 'mathematics', 'algebra', 'calculus', 'geometry', 'trigonometry',
            'statistics', 'probability', 'arithmetic', 'numbers', 'equations',
            'physics', 'chemistry', 'atoms', 'molecules', 'quantum', 'mechanics',
            'thermodynamics', 'electricity', 'magnetism', 'optics', 'waves',
            'engineering', 'economics', 'finance', 'accounting', 'data science',
            'computer science', 'algorithms', 'programming', 'coding'
        }

        # Check if topic is numerical
        is_numerical = any(num_topic in topic_lower for num_topic in numerical_topics)

        if is_numerical:
            detected_type = "numerical"
            confidence = "high" if topic_lower in numerical_topics else "medium"
            is_numerical_possible = True
            is_conceptual_possible = False  # Prefer numerical for clearly numerical topics
        else:
            detected_type = "conceptual"
            confidence = "medium"
            is_numerical_possible = True
            is_conceptual_possible = True

        profile = {
            "is_conceptual_possible": is_conceptual_possible,
            "is_numerical_possible": is_numerical_possible,
            "confidence": confidence,
            "detected_type": detected_type,
            "original_topic": topic,
            "corrected_topic": topic,
            "corrections_made": [],
            "spelling_corrected": False,
            "ui_feedback": {
                "show_correction_notice": False,
                "correction_message": "",
                "corrections_made": []
            }
        }

        logger.info(f"🧠 Smart fallback analysis: '{topic}' → {detected_type} ({confidence} confidence)")
        return profile
            
    @pyqtSlot(str, result=str)
    def getTopicRecommendations(self, topic: str):
        """
        🧠 Get comprehensive topic recommendations including optimal question type.
        Enhanced with AI spell correction. Returns JSON string with detailed analysis.
        """
        try:
            if not topic or len(topic.strip()) < 2:
                recommendations = {
                    "optimal_question_type": "mixed",
                    "is_conceptual_possible": True,
                    "is_numerical_possible": True,
                    "confidence": "low",
                    "detected_type": "unknown",
                    "original_topic": topic,
                    "corrected_topic": topic,
                    "corrections_made": [],
                    "spelling_corrected": False,
                    "ui_feedback": {
                        "show_correction_notice": False,
                        "correction_message": "",
                        "corrections_made": []
                    }
                }
            else:
                topic_analyzer = self._get_topic_analyzer()
                if topic_analyzer:
                    recommendations = topic_analyzer.get_topic_recommendations(topic.strip())
                else:
                    recommendations = {
                        "optimal_question_type": "mixed",
                        "is_conceptual_possible": True,
                        "is_numerical_possible": True,
                        "confidence": "low",
                        "detected_type": "unknown",
                        "original_topic": topic,
                        "corrected_topic": topic,
                        "corrections_made": [],
                        "spelling_corrected": False,
                        "ui_feedback": {
                            "show_correction_notice": False,
                            "correction_message": "",
                            "corrections_made": []
                        }
                    }
                
                # Log AI corrections for debugging
                if recommendations.get('spelling_corrected', False):
                    logger.debug(f"🤖 Recommendations with AI corrections: {recommendations.get('corrections_made', [])}")
                
            return json.dumps(recommendations, ensure_ascii=False, separators=(',', ':'))
            
        except Exception as e:
            logger.error(f"❌ Topic recommendations failed for '{topic}': {e}")
            # Return safe fallback JSON with AI spell correction placeholders
            fallback = {
                "optimal_question_type": "mixed",
                "is_conceptual_possible": True,
                "is_numerical_possible": True,
                "confidence": "low",
                "detected_type": "error",
                "original_topic": topic,
                "corrected_topic": topic,
                "corrections_made": [],
                "spelling_corrected": False,
                "ui_feedback": {
                    "show_correction_notice": False,
                    "correction_message": "",
                    "corrections_made": []
                }
            }
            return json.dumps(fallback, ensure_ascii=False, separators=(',', ':'))
    
    @pyqtSlot(result=str)
    def getGpuUtilization(self):
        """Get current GPU utilization stats - GUARANTEED VALID JSON"""
        try:
            # Try to get GPU stats with comprehensive error handling
            stats = None
            try:
                from .core.hardware_utils import get_gpu_utilization
                stats = get_gpu_utilization()
                logger.debug("✅ Hardware utils GPU stats retrieved")
            except ImportError as import_error:
                logger.debug(f"📊 Hardware utils not available: {import_error}")
                # Hardware utils not available
                stats = {
                    "available": False,
                    "error": "hardware_utils module not available",
                    "gpu_utilization": 0,
                    "memory_utilization": 0
                }
            except Exception as gpu_error:
                logger.debug(f"📊 GPU stats failed: {gpu_error}")
                # GPU stats failed
                stats = {
                    "available": False,
                    "error": str(gpu_error)[:100],  # Limit error length
                    "gpu_utilization": 0,
                    "memory_utilization": 0
                }
            
            # Validate that stats is a dictionary
            if not stats or not isinstance(stats, dict):
                logger.warning("⚠️ Invalid stats object, using fallback")
                stats = {
                    "available": False,
                    "error": "invalid_stats_object",
                    "gpu_utilization": 0,
                    "memory_utilization": 0
                }
            
            # Ensure ALL values are JSON serializable with strict validation
            clean_stats = {}
            for key, value in stats.items():
                try:
                    if value is None:
                        clean_stats[key] = None
                    elif isinstance(value, bool):
                        clean_stats[key] = bool(value)
                    elif isinstance(value, int):
                        clean_stats[key] = int(value)
                    elif isinstance(value, float):
                        # Handle NaN and infinity values
                        if value != value:  # NaN check
                            clean_stats[key] = 0.0
                        elif value == float('inf') or value == float('-inf'):
                            clean_stats[key] = 0.0
                        else:
                            clean_stats[key] = float(value)
                    elif isinstance(value, str):
                        # Ensure string is not too long and clean
                        clean_value = str(value)[:200]  # Limit length
                        clean_stats[key] = clean_value
                    else:
                        # Convert anything else to string safely
                        clean_stats[key] = str(value)[:100]
                except Exception as clean_error:
                    logger.warning(f"⚠️ Error cleaning stats key {key}: {clean_error}")
                    clean_stats[key] = "error_cleaning_value"
            
            # Ensure essential keys exist
            essential_keys = ["available", "gpu_utilization", "memory_utilization"]
            for key in essential_keys:
                if key not in clean_stats:
                    if key == "available":
                        clean_stats[key] = False
                    else:
                        clean_stats[key] = 0
            
            # Force JSON string creation with comprehensive error handling
            try:
                json_result = json.dumps(clean_stats, ensure_ascii=False, separators=(',', ':'))
                
                # Validate the JSON string is not empty and looks valid
                if not json_result or len(json_result) < 10:
                    raise ValueError("JSON result too short")
                    
                if not (json_result.startswith('{') and json_result.endswith('}')):
                    raise ValueError("JSON result format invalid")
                
                # Test parse to ensure it's valid JSON
                json.loads(json_result)
                
                logger.debug(f"📊 GPU stats JSON created: {len(json_result)} chars")
                return str(json_result)  # Ensure string type
                
            except (TypeError, ValueError, json.JSONDecodeError) as json_error:
                logger.error(f"❌ JSON serialization failed: {json_error}")
                # Return minimal fallback JSON - absolutely guaranteed to work
                fallback = {
                    "available": False, 
                    "error": f"json_error_{str(json_error)[:50]}", 
                    "gpu_utilization": 0, 
                    "memory_utilization": 0
                }
                try:
                    fallback_json = json.dumps(fallback)
                    return str(fallback_json)
                except Exception:
                    # Ultimate fallback - hardcoded JSON string
                    return '{"available":false,"error":"critical_json_error","gpu_utilization":0,"memory_utilization":0}'
            
        except Exception as e:
            logger.error(f"❌ Critical error in getGpuUtilization: {e}")
            # Return guaranteed valid JSON fallback - hardcoded for absolute safety
            try:
                emergency_fallback = {
                    "available": False, 
                    "error": f"critical_error_{str(e)[:50]}",
                    "gpu_utilization": 0,
                    "memory_utilization": 0
                }
                emergency_json = json.dumps(emergency_fallback)
                return str(emergency_json)
            except Exception:
                # Absolute emergency - return hardcoded JSON
                return '{"available":false,"error":"ultimate_fallback","gpu_utilization":0,"memory_utilization":0}'
        
    @pyqtSlot(str, result=str)
    def getConfig(self, key):
        """Get configuration value"""
        return json.dumps(self.config.get(key, None))
        
    @pyqtSlot(str, str)
    def setConfig(self, key, value):
        """Set configuration value"""
        logger.info(f"Config set: {key} = {value}")
        # TODO: Implement actual config storage

    @pyqtSlot(str, str, str, result=str)
    def generateDeepSeekQuestion(self, topic: str, difficulty: str, question_type: str = "mixed"):
        """
        🧠 Generate expert-level question using DeepSeek two-model pipeline
        """
        try:
            deepseek_pipeline = self._get_deepseek_pipeline()
            if not deepseek_pipeline:
                return json.dumps({
                    "success": False,
                    "error": "DeepSeek pipeline not available",
                    "fallback": True
                })

            logger.info(f"🧠 Generating DeepSeek question: {topic} ({difficulty}) - Type: {question_type}")

            # Generate question using DeepSeek pipeline
            result = deepseek_pipeline.generate_expert_question(
                topic=topic.strip(),
                difficulty=difficulty.lower(),
                question_type=question_type.lower()
            )

            if result:
                logger.info("✅ DeepSeek question generated successfully")
                return json.dumps({
                    "success": True,
                    "question": result,
                    "method": "deepseek_pipeline"
                })
            else:
                logger.warning("⚠️ DeepSeek generation failed")
                return json.dumps({
                    "success": False,
                    "error": "DeepSeek generation failed",
                    "fallback": True
                })

        except Exception as e:
            logger.error(f"❌ DeepSeek question generation error: {e}")
            return json.dumps({
                "success": False,
                "error": str(e),
                "fallback": True
            })

    @pyqtSlot(result=str)
    def getDeepSeekStatus(self):
        """
        🔍 Get DeepSeek pipeline status and available models
        """
        try:
            if not DEEPSEEK_AVAILABLE:
                return json.dumps({
                    "available": False,
                    "error": "DeepSeek integration not installed"
                })

            deepseek_pipeline = self._get_deepseek_pipeline()
            if deepseek_pipeline:
                return json.dumps({
                    "available": True,
                    "ready": self.deepseek_ready,
                    "thinking_model": deepseek_pipeline.thinking_model,
                    "json_model": deepseek_pipeline.json_model,
                    "available_models": deepseek_pipeline.available_models
                })
            else:
                return json.dumps({
                    "available": True,
                    "ready": False,
                    "error": "Pipeline initialization failed"
                })

        except Exception as e:
            logger.error(f"❌ DeepSeek status check failed: {e}")
            return json.dumps({
                "available": False,
                "error": str(e)
            })
        
    @pyqtSlot(str)
    def saveUserSettings(self, settings_json):
        """Save user settings including API keys"""
        try:
            settings = json.loads(settings_json)
            
            # Save to user_settings.json
            settings_path = Path("user_data/user_settings.json")
            settings_path.parent.mkdir(exist_ok=True)
            
            # Load existing settings
            existing_settings = {}
            if settings_path.exists():
                try:
                    with open(settings_path, 'r', encoding='utf-8') as f:
                        existing_settings = json.load(f)
                except Exception as e:
                    logger.warning(f"Could not load existing settings: {e}")
            
            # Merge settings
            existing_settings.update(settings)
            
            # Save merged settings
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(existing_settings, f, indent=2, ensure_ascii=False)
            
            logger.info("✅ User settings saved successfully")
            
            # Reinitialize MCQ manager with new API keys if they changed
            if 'api_keys' in settings:
                self._reinitialize_mcq_manager_with_api_keys(settings['api_keys'])
                
        except Exception as e:
            logger.error(f"❌ Failed to save user settings: {e}")
    
    @pyqtSlot(result=str)
    def getUserSettings(self):
        """Get user settings - GUARANTEED to return valid JSON string"""
        try:
            # CRITICAL FIX: Ensure user_data directory exists
            user_data_dir = Path("user_data")
            user_data_dir.mkdir(exist_ok=True)
            
            settings_path = user_data_dir / "user_settings.json"
            
            # Default settings structure - GUARANTEED valid
            default_settings = {
                "theme": "light",
                "font_size": 10,
                "storage_limit": **********,  # 1GB as integer
                "auto_switch_images": False,
                "offline_mode": True,
                "answered_questions_history": [],
                "default_timer": 30,
                "show_answers": True,
                "api_keys": {
                    "openai": "",
                    "anthropic": "",
                    "gemini": "",
                    "groq": "",
                    "openrouter": ""
                },
                "default_game_mode": "casual",
                "default_difficulty": "medium",
                "default_submode": "mixed",
                "default_quiz_mode": "offline"
            }
            
            if settings_path.exists():
                try:
                    with open(settings_path, 'r', encoding='utf-8') as f:
                        loaded_settings = json.load(f)
                    
                    # Merge with defaults to ensure all required keys exist
                    settings = {**default_settings, **loaded_settings}
                    
                    # Ensure api_keys structure is complete
                    if 'api_keys' not in settings or not isinstance(settings['api_keys'], dict):
                        settings['api_keys'] = default_settings['api_keys']
                    else:
                        # Merge api_keys to ensure all providers exist
                        settings['api_keys'] = {**default_settings['api_keys'], **settings['api_keys']}
                    
                except (json.JSONDecodeError, KeyError, TypeError) as e:
                    # File exists but is corrupted - use defaults
                    settings = default_settings
                    
            else:
                # File doesn't exist - use defaults and create it
                settings = default_settings
                try:
                    with open(settings_path, 'w', encoding='utf-8') as f:
                        json.dump(settings, f, indent=2, ensure_ascii=False)
                except Exception:
                    # Silent failure - continue with defaults
                    pass
            
            # GUARANTEED valid JSON response
            json_string = json.dumps(settings, ensure_ascii=False, separators=(',', ':'))
            
            # Final validation - ensure the JSON is not empty
            if not json_string or len(json_string) < 10:
                raise ValueError("Generated JSON too short")
            
            return json_string
                
        except Exception as e:
            # ABSOLUTE FALLBACK - hardcoded valid JSON string
            return '{"theme":"light","font_size":10,"storage_limit":**********,"auto_switch_images":false,"offline_mode":true,"answered_questions_history":[],"default_timer":30,"show_answers":true,"api_keys":{"openai":"","anthropic":"","gemini":"","groq":"","openrouter":""},"default_game_mode":"casual","default_difficulty":"medium","default_submode":"mixed","default_quiz_mode":"offline"}'
    
    @pyqtSlot(str, str)
    def testApiKey(self, provider, api_key):
        """Test an API key with minimal, low-cost API call and specific feedback"""
        try:
            logger.info(f"🧪 Testing {provider} API key with lightweight test...")
            
            # Prepare test result variables
            test_success = False
            test_message = "Unknown error"
            
            # Create a minimal, low-cost test for each provider
            if not aiohttp:
                self.apiTestResult.emit(provider, False, "❌ aiohttp not available - install with: pip install aiohttp")
                return
            
            # Use asyncio to make the lightweight test call
            import asyncio
            import aiohttp
            
            async def lightweight_api_test():
                """Make a minimal API call to test the key"""
                try:
                    timeout = aiohttp.ClientTimeout(total=10, connect=5)
                    async with aiohttp.ClientSession(timeout=timeout) as session:
                        
                        if provider.lower() == 'openai':
                            headers = {'Authorization': f'Bearer {api_key}', 'Content-Type': 'application/json'}
                            payload = {
                                "model": "gpt-3.5-turbo",
                                "messages": [{"role": "user", "content": "test"}],
                                "max_tokens": 1,  # Minimal tokens to reduce cost
                                "temperature": 0
                            }
                            url = 'https://api.openai.com/v1/chat/completions'
                            
                        elif provider.lower() == 'anthropic':
                            headers = {
                                'x-api-key': api_key, 
                                'Content-Type': 'application/json', 
                                'anthropic-version': '2023-06-01'
                            }
                            payload = {
                                "model": "claude-3-haiku-20240307",  # Cheapest model
                                "max_tokens": 1,  # Minimal tokens
                                "messages": [{"role": "user", "content": "test"}]
                            }
                            url = 'https://api.anthropic.com/v1/messages'
                            
                        elif provider.lower() == 'groq':
                            headers = {'Authorization': f'Bearer {api_key}', 'Content-Type': 'application/json'}
                            payload = {
                                "model": "mixtral-8x7b-32768",
                                "messages": [{"role": "user", "content": "test"}],
                                "max_tokens": 1,  # Minimal tokens
                                "temperature": 0
                            }
                            url = 'https://api.groq.com/openai/v1/chat/completions'
                            
                        elif provider.lower() == 'gemini':
                            headers = {'Content-Type': 'application/json'}
                            payload = {
                                "contents": [{"parts": [{"text": "test"}]}],
                                "generationConfig": {"maxOutputTokens": 1}  # Minimal tokens
                            }
                            url = f'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}'
                            
                        elif provider.lower() == 'openrouter':
                            headers = {
                                'Authorization': f'Bearer {api_key}', 
                                'Content-Type': 'application/json',
                                'HTTP-Referer': 'https://knowledge-app.local',
                                'X-Title': 'Knowledge App'
                            }
                            payload = {
                                "model": "meta-llama/llama-3.1-8b-instruct:free",  # Free model
                                "messages": [{"role": "user", "content": "test"}],
                                "max_tokens": 1,  # Minimal tokens
                                "temperature": 0
                            }
                            url = 'https://openrouter.ai/api/v1/chat/completions'
                            
                        else:
                            return False, f"❌ Unknown provider: {provider}"
                        
                        # Make the lightweight API request
                        async with session.post(url, headers=headers, json=payload) as response:
                            # Handle specific HTTP error codes with detailed messages
                            if response.status == 200:
                                return True, "✅ API Key is valid and working"
                            elif response.status == 401:
                                return False, "❌ Invalid API Key (Authentication Failed)"
                            elif response.status == 403:
                                return False, "❌ API Key lacks required permissions"
                            elif response.status == 429:
                                return False, "❌ Rate Limit Exceeded. Check your plan or wait"
                            elif response.status == 402:
                                return False, "❌ Insufficient credits or billing issue"
                            elif response.status == 404:
                                return False, "❌ Model or endpoint not found. Check your plan"
                            elif response.status >= 500:
                                return False, f"❌ Server Error: {provider} service is experiencing issues ({response.status})"
                            else:
                                error_text = await response.text()
                                return False, f"❌ API Error: HTTP {response.status} - {error_text[:100]}"
                                
                except aiohttp.ClientResponseError as api_error:
                    if api_error.status == 401:
                        return False, "❌ Invalid API Key (Authentication Failed)"
                    elif api_error.status == 429:
                        return False, "❌ Rate Limit Exceeded. Please check your plan or wait"
                    elif api_error.status == 402:
                        return False, "❌ Insufficient credits or billing issue"
                    else:
                        return False, f"❌ API Error: HTTP {api_error.status}"
                        
                except asyncio.TimeoutError:
                    return False, f"❌ API request timed out for {provider}"
                    
                except aiohttp.ClientError as e:
                    return False, f"❌ Network error: {str(e)[:100]}"
                    
                except Exception as e:
                    return False, f"❌ Test failed: {str(e)[:100]}"
            
            # Run the async test
            try:
                # Create new event loop for the test
                loop = asyncio.new_event_loop() 
                asyncio.set_event_loop(loop)
                try:
                    test_success, test_message = loop.run_until_complete(
                        asyncio.wait_for(lightweight_api_test(), timeout=15.0)
                    )
                finally:
                    loop.close()
                    
            except asyncio.TimeoutError:
                test_success = False
                test_message = f"❌ API test timed out for {provider}"
            except Exception as e:
                test_success = False
                test_message = f"❌ Test execution failed: {str(e)[:100]}"
            
            # Log the result
            if test_success:
                logger.info(f"✅ {provider} API key test successful")
            else:
                logger.warning(f"⚠️ {provider} API key test failed: {test_message}")
            
            # Emit the result to JavaScript
            self.apiTestResult.emit(provider, test_success, test_message)
            
        except Exception as e:
            logger.error(f"❌ Failed to test {provider} API key: {e}")
            self.apiTestResult.emit(provider, False, f"❌ Test failed: {str(e)[:100]}")

    def _reinitialize_mcq_manager_with_api_keys(self, api_keys):
        """Reinitialize MCQ manager with new API keys - Enhanced with GPU cleanup"""
        try:
            if hasattr(self, '_mcq_manager') and self._mcq_manager:
                # 🔥 REMOVED: _emergency_clear_gpu_memory() call - not needed for online APIs
                
                # Update the online generator with new API keys
                if hasattr(self._mcq_manager, 'online_generator') and self._mcq_manager.online_generator:
                    self._mcq_manager.online_generator._update_api_keys(api_keys)
                    logger.info("🔄 MCQ manager reinitialized with new API keys")
        except Exception as e:
            logger.error(f"❌ Failed to reinitialize MCQ manager: {e}")
    
    def _initialize_mcq_manager(self):
        """Initialize MCQ manager for question generation - FULLY ASYNC"""
        self.mcq_manager = None
        self.mcq_manager_ready = False
        self.mcq_manager_initializing = False
        logger.info("MCQ Manager will be initialized asynchronously when needed")
        
    def _get_mcq_manager(self):
        """Get MCQ manager - returns None if not ready yet (non-blocking)"""
        return self.mcq_manager if self.mcq_manager_ready else None
    
    def _start_mcq_manager_initialization(self):
        """🔥 OPTIMIZED: Start MCQ manager initialization without blocking"""
        if hasattr(self, 'mcq_manager_initializing') and self.mcq_manager_initializing:
            logger.info("⚡ MCQ Manager already initializing - skipping duplicate init")
            return
            
        self.mcq_manager_initializing = True
        logger.info("🔧 Starting optimized MCQ manager initialization...")
        
        def fast_initialize():
            """Streamlined initialization process"""
            try:
                # 🔥 CRITICAL: Initialize unified inference manager FIRST
                from .core.unified_inference_manager import initialize_unified_inference
                logger.info("🔧 Initializing unified inference manager...")
                
                # Quick initialization with minimal config
                inference_ready = initialize_unified_inference({
                    'timeout': 10.0,  # Fast timeout
                    'mode': 'auto',   # Auto-detect best available
                    'prefer_local': True  # Prefer local models
                })
                
                if inference_ready:
                    logger.info("✅ Unified inference manager ready")
                else:
                    logger.warning("⚠️ Unified inference manager failed, using fallback")
                
                # 🔥 STREAMLINED: Use simple MCQ manager
                from .core.mcq_manager import MCQManager
                config = {
                    'use_cache': True,
                    'timeout': 15.0,
                    'max_retries': 2,
                    'fallback_enabled': True
                }
                
                self.mcq_manager = MCQManager(config)
                
                # Quick availability check
                self.mcq_manager_ready = True
                self.mcq_manager_initializing = False
                
                logger.info("🚀 MCQ Manager initialized successfully!")
                
            except Exception as e:
                logger.error(f"❌ MCQ Manager initialization failed: {e}")
                self.mcq_manager_initializing = False
                self._handle_initialization_failure(str(e))
        
        # Run in background thread to avoid blocking
        import threading
        init_thread = threading.Thread(target=fast_initialize, name="MCQInit", daemon=True)
        init_thread.start()
    
    def _handle_initialization_failure(self, error_msg):
        """Handle MCQ Manager initialization failure gracefully"""
        self.mcq_manager_initializing = False
        self.mcq_manager_ready = False
        self.mcq_manager = None
        
        logger.error(f"❌ MCQ Manager initialization failed: {error_msg}")
        
        # Emit error status to UI - THREAD SAFE
        self.updateStatus.emit(f"⚠️ AI initialization failed - using fallback mode")
        
        # No fallback questions needed - let proper error handling work

    def _optimize_mcq_manager_for_speed(self):
        """Optimize MCQ manager for speed - Enhanced with GPU management"""
        try:
            if self.mcq_manager:
                logger.info("🚀 MCQ Manager optimization with enhanced GPU management...")
                
                # ✅ ENHANCED: Optimize GPU settings if available
                try:
                    if hasattr(self.mcq_manager, 'offline_generator') and self.mcq_manager.offline_generator:
                        # Set GPU optimization flags
                        ollama_gen = self.mcq_manager.offline_generator
                        if hasattr(ollama_gen, 'ollama_interface'):
                            ollama_interface = ollama_gen.ollama_interface
                            if hasattr(ollama_interface, 'generation_params'):
                                # Enhanced GPU optimization parameters
                                ollama_interface.generation_params.update({
                                    'num_gpu': -1,  # Use all available GPU layers
                                    'num_thread': 8,  # Optimize CPU threads
                                    'use_mmap': True,  # Memory mapping for efficiency
                                    'use_mlock': True,  # Lock memory to prevent swapping
                                    'numa': False,  # Disable NUMA for better GPU utilization
                                })
                                logger.info("✅ GPU optimization parameters applied")
                except Exception as gpu_opt_error:
                    logger.debug(f"GPU optimization failed: {gpu_opt_error}")
                
                logger.info("🚀 MCQ Manager optimized for speed (respecting user's online/offline choice)")
        except Exception as e:
            logger.error(f"❌ Failed to optimize MCQ manager: {e}")

    def _wait_for_mcq_manager_and_start_questions(self):
        """Wait for MCQ manager to be fully ready, then start question generation"""
        logger.info("⏳ Waiting for MCQ Manager to be fully ready...")
        
        # Use a timer to check every 500ms if the manager is ready - ENSURE MAIN THREAD
        self.ready_check_timer = QTimer(self)  # Explicitly set parent to ensure main thread
        self.ready_check_timer.timeout.connect(self._check_mcq_manager_ready)
        self.ready_check_attempts = 0
        # Allow up to 30 seconds (60 * 500 ms) for faster startup
        self.max_ready_attempts = 60  # 30 seconds max wait
        self.ready_check_timer.start(500)  # Check every 500ms
        
    def _check_mcq_manager_ready(self):
        """Check if MCQ manager is ready and start questions if so"""
        try:
            self.ready_check_attempts += 1
            
            mcq_manager = self._get_mcq_manager()
            if not mcq_manager:
                logger.warning(f"⚠️ MCQ Manager not available (attempt {self.ready_check_attempts})")
                return
                
            # Check if ANY generator (online or offline) is available and ready
            offline_available = mcq_manager.is_offline_available()
            online_available = mcq_manager.is_online_available()
            
            # Check offline generator readiness
            has_offline_generator = mcq_manager.offline_generator is not None
            offline_ready = mcq_manager.offline_generator.is_available() if has_offline_generator else False
            
            # Check online generator readiness
            has_online_generator = mcq_manager.online_generator is not None
            online_ready = mcq_manager.online_generator.is_available() if has_online_generator else False
            
            # Overall readiness depends on the mode
            is_offline_mode = mcq_manager.is_offline_mode() if hasattr(mcq_manager, 'is_offline_mode') else True
            generator_ready = (offline_ready if is_offline_mode else online_ready) or (offline_ready or online_ready)
            
            logger.info(f"🔍 Ready Check: offline_mode={is_offline_mode}, offline_ready={offline_ready}, online_ready={online_ready}, generator_ready={generator_ready}, buffer_size={len(self.question_buffer)}")
            
            # Proceed when generator is ready - don't wait for buffer to fill!
            if generator_ready:
                logger.info("✅ MCQ Manager is READY! Starting UI...")
                self.ready_check_timer.stop()
                
                # If buffer already has questions, show them immediately
                if len(self.question_buffer) > 0:
                    logger.info("📦 Buffer already has questions - showing immediately!")
                    # Use direct call instead of timer - faster and thread-safe
                    self._sendNextQuestion()
                else:
                    # Buffer empty - show loading state but don't block
                    logger.info("⏳ Buffer empty - UI will update when questions arrive")
                    # Start checking for questions to arrive
                    self._start_question_arrival_check()
                return
                
            # Check if we've exceeded max attempts
            if self.ready_check_attempts >= self.max_ready_attempts:
                logger.warning("⚠️ MCQ Manager initialization timeout - starting with fallback")
                self.ready_check_timer.stop()
                # Use direct call instead of timer
                self._sendNextQuestion()
                return
                
            logger.info(f"⏳ MCQ Manager still initializing... (attempt {self.ready_check_attempts}/{self.max_ready_attempts})")
            
        except Exception as e:
            logger.error(f"❌ Error checking MCQ manager ready state: {e}")
            self.ready_check_timer.stop()
            # Use direct call instead of timer
            self._sendNextQuestion()  # Fallback to starting anyway

    def _start_question_arrival_check(self):
        """Start checking for questions to arrive in the buffer"""
        self.question_check_timer = QTimer(self)  # Explicitly set parent to ensure main thread
        self.question_check_timer.timeout.connect(self._check_for_arrived_questions)
        self.question_check_attempts = 0
        self.max_question_check_attempts = 40  # 20 seconds max wait (40 * 500ms)
        self.question_check_timer.start(500)  # Check every 500ms
        
    def _check_for_arrived_questions(self):
        """Check if questions have arrived and show them"""
        try:
            self.question_check_attempts += 1
            
            if len(self.question_buffer) > 0:
                logger.info(f"📦 Questions arrived! Buffer size: {len(self.question_buffer)} - showing first question")
                self.question_check_timer.stop()
                self._sendNextQuestion()
                return
                
            if self.question_check_attempts >= self.max_question_check_attempts:
                logger.warning("⚠️ Question generation timeout - using REAL topic-specific fallback")
                self.question_check_timer.stop()
                # Generate a REAL topic-specific fallback question
                fallback = self._generate_fallback_question()
                if fallback:
                    self.question_buffer.append(fallback)
                    self._sendNextQuestion()
                return
                
            logger.debug(f"⏳ Still waiting for questions... (attempt {self.question_check_attempts}/{self.max_question_check_attempts})")
            
        except Exception as e:
            logger.error(f"❌ Error checking for questions: {e}")
            self.question_check_timer.stop()
            # Use REAL topic-specific fallback on error
            fallback = self._generate_fallback_question()
            if fallback:
                self.question_buffer.append(fallback)
                self._sendNextQuestion()
    
    def _start_fast_generation(self, num_questions=None):
        """Start fast parallel question generation - COMPLETELY NON-BLOCKING"""
        if not num_questions:
            num_questions = self.buffer_size
            
        with self.generation_lock:
            if self.pending_generations > 0:
                logger.info("⚡ Generation already in progress, skipping...")
                return
                
            # UNLEASH FULL PARALLEL POWER - No artificial limits!
            max_concurrent = min(num_questions, 15)  # Generate up to 15 questions in parallel for SPEED
            self.pending_generations = max_concurrent
            logger.info(f"🚀 Starting TURBO PARALLEL generation of {max_concurrent} questions (requested: {num_questions})")
            
            # Use full requested amount for maximum speed
            num_questions = max_concurrent
            
        try:
            # Check if MCQ manager is ready (non-blocking)
            mcq_manager = self._get_mcq_manager()
            if not mcq_manager:
                logger.info("⏳ MCQ Manager not ready yet - will retry when available")
                with self.generation_lock:
                    self.pending_generations = 0
                
                # Schedule retry when manager is ready - NO DELAYS!
                def retry_when_ready():
                    if self._get_mcq_manager():
                        logger.info("🔄 MCQ Manager ready - retrying generation...")
                        self._start_fast_generation(num_questions)
                    else:
                        # Still not ready, try again with proper timer
                        timer = QTimer(self)
                        timer.singleShot(50, retry_when_ready)  # Minimal delay
                        
                timer = QTimer(self)
                timer.singleShot(10, retry_when_ready)  # Almost instant retry
                return
                
            # Stop any existing generator
            if self.fast_generator and self.fast_generator.isRunning():
                self.fast_generator.stop()
                self.fast_generator.wait(100)  # Very short wait
                
            # Create and start new fast generator
            if not self.current_quiz:
                logger.warning("⚠️ No current quiz state - using defaults")
                quiz_params = {
                    "topic": "General Knowledge",
                    "difficulty": "medium",
                    "game_mode": "casual",
                    "submode": "mixed"
                }
            else:
                quiz_params = {
                    "topic": self.current_quiz["topic"],
                    "difficulty": self.current_quiz["difficulty"],
                    "game_mode": self.current_quiz["game_mode"],
                    "submode": self.current_quiz["submode"]
                }
            
            self.fast_generator = FastQuestionGenerator(mcq_manager, quiz_params, num_questions)
            self.fast_generator.questionGenerated.connect(self._on_fast_question_ready)
            self.fast_generator.batchCompleted.connect(self._on_fast_batch_completed)
            
            logger.info(f"🚀 Starting TURBO generation of {num_questions} questions...")
            self.fast_generator.start()
            
        except Exception as e:
            logger.error(f"❌ Failed to start fast generation: {e}")
            with self.generation_lock:
                self.pending_generations = 0
            
            # Generate REAL topic-specific fallback immediately
            logger.info("🔄 Using REAL topic-specific fallback due to generation failure")
            fallback = self._generate_fallback_question()
            if fallback:
                self.question_buffer.append(fallback)

    def _on_fast_question_ready(self, question_data):
        """Handle when a question is ready from fast generation WITH VALIDATION"""
        try:
            # ✅ CRITICAL FIX: Validate question data before adding to buffer
            if not self._validate_and_add_question(question_data):
                logger.warning("⚠️ Invalid question data rejected from buffer")
                return
            
            logger.info(f"⚡ NEW Validated question added to buffer (size: {len(self.question_buffer)})")
            
            with self.generation_lock:
                self.pending_generations = max(0, self.pending_generations - 1)
                
        except Exception as e:
            logger.error(f"❌ Failed to handle fast question: {e}")

    def _validate_and_add_question(self, question_data):
        """Validate question before adding to buffer"""
        try:
            # Basic data validation
            if not question_data or not isinstance(question_data, dict):
                logger.error("❌ Question data is None or not a dictionary")
                return False
            
            # Required fields validation
            required_fields = ['question', 'options', 'correct_answer']
            for field in required_fields:
                if field not in question_data:
                    logger.error(f"❌ Missing required field: {field}")
                    return False
            
            # Options validation
            options = question_data.get('options', [])
            if not isinstance(options, list) or len(options) != 4:
                logger.error(f"❌ Invalid options: expected 4 options, got {len(options) if isinstance(options, list) else 'non-list'}")
                return False
            
            # Correct answer validation
            correct_answer = question_data.get('correct_answer', '')
            if not correct_answer or correct_answer not in options:
                logger.error(f"❌ Correct answer '{correct_answer}' not in options: {options}")
                return False
            
            # Question text validation
            question_text = question_data.get('question', '').strip()
            if not question_text or len(question_text) < 10:
                logger.error(f"❌ Question text too short or empty: '{question_text}'")
                return False
            
            # Add correct_index if missing
            if 'correct_index' not in question_data:
                try:
                    question_data['correct_index'] = options.index(correct_answer)
                except ValueError:
                    logger.error(f"❌ Could not determine correct_index for answer: {correct_answer}")
                    return False
            
            # Validation passed - add to buffer
            self.question_buffer.append(question_data)
            logger.info(f"✅ Question validated and added to buffer: {question_text[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"❌ Question validation error: {e}")
            return False

    def _on_fast_batch_completed(self, num_generated):
        """Handle when fast generation batch is completed"""
        try:
            with self.generation_lock:
                self.pending_generations = 0
                
            logger.info(f"🏁 Fast generation completed: {num_generated} questions ready, buffer size: {len(self.question_buffer)}")
            
            # If buffer is still low, start another batch INSTANTLY
            if len(self.question_buffer) < self.min_buffer_threshold and self.current_quiz:
                logger.info("🚀 Buffer still low, starting INSTANT mega-refill...")
                timer = QTimer(self)
                timer.singleShot(5, lambda: self._start_fast_generation(self.buffer_size))  # Full buffer refill IMMEDIATELY
                
        except Exception as e:
            logger.error(f"❌ Failed to handle batch completion: {e}")

    def _format_question_data(self, mcq_result):
        """Format MCQ result into question data"""
        try:
            if hasattr(mcq_result, 'question'):
                question_text = mcq_result.question
                options = mcq_result.options
                correct_answer = mcq_result.correct_answer
                explanation = getattr(mcq_result, 'explanation', 'No explanation available.')
            elif isinstance(mcq_result, dict):
                question_text = mcq_result.get('question', '')
                options = list(mcq_result.get('options', {}).values()) if 'options' in mcq_result else []
                correct_answer = mcq_result.get('options', {}).get(mcq_result.get('correct', 'A'), options[0] if options else '')
                explanation = mcq_result.get('explanation', 'No explanation available.')
            else:
                return None
                
            return {
                "question": question_text,
                "options": options,
                "correct_answer": correct_answer,
                "explanation": explanation,
                "correct_index": options.index(correct_answer) if correct_answer in options else 0
            }
        except Exception as e:
            logger.error(f"Error formatting question data: {e}")
            return None
        
    def _filter_inappropriate_topic(self, topic):
        """Filter inappropriate topics and convert to educational equivalents"""
        # Use centralized filter utility to avoid code duplication
        from .utils.text_filter import filter_inappropriate_topic
        return filter_inappropriate_topic(topic)
    
    def _safe_extract_question_data(self, mcq_data):
        """Safely extract and validate question data from any generator format"""
        try:
            # Handle MCQResult object format
            if hasattr(mcq_data, 'question'):
                return {
                    "question": str(mcq_data.question),
                    "options": list(mcq_data.options),
                    "correct_answer": str(mcq_data.correct_answer),
                    "explanation": str(getattr(mcq_data, 'explanation', '')),
                    "correct_index": self._safe_find_correct_index(mcq_data.options, mcq_data.correct_answer)
                }
            
            # Handle dictionary format
            elif isinstance(mcq_data, dict):
                options_list = self._normalize_options(mcq_data.get("options", []))
                correct_answer = str(mcq_data.get("correct_answer", ""))
                
                return {
                    "question": str(mcq_data.get("question", "")),
                    "options": options_list,
                    "correct_answer": correct_answer,
                    "explanation": str(mcq_data.get("explanation", "")),
                    "correct_index": self._safe_find_correct_index(options_list, correct_answer)
                }
            
            logger.error(f"Unknown MCQ data format: {type(mcq_data)}")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting question data: {e}")
            return None

    def _normalize_options(self, options):
        """Normalize options to consistent list format"""
        if isinstance(options, dict):
            return list(options.values())
        elif isinstance(options, list):
            return [str(opt) for opt in options]
        else:
            logger.error(f"Invalid options format: {type(options)}")
            return ["Option A", "Option B", "Option C", "Option D"]

    def _safe_find_correct_index(self, options, correct_answer):
        """Safely find correct answer index with fallback logic"""
        try:
            # Direct match
            if correct_answer in options:
                return options.index(correct_answer)
            
            # Case-insensitive match
            correct_lower = correct_answer.lower().strip()
            for i, option in enumerate(options):
                if option.lower().strip() == correct_lower:
                    return i
            
            # Partial match
            for i, option in enumerate(options):
                if correct_lower in option.lower() or option.lower() in correct_lower:
                    return i
            
            logger.warning(f"Could not find correct answer '{correct_answer}' in options: {options}")
            return 0  # Safe fallback
            
        except Exception as e:
            logger.error(f"Error finding correct index: {e}")
            return 0



    def _create_offline_emergency_question(self, topic, difficulty):
        """DISABLED: No hardcoded emergency questions - AI models only"""
        logger.error("🚫 HARDCODED EMERGENCY QUESTIONS COMPLETELY DISABLED")
        logger.error(f"❌ Cannot create emergency question for topic '{topic}' with difficulty '{difficulty}'")
        logger.error("🚨 APPLICATION MUST USE AI MODELS ONLY - NO HARDCODED CONTENT")
        raise Exception(f"Emergency questions disabled for '{topic}' - AI model generation required")




    def _sendNextQuestion(self):
        """Send next question to frontend - DEPRECATED, use loadNextQuestion instead"""
        self.loadNextQuestion()

    @pyqtSlot()
    def getApiStatus(self):
        """Get API provider status"""
        try:
            mcq_manager = self._get_mcq_manager()
            if mcq_manager and mcq_manager.online_generator:
                stats = mcq_manager.online_generator.get_performance_stats()
                
                # Convert to frontend format
                providers = []
                for provider_name in ['openai', 'anthropic', 'gemini', 'groq', 'openrouter']:
                    status = 'available' if provider_name in stats.get('available_providers', []) else 'unavailable'
                    providers.append({
                        'name': provider_name.title(),
                        'status': status
                    })
                
                logger.info(f"📊 API Status: {len(stats.get('available_providers', []))} providers available")
            else:
                # No online generator available
                providers = [
                    {'name': 'OpenAI', 'status': 'unavailable'},
                    {'name': 'Anthropic', 'status': 'unavailable'},
                    {'name': 'Gemini', 'status': 'unavailable'},
                    {'name': 'Groq', 'status': 'unavailable'},
                    {'name': 'OpenRouter', 'status': 'unavailable'}
                ]
                
        except Exception as e:
            logger.error(f"Error getting API status: {e}")
            providers = [
                {'name': 'OpenAI', 'status': 'unknown'},
                {'name': 'Anthropic', 'status': 'unknown'},
                {'name': 'Gemini', 'status': 'unknown'},
                {'name': 'Groq', 'status': 'unknown'},
                {'name': 'OpenRouter', 'status': 'unknown'}
            ]

    def _completeQuiz(self):
        """Complete the current quiz"""
        # Stop any running generation
        if self.fast_generator and self.fast_generator.isRunning():
            self.fast_generator.stop()
            
        results = {
            "score": self.current_quiz["score"],
            "total": self.current_quiz["total_answered"],
            "percentage": (self.current_quiz["score"] / max(self.current_quiz["total_answered"], 1)) * 100,
            "topic": self.current_quiz["topic"]
        }
        
        self.quizCompleted.emit(results)

    @pyqtSlot(str)
    def navigate(self, screen):
        """Navigate to a different screen"""
        logger.info(f"Navigating to: {screen}")
        if screen == "quiz":
            self.updateStatus.emit("Status: Loading quiz...")
        else:
            self.updateStatus.emit(f"Loading {screen}...")

    @pyqtSlot()
    def showSettings(self):
        """Show settings dialog"""
        logger.info("Opening settings...")
    
    # 🏆 QUESTION HISTORY & REVIEW FUNCTIONALITY
    
    @pyqtSlot(result=str)
    def getQuestionHistory(self):
        """Get recent questions from history for review"""
        try:
            from .core.question_history_storage import QuestionHistoryStorage
            
            if not hasattr(self, 'question_history_storage'):
                self.question_history_storage = QuestionHistoryStorage()
            
            recent_questions = self.question_history_storage.get_recent_questions(limit=50)
            
            result = {
                "success": True,
                "questions": recent_questions,
                "total_count": len(recent_questions)
            }
            
            json_result = json.dumps(result)
            logger.info(f"📚 Retrieved {len(recent_questions)} questions from history")
            logger.info(f"🔍 JSON result: {json_result[:200]}...")
            logger.info(f"🔍 JSON result type: {type(json_result)}")
            return json_result
            
        except Exception as e:
            logger.error(f"❌ Failed to get question history: {e}")
            error_result = json.dumps({"success": False, "error": str(e), "questions": []})
            logger.info(f"🔍 Error JSON result: {error_result}")
            return error_result
    
    @pyqtSlot(str, result=str)
    def getQuestionsByTopic(self, topic):
        """Get questions filtered by topic"""
        try:
            from .core.question_history_storage import QuestionHistoryStorage
            
            if not hasattr(self, 'question_history_storage'):
                self.question_history_storage = QuestionHistoryStorage()
            
            questions = self.question_history_storage.get_questions_by_topic(topic, limit=50)
            
            result = {
                "success": True,
                "questions": questions,
                "topic": topic,
                "total_count": len(questions)
            }
            
            logger.info(f"📚 Retrieved {len(questions)} questions for topic: {topic}")
            return json.dumps(result)
            
        except Exception as e:
            logger.error(f"❌ Failed to get questions by topic: {e}")
            return json.dumps({"success": False, "error": str(e), "questions": []})
    
    @pyqtSlot(str, result=str)
    def getQuestionsByDifficulty(self, difficulty):
        """Get questions filtered by difficulty"""
        try:
            from .core.question_history_storage import QuestionHistoryStorage
            
            if not hasattr(self, 'question_history_storage'):
                self.question_history_storage = QuestionHistoryStorage()
            
            questions = self.question_history_storage.get_questions_by_difficulty(difficulty, limit=50)
            
            result = {
                "success": True,
                "questions": questions,
                "difficulty": difficulty,
                "total_count": len(questions)
            }
            
            logger.info(f"📊 Retrieved {len(questions)} questions for difficulty: {difficulty}")
            return json.dumps(result)
            
        except Exception as e:
            logger.error(f"❌ Failed to get questions by difficulty: {e}")
            return json.dumps({"success": False, "error": str(e), "questions": []})
    
    @pyqtSlot(result=str)
    def getQuestionStatistics(self):
        """Get comprehensive statistics about stored questions"""
        try:
            from .core.question_history_storage import QuestionHistoryStorage
            
            if not hasattr(self, 'question_history_storage'):
                self.question_history_storage = QuestionHistoryStorage()
            
            stats = self.question_history_storage.get_statistics()
            
            result = {
                "success": True,
                "statistics": stats
            }
            
            logger.info(f"📊 Retrieved question statistics: {stats.get('total_questions', 0)} total questions")
            return json.dumps(result)
            
        except Exception as e:
            logger.error(f"❌ Failed to get question statistics: {e}")
            return json.dumps({"success": False, "error": str(e), "statistics": {}})
    
    @pyqtSlot(str, result=str)
    def searchQuestions(self, query):
        """Search questions by content"""
        try:
            from .core.question_history_storage import QuestionHistoryStorage
            
            if not hasattr(self, 'question_history_storage'):
                self.question_history_storage = QuestionHistoryStorage()
            
            questions = self.question_history_storage.search_questions(query, limit=50)
            
            result = {
                "success": True,
                "questions": questions,
                "query": query,
                "total_count": len(questions)
            }
            
            logger.info(f"🔍 Found {len(questions)} questions matching: {query}")
            return json.dumps(result)
            
        except Exception as e:
            logger.error(f"❌ Failed to search questions: {e}")
            return json.dumps({"success": False, "error": str(e), "questions": []})

    @pyqtSlot()
    def exitApp(self):
        """Exit the application with proper resource cleanup"""
        logger.info("🚪 Exiting application with graceful cleanup...")
        
        try:
            # 🔥 CRITICAL: Perform proper resource cleanup before exit
            logger.info("🧹 Starting graceful resource cleanup...")
            
            # Cancel all active thread-safe operations
            if hasattr(self, 'thread_safe_inference'):
                from .core.thread_safe_inference import shutdown_thread_safe_inference
                shutdown_thread_safe_inference()
                logger.info("✅ Thread-safe inference shutdown complete")
            
            # Shutdown unified inference manager
            from .core.unified_inference_manager import shutdown_unified_inference
            shutdown_unified_inference()
            logger.info("✅ Unified inference manager shutdown complete")
            
            # Emergency resource cleanup
            if hasattr(self, 'resource_manager'):
                self.resource_manager.emergency_cleanup()
                logger.info("✅ Resource manager emergency cleanup complete")
            
            logger.info("✅ Graceful cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")
        finally:
            # Exit regardless of cleanup success
            if QCoreApplication.instance():
                QCoreApplication.instance().quit()

    def _emergency_stop_generation(self, freeze_duration=None):
        """Emergency action: Stop all question generation"""
        freeze_text = f"{freeze_duration:.2f}s" if freeze_duration else "unknown"
        logger.error(f"🚨 EMERGENCY: Stopping all generation due to UI freeze ({freeze_text})")
        
        try:
            # Stop fast generator immediately
            if self.fast_generator and self.fast_generator.isRunning():
                logger.info("⚡ Emergency stopping FastQuestionGenerator...")
                self.fast_generator.stop()
                self.fast_generator.terminate()  # Force terminate
                
            # Stop all timers
            if hasattr(self, 'ready_check_timer') and self.ready_check_timer.isActive():
                self.ready_check_timer.stop()
                logger.info("⏹️ Emergency stopped ready check timer")
                
            if hasattr(self, 'question_check_timer') and self.question_check_timer.isActive():
                self.question_check_timer.stop()
                logger.info("⏹️ Emergency stopped question check timer")
                
            # ✅ ENHANCED: Clear GPU memory if available
            self._emergency_clear_gpu_memory()
            
            # Clear pending generations
            with self.generation_lock:
                self.pending_generations = 0
                
            logger.info("✅ Emergency generation stop completed")
            
        except Exception as e:
            logger.error(f"❌ Emergency generation stop failed: {e}")

    def _emergency_clear_gpu_memory(self):
        """
        🔥 OPTIMIZED EMERGENCY GPU MEMORY CLEARING
        
        CRITICAL FIX: More effective memory management with proper resource cleanup
        and prevention of cascading memory failures.
        """
        try:
            logger.warning("🔥 EMERGENCY: Starting optimized GPU memory recovery")
            
            # CRITICAL FIX: Prevent memory clearing cascade by checking if already in progress
            if hasattr(self, '_memory_clearing_in_progress') and self._memory_clearing_in_progress:
                logger.warning("⚠️ Memory clearing already in progress, skipping")
                return
            
            self._memory_clearing_in_progress = True
            
            try:
                # STEP 1: Stop all generation activities immediately
                if hasattr(self, 'fast_generator') and self.fast_generator:
                    if self.fast_generator.isRunning():
                        logger.warning("🛑 Gracefully stopping FastQuestionGenerator")
                        self.fast_generator.stop()
                        
                        # Wait for graceful shutdown, then force if needed
                        if not self.fast_generator.wait(3000):  # 3 second timeout
                            logger.warning("🛑 Force-terminating FastQuestionGenerator")
                            self.fast_generator.terminate()
                        
                        self.fast_generator = None
                        logger.info("✅ Question generator stopped")
                
                # STEP 2: Enhanced unified resource manager cleanup
                try:
                    from .core.unified_resource_manager import get_unified_resource_manager
                    resource_manager = get_unified_resource_manager()
                    if hasattr(resource_manager, 'emergency_cleanup'):
                        gpu_stats = resource_manager.emergency_cleanup()
                        logger.info(f"✅ Emergency resource cleanup: {gpu_stats.get('cleared_mb', 0):.1f}MB freed")
                    else:
                        gpu_stats = resource_manager.clear_gpu_memory()
                        logger.info(f"✅ GPU memory cleared via resource manager: {gpu_stats.get('cleared_mb', 0):.1f}MB freed")
                except Exception as e:
                    logger.debug(f"Resource manager cleanup failed: {e}")
                
                # STEP 3: Enhanced MCQ manager resource cleanup
                try:
                    mcq_manager = self._get_mcq_manager()
                    if mcq_manager:
                        # CRITICAL FIX: More thorough MCQ manager cleanup
                        if hasattr(mcq_manager, '_unified_manager'):
                            unified_mgr = mcq_manager._unified_manager
                            if hasattr(unified_mgr, 'emergency_cleanup'):
                                unified_mgr.emergency_cleanup()
                                logger.info("✅ Unified manager emergency cleanup complete")
                        
                        # Clear all generator caches
                        for gen_attr in ['offline_generator', 'online_generator', '_rag_engine']:
                            if hasattr(mcq_manager, gen_attr):
                                generator = getattr(mcq_manager, gen_attr)
                                if generator:
                                    for method in ['clear_model_cache', 'clear_cache', 'cleanup']:
                                        if hasattr(generator, method):
                                            try:
                                                getattr(generator, method)()
                                                logger.info(f"✅ {gen_attr} cache cleared")
                                                break
                                            except Exception as method_error:
                                                logger.debug(f"Cache clear method {method} failed: {method_error}")
                                                
                except Exception as e:
                    logger.debug(f"MCQ manager cleanup failed: {e}")
                
                # STEP 4: Clear application memory structures
                memory_attrs = ['question_buffer', 'persistent_cache', 'answered_questions_history', 'question_history']
                for attr in memory_attrs:
                    if hasattr(self, attr) and hasattr(getattr(self, attr), 'clear'):
                        try:
                            getattr(self, attr).clear()
                            logger.debug(f"✅ {attr} cleared")
                        except Exception as clear_error:
                            logger.debug(f"Failed to clear {attr}: {clear_error}")
                
                # STEP 5: Enhanced PyTorch GPU memory management
                try:
                    import torch
                    if torch.cuda.is_available():
                        # CRITICAL FIX: More thorough GPU memory clearing
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()
                        
                        # Advanced GPU memory management
                        try:
                            torch.cuda.ipc_collect()
                            torch.cuda.reset_peak_memory_stats()
                            torch.cuda.reset_accumulated_memory_stats()
                        except AttributeError:
                            pass  # Methods might not be available in all PyTorch versions
                        
                        # Memory statistics
                        allocated = torch.cuda.memory_allocated() / 1e9
                        reserved = torch.cuda.memory_reserved() / 1e9
                        logger.info(f"🔥 GPU memory after cleanup: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved")
                        
                except ImportError:
                    logger.debug("PyTorch not available for GPU cleanup")
                except Exception as e:
                    logger.debug(f"PyTorch GPU cleanup failed: {e}")
                
                # STEP 6: Aggressive Python memory cleanup
                try:
                    import gc
                    import sys
                    
                    # Multiple garbage collection passes
                    total_collected = 0
                    for i in range(3):
                        collected = gc.collect()
                        total_collected += collected
                        if collected == 0:
                            break
                    
                    logger.info(f"✅ Python garbage collection: {total_collected} objects collected")
                    
                    # Force finalization of pending objects
                    if hasattr(gc, 'collect_finalizers'):
                        gc.collect_finalizers()
                        
                except Exception as e:
                    logger.debug(f"Garbage collection failed: {e}")
                
                # STEP 7: Reset resource monitoring
                if hasattr(self, 'resource_manager') and self.resource_manager:
                    try:
                        if hasattr(self.resource_manager, 'reset_monitoring'):
                            self.resource_manager.reset_monitoring()
                        logger.info("✅ Resource monitoring reset")
                    except Exception as resource_error:
                        logger.debug(f"Resource monitoring reset failed: {resource_error}")
                
                logger.info("✅ Optimized emergency memory clearing completed successfully")
                
            finally:
                # CRITICAL FIX: Always reset the clearing flag
                self._memory_clearing_in_progress = False
            
        except Exception as e:
            logger.error(f"❌ Emergency memory clearing failed: {e}")
            # CRITICAL FIX: Ensure flag is always reset even on failure
            if hasattr(self, '_memory_clearing_in_progress'):
                self._memory_clearing_in_progress = False
            
            # Try minimal fallback clearing
            try:
                import gc
                import torch
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                logger.info("🆘 Minimal fallback memory cleanup attempted")
            except Exception:
                logger.error("🆘 Even minimal memory cleanup failed")

    def _emergency_reset_timers(self, freeze_duration=None):
        """Emergency action: Reset all timers - ENHANCED"""
        freeze_text = f"{freeze_duration:.2f}s" if freeze_duration else "unknown"
        logger.error(f"🚨 EMERGENCY: Resetting timers due to UI freeze ({freeze_text})")
        
        try:
            # ✅ ENHANCED: Extended list of timer attributes to check and reset
            timer_attrs = [
                'ready_check_timer', 'question_check_timer', 'question_delivery_timer', 
                'unified_delivery_timer', 'auto_advance_timer', 'status_update_timer',
                'performance_monitor_timer', 'cache_cleanup_timer'
            ]
            
            for timer_attr in timer_attrs:
                if hasattr(self, timer_attr):
                    timer = getattr(self, timer_attr)
                    if timer and hasattr(timer, 'isActive') and timer.isActive():
                        timer.stop()
                        logger.info(f"⏹️ Reset timer: {timer_attr}")
            
            # ✅ ENHANCED: Reset delivery state
            self.unified_delivery_attempts = 0
            
            # ✅ ENHANCED: Restart unified system
            QTimer.singleShot(1000, self._start_unified_delivery_system)  # Restart after 1 second
            
            logger.info("✅ Emergency timer reset completed")
            
        except Exception as e:
            logger.error(f"❌ Emergency timer reset failed: {e}")

    @pyqtSlot()
    def getUIPerformanceStats(self):
        """Get UI performance statistics for frontend"""
        try:
            from .ui_responsiveness_monitor import get_ui_performance_stats
            stats = get_ui_performance_stats()
            logger.info(f"📊 UI Performance Stats: {stats}")
            
            # Emit performance data to frontend
            self.updateStatus.emit(f"UI Performance: {stats.get('avg_response_time', 0):.1f}ms avg response")
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Failed to get UI performance stats: {e}")
            return {"error": str(e)}
    
    @pyqtSlot()
    def logUIPerformanceReport(self):
        """Log detailed UI performance report"""
        try:
            if self.ui_monitor:
                self.ui_monitor.log_performance_report()
            else:
                logger.warning("⚠️ UI monitor not available for performance report")
                
        except Exception as e:
            logger.error(f"❌ Failed to log performance report: {e}")

    def _start_question_delivery_system(self):
        """Unified question delivery system - eliminates timer conflicts"""
        # Stop any existing delivery systems
        self._stop_all_delivery_timers()
        
        # Single unified delivery timer
        self.unified_delivery_timer = QTimer(self)
        self.unified_delivery_timer.timeout.connect(self._unified_delivery_check)
        self.unified_delivery_timer.start(200)  # Check every 200ms
        logger.info("🚀 Unified delivery system started")

    def _stop_all_delivery_timers(self):
        """Stop all delivery-related timers to prevent conflicts"""
        timer_attrs = ['ready_check_timer', 'question_check_timer', 'question_delivery_timer', 'unified_delivery_timer']
        for attr in timer_attrs:
            if hasattr(self, attr):
                timer = getattr(self, attr)
                if timer and timer.isActive():
                    timer.stop()
                    logger.info(f"⏹️ Stopped timer: {attr}")

    def _unified_delivery_check(self):
        """Unified check for all delivery conditions - ENHANCED"""
        try:
            # ✅ ENHANCED: Check if MCQ manager is ready
            if not self.mcq_manager_ready and self.mcq_manager_initializing:
                logger.debug("⏳ MCQ Manager still initializing...")
                return  # Still initializing
            
            # ✅ ENHANCED: Check if we have questions ready to deliver
            if self.question_buffer and not self._has_pending_question():
                logger.info("📦 Questions available - delivering...")
                self._deliver_next_question()
                return
            
            # ✅ ENHANCED: Check if we need to start generation
            if (self.mcq_manager_ready and 
                len(self.question_buffer) < self.min_buffer_threshold and 
                self.current_quiz and 
                not self.pending_generations):
                logger.info("🚀 Starting generation to refill buffer...")
                self._start_fast_generation()
                return
                
            # ✅ ENHANCED: Check for timeout scenarios
            if hasattr(self, 'unified_delivery_attempts'):
                self.unified_delivery_attempts += 1
                if self.unified_delivery_attempts > 120:  # 60 seconds max
                    logger.warning("⚠️ Unified delivery timeout - using fallback")
                    self._handle_delivery_timeout()
                    return
            else:
                self.unified_delivery_attempts = 0
                
            logger.debug("🔄 Unified delivery check complete - continuing monitoring...")
            
        except Exception as e:
            logger.error(f"❌ Unified delivery check failed: {e}")
            self._handle_delivery_error(e)

    def _handle_delivery_timeout(self):
        """Handle delivery timeout scenarios"""
        try:
            logger.warning("🚨 Delivery timeout detected - implementing recovery...")
            
            # Stop all timers to prevent conflicts
            self._stop_all_delivery_timers()
            
            # Reset state
            with self.generation_lock:
                self.pending_generations = 0
            
            # Try emergency fallback
            if self.current_quiz:
                fallback = self._generate_fallback_question()
                if fallback:
                    self.question_buffer.append(fallback)
                    self._deliver_next_question()
                else:
                    # Ultimate fallback - show error state
                    self.errorOccurred.emit("Question generation timed out. Please check your AI setup.")
            
        except Exception as e:
            logger.error(f"❌ Delivery timeout handling failed: {e}")

    def _handle_delivery_error(self, error):
        """Handle delivery errors gracefully"""
        try:
            logger.error(f"🚨 Delivery error: {error}")
            
            # Stop all timers
            self._stop_all_delivery_timers()
            
            # Reset generation state
            with self.generation_lock:
                self.pending_generations = 0
            
            # Emit error to UI
            self.errorOccurred.emit(f"Question delivery error: {str(error)}")
            
        except Exception as e:
            logger.error(f"❌ Error handling failed: {e}")

    def _start_unified_delivery_system(self):
        """Start the unified delivery system - ENHANCED"""
        try:
            # ✅ ENHANCED: Stop any existing timers first
            self._stop_all_delivery_timers()
            
            # ✅ ENHANCED: Reset delivery attempts
            self.unified_delivery_attempts = 0
            
            # ✅ ENHANCED: Start unified timer
            self.unified_delivery_timer = QTimer(self)
            self.unified_delivery_timer.timeout.connect(self._unified_delivery_check)
            self.unified_delivery_timer.start(500)  # Check every 500ms
            
            logger.info("🚀 Unified delivery system started")
            
        except Exception as e:
            logger.error(f"❌ Failed to start unified delivery system: {e}")

    def _has_pending_question(self) -> bool:
        """Check if there's a question currently being displayed"""
        return (self.question_history and 
                len(self.question_history) > 0 and 
                not self.question_history[-1].get("answered", False))
    
    def _start_question_delivery(self):
        """Start the unified question delivery system (replaces old timer-based system)"""
        self._start_question_delivery_system()
        
    def _deliver_next_question(self):
        """Deliver the next question from buffer instantly"""
        try:
            if not self.question_buffer:
                # No questions available, keep loading state
                logger.info("⏳ No questions ready yet, keeping loading state")
                return
                
            # Get next question from buffer
            question_data = self.question_buffer.pop(0)
            logger.info(f"⚡ Delivering question from buffer (remaining: {len(self.question_buffer)})")
            
            # Replace loading question or add to history
            if (self.question_history and 
                self.question_history[-1].get("is_loading", False)):
                # Replace loading question
                self.question_history[-1] = question_data
                self.current_question_index = len(self.question_history) - 1
            else:
                # Add new question
                self.question_history.append(question_data)
                self.current_question_index = len(self.question_history) - 1
                
            # Add question metadata
            question_data["question_number"] = self.current_quiz["total_answered"] + 1
            question_data["total_questions"] = self.current_quiz["num_questions"]
            question_data["is_loading"] = False
            
            # Send to frontend IMMEDIATELY
            self.questionReceived.emit(question_data)
            logger.info("✅ INSTANT: Real question delivered to UI")
            
            # Update status
            self.updateStatus.emit(f"Question {question_data['question_number']} of {question_data['total_questions']} ready")
            
        except Exception as e:
            logger.error(f"❌ Error delivering question: {e}")
            logger.info("🚀 Error delivering - waiting for proper question generation")

    def _load_question_cache(self):
        """Load cached questions from previous session"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                    self.persistent_cache = cache_data.get('questions', [])
                    self.cache_topic = cache_data.get('topic')
                    self.cache_difficulty = cache_data.get('difficulty')
                    self.cache_game_mode = cache_data.get('game_mode')
                    self.cache_submode = cache_data.get('submode')
                    
                if self.persistent_cache:
                    logger.info(f"💚 Loaded {len(self.persistent_cache)} cached questions from previous session")
                    logger.info(f"📋 Cache params: topic='{self.cache_topic}', difficulty='{self.cache_difficulty}', mode='{self.cache_game_mode}', submode='{self.cache_submode}'")
                else:
                    logger.info("📋 Cache file exists but no questions found")
            else:
                logger.info("📋 No question cache file found - first time or cleared cache")
        except Exception as e:
            logger.error(f"❌ Error loading cached questions: {e}")
            self.persistent_cache = []

    def _load_answered_questions_history(self):
        """Load previously answered questions to prevent repeats"""
        try:
            settings_file = Path("user_data/user_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.answered_questions_history = settings.get('answered_questions_history', [])
                    
                if self.answered_questions_history:
                    logger.info(f"🔒 Loaded {len(self.answered_questions_history)} previously answered questions for filtering")
                else:
                    logger.info("🔒 No previous answered questions found - all questions will be new")
            else:
                logger.info("🔒 No user settings file found - all questions will be new")
                self.answered_questions_history = []
                
        except Exception as e:
            logger.error(f"❌ Error loading answered questions history: {e}")
            self.answered_questions_history = []

    def _is_question_already_answered(self, question_data):
        """Check if a question has been answered before to prevent repeats"""
        try:
            if not self.answered_questions_history:
                return False
                
            question_text = question_data.get('question', '').strip().lower()
            if not question_text:
                return False
                
            # Check against all previously answered questions
            for answered in self.answered_questions_history:
                answered_question = answered.get('question_data', {}).get('question_text', '').strip().lower()
                
                # Exact match check
                if question_text == answered_question:
                    logger.info(f"🔒 DUPLICATE DETECTED: Question already answered before")
                    logger.info(f"🔒 Question: {question_text[:80]}...")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Error checking for duplicate question: {e}")
            return False  # If error, allow the question

    def _filter_answered_questions(self, questions_list):
        """Filter out questions that have been answered before"""
        try:
            if not self.answered_questions_history or not questions_list:
                return questions_list
                
            filtered_questions = []
            duplicates_removed = 0
            
            for question in questions_list:
                if not self._is_question_already_answered(question):
                    filtered_questions.append(question)
                else:
                    duplicates_removed += 1
                    
            if duplicates_removed > 0:
                logger.info(f"🔒 FILTERED OUT {duplicates_removed} duplicate questions!")
                logger.info(f"🔒 Remaining new questions: {len(filtered_questions)}")
            else:
                logger.info(f"🔒 All {len(questions_list)} questions are new - no duplicates found")
                
            return filtered_questions
            
        except Exception as e:
            logger.error(f"❌ Error filtering answered questions: {e}")
            return questions_list  # If error, return original list

    def _add_to_answered_history(self, question_data, user_answer, is_correct):
        """Add answered question to history to prevent future repeats"""
        try:
            # Create the answered question entry
            answered_entry = {
                "question_data": {
                    "question_text": question_data.get("question", ""),
                    "options": "|".join(question_data.get("options", [])),
                    "correct_option_letter": chr(65 + question_data.get("correct_index", 0)),
                    "explanation": question_data.get("explanation", "")
                },
                "user_selected_letter": chr(65 + user_answer) if isinstance(user_answer, int) else str(user_answer),
                "user_selected_text": question_data.get("options", [])[user_answer] if isinstance(user_answer, int) and user_answer < len(question_data.get("options", [])) else "",
                "is_correct": is_correct
            }
            
            # Add to memory
            self.answered_questions_history.append(answered_entry)
            
            # Save to user settings file
            try:
                settings_file = Path("user_data/user_settings.json")
                if settings_file.exists():
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                else:
                    settings = {}
                
                # Update the answered questions history
                if 'answered_questions_history' not in settings:
                    settings['answered_questions_history'] = []
                
                settings['answered_questions_history'].append(answered_entry)
                
                # Save back to file
                with open(settings_file, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, indent=2, ensure_ascii=False)
                
                logger.info(f"🔒 Added answered question to history (total: {len(settings['answered_questions_history'])})")
                
            except Exception as e:
                logger.error(f"❌ Failed to save answered question to settings: {e}")
                
        except Exception as e:
            logger.error(f"❌ Failed to add question to answered history: {e}")

    def _save_question_cache(self):
        """Save unused questions to cache for next session"""
        try:
            if not self.current_quiz:
                return
                
            # Combine unused buffer questions with any remaining persistent cache
            unused_questions = []
            
            # Add questions from current buffer that haven't been shown
            unused_questions.extend(self.question_buffer)
            
            # Add any persistent cache questions that match current quiz and weren't used
            if self._cache_matches_current_quiz():
                unused_questions.extend(self.persistent_cache)
            
            if unused_questions:
                cache_data = {
                    'questions': unused_questions,
                    'topic': self.current_quiz['topic'],
                    'difficulty': self.current_quiz['difficulty'],
                    'game_mode': self.current_quiz['game_mode'],
                    'submode': self.current_quiz['submode'],
                    'saved_at': time.time()
                }
                
                with open(self.cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, indent=2, ensure_ascii=False)
                    
                logger.info(f"💾 Saved {len(unused_questions)} unused questions to cache for next session")
                logger.info(f"💚 GPU power will be saved when you return with same quiz parameters!")
            else:
                # No unused questions, clear the cache
                if self.cache_file.exists():
                    self.cache_file.unlink()
                logger.info("🗑️ No unused questions to cache - cleared old cache")
                
        except Exception as e:
            logger.error(f"❌ Error saving question cache: {e}")

    def _cache_matches_current_quiz(self):
        """Check if cached questions match current quiz parameters"""
        if not self.current_quiz or not self.persistent_cache:
            return False
        
        # More flexible matching - allow partial matches for better cache usage
        topic_match = (
            self.cache_topic == self.current_quiz['topic'] or
            (self.cache_topic and self.current_quiz['topic'] and 
             self.cache_topic.lower() in self.current_quiz['topic'].lower()) or
            (self.cache_topic and self.current_quiz['topic'] and 
             self.current_quiz['topic'].lower() in self.cache_topic.lower())
        )
        
        difficulty_match = (
            self.cache_difficulty == self.current_quiz['difficulty'] or
            not self.cache_difficulty  # Use any cached questions if no difficulty specified
        )
        
        # Use cached questions more liberally for better performance
        return topic_match and difficulty_match

    def _use_cached_questions(self):
        """Use cached questions from previous session"""
        try:
            if not self.persistent_cache:
                logger.info("📋 No cached questions available")
                return False
                
            # Filter out answered questions from cache
            logger.info(f"🔒 Filtering {len(self.persistent_cache)} cached questions for duplicates...")
            filtered_cache = self._filter_answered_questions(self.persistent_cache[:self.buffer_size])
            
            if not filtered_cache:
                logger.info("🔒 All cached questions were duplicates - need to generate new ones")
                return False
                
            # Add filtered questions to buffer
            questions_added = 0
            for cached_question in filtered_cache:
                try:
                    # Ensure cached question has proper format
                    if isinstance(cached_question, dict) and "question" in cached_question:
                        self.question_buffer.append(cached_question)
                        questions_added += 1
                except Exception as e:
                    logger.warning(f"⚠️ Skipping invalid cached question: {e}")
                    
            if questions_added > 0:
                logger.info(f"💚 Loaded {questions_added} NEW cached questions instantly (duplicates filtered)!")
                # Remove used questions from cache
                self.persistent_cache = self.persistent_cache[len(filtered_cache):]
                return True
            else:
                logger.info("📋 No valid cached questions found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to use cached questions: {e}")
            return False

    def _should_generate_new_questions(self):
        """Determine if we need to use GPU to generate new questions"""
        # First check if we can use cached questions
        if self._use_cached_questions():
            # We used cached questions, check if we still need more
            if len(self.question_buffer) >= self.min_buffer_threshold:
                logger.info("💚 Sufficient cached questions available - GPU generation skipped!")
                return False
            else:
                logger.info("⚡ Some cached questions used, but need more - will generate additional questions")
                return True
        
        # No cached questions available, need to generate
        logger.info("🔥 No matching cached questions - will generate with GPU")
        return True

    def _preload_popular_topic_caches(self):
        """Proactively load cache for popular topics to enable instant question delivery"""
        try:
            logger.info("🚀 Preloading popular topic caches for instant access...")
            
            # Popular topics that users commonly search for
            popular_topics = [
                'physics', 'chemistry', 'mathematics', 'magnetism', 'electricity',
                'thermodynamics', 'mechanics', 'optics', 'biology', 'algebra',
                'calculus', 'geometry', 'organic chemistry', 'atomic structure',
                'electromagnetic induction', 'quantum mechanics', 'general knowledge'
            ]
            
            # For each popular topic, create a few fallback questions that can be used instantly
            for topic in popular_topics:
                try:
                    # Create a mini-cache for this topic
                    topic_cache = []
                    
                    # Generate 3-5 topic-specific questions for instant access
                    for i in range(3):
                        fallback_question = self._generate_topic_specific_fallback(topic)
                        if fallback_question:
                            topic_cache.append(fallback_question)
                    
                    # Store in a separate popular topics cache (not the main cache)
                    if not hasattr(self, 'popular_topics_cache'):
                        self.popular_topics_cache = {}
                    
                    if topic_cache:
                        self.popular_topics_cache[topic.lower()] = topic_cache
                        logger.info(f"📚 Preloaded {len(topic_cache)} questions for '{topic}'")
                
                except Exception as e:
                    logger.warning(f"⚠️ Failed to preload cache for '{topic}': {e}")
            
            logger.info(f"✅ Popular topics cache preloaded for {len(self.popular_topics_cache)} topics")
            
        except Exception as e:
            logger.error(f"❌ Failed to preload popular topic caches: {e}")
    
    def _generate_topic_specific_fallback(self, topic):
        """DISABLED: No more topic-specific fallback - pure AI only"""
        logger.info(f"🚫 Topic-specific fallback DISABLED for '{topic}' - waiting for real AI")
        return None

    def _generate_fallback_question(self):
        """NO FALLBACK QUESTIONS - AI ONLY"""
        logger.error("❌ AI generation failed - NO FALLBACK SYSTEM")
        return None

    def _generate_questions_fallback(self):
        """DISABLED: No fallback generation - AI models only"""
        logger.error("🚫 FALLBACK GENERATION COMPLETELY DISABLED")
        logger.error("❌ Cannot use fallback generation - AI model generation required")
        logger.error("🚨 APPLICATION MUST USE AI MODELS ONLY - NO FALLBACK CONTENT")
        # Instead of fallback, emit error
        self.errorOccurred.emit("AI model generation failed. Please check your AI setup and try again.")

    @pyqtSlot(str)
    def startQuiz(self, params):
        """🚀 INSTANT QUIZ START - No blocking operations on UI thread"""
        try:
            quiz_params = json.loads(params)

            # Extract and validate parameters
            topic = quiz_params.get("topic", "General Knowledge")
            num_questions = quiz_params.get("num_questions", 2)
            mode = quiz_params.get("mode", "auto")

            logger.info(f"🚀 INSTANT QUIZ START: {num_questions} questions about '{topic}' in {mode} mode")

            # 🚀 CRITICAL FIX: ZERO blocking operations on UI thread
            # Store params and immediately return control to UI

            # Initialize quiz state (lightweight operation)
            self.current_quiz = {
                "topic": topic,
                "mode": mode,
                "game_mode": quiz_params.get("game_mode", "casual"),
                "submode": quiz_params.get("submode", "mixed"),
                "difficulty": quiz_params.get("difficulty", "medium"),
                "num_questions": num_questions,
                "score": 0,
                "total_answered": 0,
                "loading_stage": "initializing"
            }

            # Reset quiz state (lightweight operation)
            self.quiz_questions = []
            self.current_question_index = 0

            # 🚀 IMMEDIATE UI RESPONSE: Show loading state instantly
            mode_text = "🎮 LOCAL GPU" if mode == "offline" else "🌐 AI APIs"
            self.updateStatus.emit(f"🧠 Starting {mode_text} for {topic}...")

            # 🚀 CRITICAL: Use QTimer to defer heavy operations
            # This ensures UI updates are processed before starting background work
            QTimer.singleShot(10, self._start_instant_quiz_initialization)

            logger.info("✅ Quiz UI updated instantly - heavy operations deferred")

        except Exception as e:
            logger.error(f"❌ Error starting quiz: {e}")
            self.errorOccurred.emit(f"Failed to start quiz: {str(e)}")

    def _start_instant_quiz_initialization(self):
        """
        🚀 INSTANT QUIZ INITIALIZATION - Deferred heavy operations with immediate UI feedback
        """
        try:
            logger.info("🚀 Starting instant quiz initialization...")

            # Start progress monitoring immediately
            self._start_enhanced_progress_monitoring()

            # Update loading stage
            self.current_quiz["loading_stage"] = "checking_models"
            self.updateStatus.emit("🔍 Checking available AI models...")

            # Use QTimer to defer heavy operations in small chunks
            QTimer.singleShot(100, self._check_model_availability_async)

        except Exception as e:
            logger.error(f"❌ Instant initialization failed: {e}")
            self.errorOccurred.emit(f"Quiz initialization failed: {str(e)}")

    def _start_quiz_background_initialization(self):
        """
        🚀 Start quiz initialization in background thread to prevent UI blocking
        """
        import threading

        async def background_initialization():
            try:
                logger.info("🔄 Background quiz initialization started...")

                # 🚀 CRITICAL FIX: Start progress monitoring for long operations
                self._start_progress_monitoring()

                # ✅ MOVED FROM UI THREAD: Initialize MCQ manager FIRST
                if not self._ensure_mcq_manager_ready():
                    logger.info("⏳ MCQ Manager not ready - initializing first...")
                    self._initialize_mcq_manager()

                    # Wait a moment for initialization to complete (now safe in background)
                    import asyncio
                    await asyncio.sleep(0.5)

                # ✅ MOVED FROM UI THREAD: Offline mode validation
                mode = self.current_quiz.get("mode", "auto")
                topic = self.current_quiz.get("topic", "General Knowledge")

                if mode == "offline":
                    offline_available, offline_checks = self._validate_offline_mode_requirements()
                    if not offline_available:
                        logger.error("❌ Offline mode requested but not available")
                        logger.error(f"📋 Offline checks failed: {offline_checks}")
                        self.errorOccurred.emit(
                            "🚫 Offline Mode Not Available\n\n"
                            "Offline mode requires local AI setup:\n\n"
                            "🔥 INSTALL LM Studio: https://lmstudio.ai/\n"
                            "   • Load a model like Qwen2.5-7B-Instruct\n"
                            "   • Start the server\n\n"
                            "🔧 OR INSTALL Ollama: https://ollama.ai/\n"
                            "   • Run: ollama pull llama3.1:8b\n"
                            "   • Ensure Ollama is running\n\n"
                            "💡 Or switch to Online mode and add API keys."
                        )
                        return
                    else:
                        logger.info(f"✅ Offline mode validated: {offline_checks}")
                        self.updateStatus.emit(f"🎮 Offline mode ready for {topic}...")

                # Now start generation
                if not self._ensure_mcq_manager_ready():
                    logger.info("⏳ MCQ Manager still not ready - starting initialization...")
                    self._start_mcq_manager_initialization()

                    # Wait for initialization with proper timeout
                    self._wait_for_mcq_manager_then_generate()
                else:
                    logger.info("✅ MCQ Manager ready - starting generation immediately")
                    self._generate_all_questions_upfront()

            except Exception as e:
                logger.error(f"❌ Background initialization failed: {e}")
                self.errorOccurred.emit(f"Quiz initialization failed: {str(e)}")

        # 🚀 CRITICAL FIX: Use QTimer with async wrapper
        def run_async_background():
            import asyncio
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(background_initialization())
            except Exception as e:
                logger.error(f"❌ Async background initialization failed: {e}")
            finally:
                loop.close()

        QTimer.singleShot(50, run_async_background)
        logger.info("🚀 Background initialization scheduled via QTimer - UI fully responsive")

    def _start_progress_monitoring(self):
        """
        🚀 CRITICAL FIX: Start periodic progress updates during long operations

        This prevents the UI from appearing frozen during DeepSeek inference
        """
        if not hasattr(self, 'progress_timer'):
            self.progress_timer = QTimer(self)
            self.progress_timer.timeout.connect(self._update_progress_during_generation)

        # Start timer for periodic updates every 3 seconds
        self.progress_timer.start(3000)
        self.progress_start_time = time.time()
        self.progress_messages = [
            "🧠 AI is thinking deeply...",
            "🤖 DeepSeek is analyzing the topic...",
            "📝 Generating expert-level question...",
            "🔍 Ensuring question quality...",
            "⚡ Almost ready...",
            "🎯 Finalizing question details...",
            "✨ Polishing the question...",
            "🧪 Validating question logic..."
        ]
        self.progress_message_index = 0
        logger.info("🚀 Progress monitoring started")

    def _start_enhanced_progress_monitoring(self):
        """
        🚀 Enhanced progress monitoring with more frequent updates and better UX
        """
        if not hasattr(self, 'enhanced_progress_timer'):
            self.enhanced_progress_timer = QTimer(self)
            self.enhanced_progress_timer.timeout.connect(self._update_enhanced_progress)

        # Start timer for more frequent updates (every 1.5 seconds)
        self.enhanced_progress_timer.start(1500)
        self.enhanced_progress_start_time = time.time()
        self.enhanced_progress_messages = [
            "🧠 Initializing AI systems...",
            "🔍 Checking model availability...",
            "⚡ Preparing question generation...",
            "🤖 Loading AI models...",
            "📝 Setting up quiz engine...",
            "🎯 Almost ready...",
            "✨ Finalizing setup...",
            "🚀 Starting generation..."
        ]
        self.enhanced_progress_index = 0
        logger.info("🚀 Enhanced progress monitoring started")

    def _update_enhanced_progress(self):
        """Update progress with enhanced messages"""
        try:
            if hasattr(self, 'enhanced_progress_messages') and self.enhanced_progress_messages:
                elapsed = time.time() - self.enhanced_progress_start_time

                # Cycle through messages
                message = self.enhanced_progress_messages[self.enhanced_progress_index % len(self.enhanced_progress_messages)]
                self.enhanced_progress_index += 1

                # Add elapsed time for user feedback
                time_str = f" ({elapsed:.1f}s)" if elapsed > 3 else ""
                self.updateStatus.emit(f"{message}{time_str}")

        except Exception as e:
            logger.debug(f"Progress update error: {e}")

    def _check_model_availability_async(self):
        """
        🚀 Check model availability without blocking UI
        """
        try:
            logger.info("🔍 Checking model availability asynchronously...")

            # Update loading stage
            self.current_quiz["loading_stage"] = "initializing_manager"
            self.updateStatus.emit("🔧 Initializing quiz manager...")

            # Check if MCQ manager exists (lightweight check)
            if hasattr(self, 'mcq_manager') and self.mcq_manager:
                logger.info("✅ MCQ Manager already available")
                QTimer.singleShot(50, self._start_lightweight_generation)
            else:
                logger.info("⏳ MCQ Manager needs initialization")
                QTimer.singleShot(50, self._initialize_manager_async)

        except Exception as e:
            logger.error(f"❌ Model availability check failed: {e}")
            self.errorOccurred.emit(f"Model check failed: {str(e)}")

    def _initialize_manager_async(self):
        """
        🚀 Initialize MCQ manager asynchronously with progress updates
        """
        try:
            logger.info("🔧 Initializing MCQ manager asynchronously...")

            # Update status
            self.updateStatus.emit("🔧 Loading AI components...")

            # Use background thread for heavy initialization
            import threading

            def init_manager():
                try:
                    if not hasattr(self, 'mcq_manager') or not self.mcq_manager:
                        from .core.mcq_manager import get_mcq_manager
                        self.mcq_manager = get_mcq_manager()
                        logger.info("✅ MCQ Manager initialized in background")

                    # Signal completion via Qt signal
                    QTimer.singleShot(0, self._on_manager_initialized)

                except Exception as e:
                    logger.error(f"❌ Manager initialization failed: {e}")
                    error_msg = f"Manager initialization failed: {str(e)}"
                    QTimer.singleShot(0, lambda msg=error_msg: self.errorOccurred.emit(msg))

            # 🚀 CRITICAL FIX: Use QTimer instead of threading to avoid Qt threading issues
            QTimer.singleShot(50, init_manager)

        except Exception as e:
            logger.error(f"❌ Async manager initialization failed: {e}")
            self.errorOccurred.emit(f"Manager initialization failed: {str(e)}")

    def _on_manager_initialized(self):
        """
        🚀 Called when MCQ manager is ready - continue with quiz generation
        """
        try:
            logger.info("✅ MCQ Manager ready - starting quiz generation")

            # Update loading stage
            self.current_quiz["loading_stage"] = "generating"
            self.updateStatus.emit("🧠 Generating your first question...")

            # Start lightweight generation
            QTimer.singleShot(50, self._start_lightweight_generation)

        except Exception as e:
            logger.error(f"❌ Manager ready callback failed: {e}")
            self.errorOccurred.emit(f"Quiz generation failed: {str(e)}")

    def _start_lightweight_generation(self):
        """
        🚀 Start quiz generation with minimal blocking
        """
        try:
            logger.info("🚀 Starting lightweight quiz generation...")

            # Update status
            self.updateStatus.emit("🎯 Generating questions...")

            # Use the existing background generation method
            self._start_quiz_background_initialization()

        except Exception as e:
            logger.error(f"❌ Lightweight generation failed: {e}")
            self.errorOccurred.emit(f"Quiz generation failed: {str(e)}")

    def _stop_enhanced_progress_monitoring(self):
        """
        🚀 Stop enhanced progress monitoring when quiz is ready
        """
        try:
            if hasattr(self, 'enhanced_progress_timer') and self.enhanced_progress_timer:
                self.enhanced_progress_timer.stop()
                logger.info("✅ Enhanced progress monitoring stopped")

            if hasattr(self, 'progress_timer') and self.progress_timer:
                self.progress_timer.stop()
                logger.info("✅ Regular progress monitoring stopped")

        except Exception as e:
            logger.debug(f"Progress monitoring stop error: {e}")

    def _update_progress_during_generation(self):
        """Update progress message during long generation"""
        try:
            elapsed = time.time() - self.progress_start_time

            # Show different messages based on elapsed time
            if elapsed < 60:  # First minute
                message = self.progress_messages[self.progress_message_index % len(self.progress_messages)]
                self.updateStatus.emit(f"{message} ({int(elapsed)}s)")
                self.progress_message_index += 1
            elif elapsed < 120:  # Second minute
                self.updateStatus.emit(f"🤖 DeepSeek is doing complex reasoning... ({int(elapsed)}s)")
            else:  # Beyond 2 minutes
                self.updateStatus.emit(f"🧠 Still working on expert question... ({int(elapsed)}s)")

        except Exception as e:
            logger.error(f"❌ Progress update error: {e}")

    def _stop_progress_monitoring(self):
        """Stop progress monitoring when generation completes"""
        if hasattr(self, 'progress_timer') and self.progress_timer.isActive():
            self.progress_timer.stop()
            logger.info("🛑 Progress monitoring stopped")

    def _validate_offline_mode_requirements(self):
        """Validate that offline mode can actually work - Enhanced Golden Path Detection"""
        try:
            checks = {
                'ollama_running': False,
                'lm_studio_running': False,
                'models_available': False
            }
            
            # 🔥 GOLDEN PATH FIX: Direct Ollama detection
            ollama_available = self._direct_ollama_check()
            checks['ollama_running'] = ollama_available
            
            # Check using the new UnifiedInferenceManager system
            if hasattr(self, 'mcq_manager') and self.mcq_manager:
                # Use the new API methods
                offline_available = self.mcq_manager.is_offline_available()
                
                if offline_available:
                    # Get detailed status from unified inference manager
                    try:
                        from knowledge_app.core.unified_inference_manager import get_inference_status
                        status = get_inference_status()
                        
                        checks['models_available'] = status.get("local_available", False)
                        
                        # Enhanced check based on actual models detected
                        models = status.get("models", {})
                        if models.get("ollama_json"):
                            checks['ollama_running'] = True
                            logger.info("✅ Ollama JSON generator is available")
                        if models.get("enhanced_lmstudio"):
                            checks['lm_studio_running'] = True
                            logger.info("✅ LM Studio generator is available")
                        
                    except Exception as e:
                        logger.debug(f"Unified inference status check failed: {e}")
                        # Use direct Ollama check result
                        checks['models_available'] = ollama_available
                        checks['ollama_running'] = ollama_available
                
                # If we have Ollama running OR models available, we're good
                final_available = ollama_available or offline_available
                logger.info(f"🔍 Enhanced offline mode validation: {checks}")
                return final_available, checks
            else:
                logger.warning("⚠️ MCQ Manager not available for offline mode validation")
                return False, {'error': 'MCQ Manager not initialized'}
            
        except Exception as e:
            logger.error(f"❌ Offline mode validation error: {e}")
            return False, {'error': str(e)}

    async def _direct_ollama_check(self) -> bool:
        """🔥 OPTIMIZED: More forgiving Ollama connection test with better error recovery"""
        try:
            import requests
            logger.info("🔍 Checking Ollama connection...")
            
            # 🔥 CRITICAL FIX: More forgiving timeout and retry logic
            session = requests.Session()
            for attempt in range(2):  # Quick 2 attempts
                try:
                    response = session.get("http://localhost:11434/api/tags", timeout=2)
                    if response.status_code == 200:
                        data = response.json()
                        models = data.get("models", [])
                        logger.info(f"✅ Ollama is running with {len(models)} models")
                        
                        if models:
                            # Just log the first few models, don't spam
                            model_names = [m.get("name", "unknown") for m in models[:3]]
                            logger.info(f"📋 Available models: {', '.join(model_names)}")
                            if len(models) > 3:
                                logger.info(f"   ... and {len(models) - 3} more models")
                        else:
                            logger.warning("⚠️ Ollama is running but no models installed")
                            logger.info("💡 Install a model: ollama pull llama3.1:8b")
                            
                        return True
                    else:
                        logger.debug(f"🔍 Ollama responded with status {response.status_code} (attempt {attempt + 1})")
                        
                except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                    logger.debug(f"🔍 Ollama connection attempt {attempt + 1} failed: {type(e).__name__}")
                    if attempt == 0:  # Only wait on first attempt
                        import time
                        time.sleep(0.5)  # Brief wait before retry
                    
            # All attempts failed - but be more forgiving
            logger.info("ℹ️ Ollama not detected - this is OK, other AI systems are available")
            return False
                
        except Exception as e:
            logger.debug(f"🔍 Ollama check failed: {e}")
            return False

    def _ensure_mcq_manager_ready(self):
        """Atomic check for MCQ manager readiness"""
        try:
            return (hasattr(self, 'mcq_manager') and 
                    self.mcq_manager is not None and 
                    hasattr(self, 'mcq_manager_ready') and 
                    self.mcq_manager_ready and
                    not getattr(self, 'mcq_manager_initializing', False))
        except Exception as e:
            logger.error(f"❌ MCQ manager readiness check failed: {e}")
            return False

    def _wait_for_mcq_manager_then_generate(self):
        """Wait for MCQ manager initialization before starting generation"""
        if not hasattr(self, 'init_wait_timer'):
            self.init_wait_timer = QTimer(self)
            self.init_wait_timer.timeout.connect(self._check_init_and_generate)
            
        self.init_wait_attempts = 0
        self.max_init_wait_attempts = 60  # 30 seconds max wait
        self.init_wait_timer.start(500)  # Check every 500ms
        
    def _check_init_and_generate(self):
        """Check if MCQ manager is ready and start generation"""
        try:
            self.init_wait_attempts += 1
            
            if hasattr(self, 'mcq_manager_ready') and self.mcq_manager_ready:
                logger.info("✅ MCQ Manager ready - starting generation")
                self.init_wait_timer.stop()
                self._generate_all_questions_upfront()
                return
                
            if self.init_wait_attempts >= self.max_init_wait_attempts:
                logger.error("❌ MCQ Manager initialization timeout")
                self.init_wait_timer.stop()
                self.errorOccurred.emit("AI system initialization timeout. Please restart the application.")
                return
                
            logger.debug(f"⏳ Waiting for MCQ Manager... (attempt {self.init_wait_attempts}/{self.max_init_wait_attempts})")
            
        except Exception as e:
            logger.error(f"❌ Error waiting for MCQ manager: {e}")
            self.init_wait_timer.stop()
            self.errorOccurred.emit(f"Initialization error: {str(e)}")

    @pyqtSlot(int)
    def submitAnswer(self, answer_index):
        """Submit answer - SIMPLE navigation through pre-generated questions"""
        try:
            logger.info(f"🎯 Submit answer called with index: {answer_index}")

            # 🚀 CRITICAL FIX: Add comprehensive validation
            if not hasattr(self, 'quiz_questions') or not self.quiz_questions:
                logger.error("❌ No quiz questions available")
                self.errorOccurred.emit("No quiz questions available")
                return

            if not hasattr(self, 'current_question_index'):
                logger.error("❌ No current question index")
                self.errorOccurred.emit("Quiz state error: no current question")
                return

            if self.current_question_index >= len(self.quiz_questions):
                logger.error(f"❌ Question index {self.current_question_index} out of range (max: {len(self.quiz_questions)})")
                self.errorOccurred.emit("Question index out of range")
                return

            current_question = self.quiz_questions[self.current_question_index]
            logger.info(f"📝 Current question: {current_question.get('question', 'Unknown')[:50]}...")

            # 🚀 CRITICAL FIX: Validate question data structure
            if not isinstance(current_question, dict):
                logger.error(f"❌ Invalid question data type: {type(current_question)}")
                self.errorOccurred.emit("Invalid question data format")
                return

            if "options" not in current_question or not current_question["options"]:
                logger.error("❌ Question missing options")
                self.errorOccurred.emit("Question missing options")
                return

            correct_index = current_question.get("correct_index", 0)
            is_correct = answer_index == correct_index

            logger.info(f"✅ Answer validation: user={answer_index}, correct={correct_index}, is_correct={is_correct}")

            if is_correct:
                self.current_quiz["score"] += 1

            # 🚀 CRITICAL FIX: Safe feedback data creation
            try:
                options = current_question["options"]
                if isinstance(options, dict):
                    options_list = list(options.values())
                else:
                    options_list = options

                correct_answer_text = ""
                if 0 <= correct_index < len(options_list):
                    correct_answer_text = options_list[correct_index]

                feedback_data = {
                    "is_correct": is_correct,
                    "correct_index": correct_index,
                    "user_answer": answer_index,
                    "explanation": current_question.get("explanation", "No explanation available."),
                    "correct_answer_text": correct_answer_text,
                    "feedback_message": "🎉 Correct!" if is_correct else f"❌ Incorrect. The correct answer was {chr(65 + correct_index)}."
                }

                logger.info(f"📤 Emitting feedback data: {feedback_data}")
                self.answerFeedback.emit(feedback_data)

            except Exception as feedback_error:
                logger.error(f"❌ Error creating feedback data: {feedback_error}")
                # Emit simple feedback as fallback
                simple_feedback = {
                    "is_correct": is_correct,
                    "correct_index": correct_index,
                    "user_answer": answer_index,
                    "explanation": "Feedback generation error",
                    "correct_answer_text": "",
                    "feedback_message": "🎉 Correct!" if is_correct else "❌ Incorrect."
                }
                self.answerFeedback.emit(simple_feedback)

            self.current_quiz["total_answered"] += 1
            logger.info(f"✅ Answer submitted successfully. Score: {self.current_quiz['score']}/{self.current_quiz['total_answered']}")

        except Exception as e:
            logger.error(f"❌ Critical error in submitAnswer: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            self.errorOccurred.emit(f"Submit answer error: {str(e)}")

    @pyqtSlot()
    def loadNextQuestion(self):
        """Load next question - SIMPLE navigation through pre-generated questions"""
        try:
            # Check if quiz is complete
            if self.current_quiz["total_answered"] >= self.current_quiz["num_questions"]:
                self._completeQuiz()
                return
                
            # Move to next question
            self.current_question_index += 1
            
            # Check if we have more questions
            if self.current_question_index >= len(self.quiz_questions):
                logger.error("❌ No more pre-generated questions available")
                self.errorOccurred.emit("No more questions available")
                return
                
            # Get next question
            question_data = self.quiz_questions[self.current_question_index].copy()
            question_data["question_number"] = self.current_question_index + 1
            question_data["total_questions"] = len(self.quiz_questions)
            
            # Send to frontend
            self.questionReceived.emit(question_data)
            logger.info(f"📋 Loaded question {self.current_question_index + 1} of {len(self.quiz_questions)}")
                    
        except Exception as e:
            logger.error(f"Error loading next question: {e}")
            self.errorOccurred.emit(str(e))

    @pyqtSlot()
    def showPreviousQuestion(self):
        """Show previous answered question"""
        try:
            if self.current_question_index > 0:
                self.current_question_index -= 1
                question_data = self.question_history[self.current_question_index].copy()
                
                # Mark as review mode
                question_data["review_mode"] = True
                question_data["question_number"] = self.current_question_index + 1
                question_data["total_questions"] = len(self.question_history)
                
                self.questionReceived.emit(question_data)
                
        except Exception as e:
            logger.error(f"Error showing previous question: {e}")
            self.errorOccurred.emit(str(e))

    @pyqtSlot()
    def showNextQuestion(self):
        """Show next answered question"""
        try:
            if self.current_question_index < len(self.question_history) - 1:
                self.current_question_index += 1
                question_data = self.question_history[self.current_question_index].copy()
                
                # Mark as review mode
                question_data["review_mode"] = True
                question_data["question_number"] = self.current_question_index + 1
                question_data["total_questions"] = len(self.question_history)
                
                self.questionReceived.emit(question_data)
                
        except Exception as e:
            logger.error(f"Error showing next question: {e}")
            self.errorOccurred.emit(str(e))

    def _generate_all_questions_upfront(self):
        """🔥 ULTRA-OPTIMIZED: Generate ALL questions without blocking UI - Maximum Robustness"""
        try:
            num_questions = self.current_quiz["num_questions"]
            topic = self.current_quiz["topic"]
            difficulty = self.current_quiz["difficulty"]
            question_type = self.current_quiz["submode"]
            
            logger.info(f"🚀 OPTIMIZED: Starting generation of {num_questions} questions for '{topic}'...")
            
            # 🔥 CRITICAL: Initialize thread-safe inference if not already done
            try:
                from .core.thread_safe_inference import get_thread_safe_inference
                self.thread_safe_inference = get_thread_safe_inference()
                
                # Connect signals (disconnect first to avoid duplicates)
                try:
                    self.thread_safe_inference.mcq_generated.disconnect()
                    self.thread_safe_inference.generation_failed.disconnect()
                    self.thread_safe_inference.generation_progress.disconnect()
                except:
                    pass  # Signals weren't connected
                    
                # Connect fresh signals
                self.thread_safe_inference.mcq_generated.connect(self._on_thread_safe_mcq_generated)
                self.thread_safe_inference.generation_failed.connect(self._on_thread_safe_generation_failed)
                self.thread_safe_inference.generation_progress.connect(self._on_thread_safe_progress)
                
                logger.info("✅ Thread-safe inference initialized")
                
            except Exception as e:
                logger.error(f"❌ Thread-safe inference failed: {e}")
                # Fallback to basic generation
                self._generate_questions_fallback()
                return
            
            # Initialize tracking
            self.quiz_questions = []
            self.questions_to_generate = num_questions
            self.questions_generated_count = 0
            self.generation_operations = []
            
            # 🚀 FIXED: Use proper async generation to prevent UI blocking
            logger.info("🚀 Starting NON-BLOCKING quiz generation...")

            # 🔥 DIRECT GENERATION FIX: Use working unified manager directly
            try:
                logger.info("🔥 Using direct unified manager generation (WORKING METHOD)")

                # Initialize unified manager if not available
                if not (hasattr(self, 'unified_manager') and self.unified_manager):
                    logger.info("🔧 Initializing unified manager on-demand...")
                    from .core.unified_inference_manager import initialize_unified_inference, UnifiedInferenceManager

                    success = initialize_unified_inference()
                    if success:
                        self.unified_manager = UnifiedInferenceManager()
                        logger.info("✅ On-demand unified manager initialization successful")
                    else:
                        logger.error("❌ On-demand unified manager initialization failed")
                        self.unified_manager = None

                # Generate first question immediately using working method
                if hasattr(self, 'unified_manager') and self.unified_manager:
                    result = self.unified_manager.generate_mcq_sync(
                        topic=topic,
                        difficulty=difficulty,
                        question_type=question_type
                    )

                    if result:
                        # Format and emit the question immediately
                        formatted_question = {
                            "question": result.get('question', ''),
                            "options": result.get('options', []),
                            "correct_answer": result.get('correct', ''),
                            "explanation": result.get('explanation', ''),
                            "question_number": 1,
                            "total_questions": num_questions,
                            "is_loading": False
                        }

                        logger.info(f"🎉 Direct generation successful: {result.get('question', '')[:100]}...")

                        # Add to quiz questions
                        self.quiz_questions = [formatted_question]

                        # Emit immediately to UI
                        self.questionReceived.emit(formatted_question)

                        logger.info("✅ Question emitted directly to UI!")
                        return
                    else:
                        logger.error("❌ Direct generation returned no result")
                else:
                    logger.error("❌ Unified manager not available")
            except Exception as e:
                logger.error(f"❌ Direct generation failed: {e}")

            # Fallback: Return immediately to prevent UI blocking
            self._start_async_generation_background(topic, difficulty, question_type, num_questions)

            # Emit status update to show loading
            self.updateStatus.emit("🧠 Generating your first question...")

            logger.info("✅ Quiz generation started in background - UI thread free")

        except Exception as e:
            logger.error(f"❌ Failed to start quiz generation: {e}")
            # Use the errorOccurred signal instead of direct JavaScript
            self.errorOccurred.emit(f"Failed to generate quiz: {str(e)}")

    def _start_async_generation_background(self, topic: str, difficulty: str, question_type: str, num_questions: int):
        """
        🚀 Start generation in background thread to prevent UI blocking
        """
        import threading

        def background_generation():
            try:
                logger.info(f"🔄 Background generation started: {num_questions} questions")

                # 🚀 CRITICAL FIX: Use truly async generation to prevent UI blocking
                for i in range(num_questions):
                    try:
                        # Use QTimer to defer each generation request to prevent blocking
                        def start_single_generation(question_num=i):
                            try:
                                # Use appropriate timeout based on difficulty
                                if difficulty == "expert":
                                    timeout_duration = 180.0  # 3 minutes for expert reasoning
                                elif difficulty == "hard":
                                    timeout_duration = 120.0  # 2 minutes for hard questions
                                else:
                                    timeout_duration = 90.0   # 1.5 minutes for easy/medium questions

                                operation_id = self.thread_safe_inference.generate_mcq_async(
                                    topic=topic,
                                    difficulty=difficulty,
                                    question_type=question_type,
                                    timeout=timeout_duration
                                )
                                self.generation_operations.append(operation_id)
                                logger.info(f"🚀 Started generation {question_num+1}/{num_questions}: {operation_id}")
                            except Exception as e:
                                logger.error(f"❌ Failed to start generation {question_num+1}: {e}")

                        # Defer each generation with small delays to prevent overwhelming
                        QTimer.singleShot(i * 100, start_single_generation)  # 100ms between starts

                    except Exception as e:
                        logger.error(f"❌ Failed to schedule generation {i+1}: {e}")

                # Set up timeout fallback
                QTimer.singleShot(1000, self._setup_generation_timeout)  # Defer timeout setup too

            except Exception as e:
                logger.error(f"❌ Background generation failed: {e}")
                # Emit error to UI using signal
                self.errorOccurred.emit(f"Background generation failed: {str(e)}")

        # 🚀 CRITICAL FIX: Use QTimer instead of threading to avoid Qt threading issues
        QTimer.singleShot(50, background_generation)
        logger.info("🚀 Background generation scheduled via QTimer - UI fully responsive")
            
    def _setup_generation_timeout(self):
        """Set up a timeout to prevent hanging forever"""
        if not hasattr(self, 'generation_timeout_timer'):
            self.generation_timeout_timer = QTimer(self)
            self.generation_timeout_timer.timeout.connect(self._handle_generation_timeout)
            
        # 60 second total timeout for all questions
        self.generation_timeout_timer.start(60000)
        
    def _handle_generation_timeout(self):
        """Handle generation timeout by using whatever questions we have"""
        try:
            self.generation_timeout_timer.stop()
            
            if len(self.quiz_questions) > 0:
                logger.warning(f"⏰ Generation timeout - using {len(self.quiz_questions)} generated questions")
                self._on_all_questions_generated()
            else:
                logger.error("⏰ Generation timeout - no questions generated, using fallback")
                self._generate_questions_fallback()
                
        except Exception as e:
            logger.error(f"❌ Error handling generation timeout: {e}")
            




    def _on_thread_safe_mcq_generated(self, mcq_data: dict):
        """Handle MCQ generated by thread-safe wrapper - RUNS ON MAIN THREAD"""
        try:
            # 🚀 CRITICAL FIX: Stop progress monitoring when first question arrives
            if self.questions_generated_count == 0:
                self._stop_progress_monitoring()
                logger.info("🛑 Stopped progress monitoring - first question received")

            # Validate and add question
            if self._validate_question_format(mcq_data):
                self.quiz_questions.append(mcq_data)
                self.questions_generated_count += 1

                logger.info(f"✅ Question {self.questions_generated_count}/{self.questions_to_generate} completed")

                # Update progress
                progress = f"✅ Generated {self.questions_generated_count}/{self.questions_to_generate} questions"
                self.updateStatus.emit(progress)

                # Check if all questions are generated
                if self.questions_generated_count >= self.questions_to_generate:
                    self._on_all_questions_generated()
            else:
                logger.error("❌ Thread-safe generated question failed validation")
                self._handle_generation_failure("Generated question failed validation")
                
        except Exception as e:
            logger.error(f"❌ Error handling thread-safe MCQ: {e}")
            self._handle_generation_failure(f"Error processing question: {str(e)}")
    
    def _on_thread_safe_generation_failed(self, error_message: str):
        """Handle generation failure from thread-safe wrapper - RUNS ON MAIN THREAD"""
        logger.error(f"❌ Thread-safe generation failed: {error_message}")
        self._handle_generation_failure(error_message)
    
    def _on_thread_safe_progress(self, progress_message: str):
        """Handle progress updates from thread-safe wrapper - RUNS ON MAIN THREAD"""
        self.updateStatus.emit(progress_message)
    
    def _handle_generation_failure(self, error_message: str):
        """Handle any generation failure"""
        # 🚀 CRITICAL FIX: Stop progress monitoring on failure
        self._stop_progress_monitoring()

        # 🚫 NO FALLBACK QUESTIONS - Report complete failure
        logger.error("🚫 NO FALLBACK QUESTIONS AVAILABLE")
        logger.error(f"❌ Question generation failed: {error_message}")
        self.errorOccurred.emit(f"Question generation failed: {error_message} - No fallback content available")
    
    def _on_all_questions_generated(self):
        """Called when all questions have been generated - MAIN THREAD"""
        try:
            if len(self.quiz_questions) == 0:
                self.errorOccurred.emit("No questions were generated successfully")
                return
            
            # Save questions to storage
            self._save_questions_to_storage()
            
            logger.info(f"🎉 All {len(self.quiz_questions)} questions generated! Starting quiz...")
            
            # Update UI
            self.updateStatus.emit(f"✅ Ready! {len(self.quiz_questions)} questions generated.")
            
            # Start quiz with first question
            self.startQuizWithFirstQuestion()
            
        except Exception as e:
            logger.error(f"❌ Error completing question generation: {e}")
            self.errorOccurred.emit(f"Failed to complete quiz setup: {str(e)}")
    
    def _save_questions_to_storage(self):
        """Save generated questions to comprehensive storage with question history"""
        try:
            if self.quiz_questions and self.current_quiz:
                # Save to current quiz file (backward compatibility)
                storage_data = {
                    "quiz_params": self.current_quiz,
                    "questions": self.quiz_questions,
                    "generated_at": time.time()
                }
                
                storage_file = Path("user_data/current_quiz.json")
                with open(storage_file, 'w', encoding='utf-8') as f:
                    json.dump(storage_data, f, indent=2, ensure_ascii=False)
                
                # 🏆 NEW: Save to comprehensive question history database
                self._save_to_question_history()
                
                logger.info(f"💾 Saved {len(self.quiz_questions)} questions to storage")
        except Exception as e:
            logger.error(f"❌ Failed to save questions: {e}")
    
    def _save_to_question_history(self):
        """Save questions to the comprehensive question history database"""
        try:
            # Import and initialize the question history storage
            from .core.question_history_storage import QuestionHistoryStorage
            
            if not hasattr(self, 'question_history_storage'):
                self.question_history_storage = QuestionHistoryStorage()
            
            # Generate session ID for this quiz
            import uuid
            session_id = str(uuid.uuid4())
            
            # Save each question to history
            saved_count = 0
            for question_data in self.quiz_questions:
                try:
                    question_id = self.question_history_storage.save_question(
                        question_data=question_data,
                        quiz_params=self.current_quiz,
                        session_id=session_id
                    )
                    if question_id:
                        saved_count += 1
                except Exception as e:
                    logger.error(f"❌ Failed to save individual question to history: {e}")
            
            # Save quiz session metadata
            if saved_count > 0:
                try:
                    self.question_history_storage.save_quiz_session(
                        session_id=session_id,
                        quiz_params=self.current_quiz,
                        questions_count=len(self.quiz_questions)
                    )
                    logger.info(f"🏆 Saved {saved_count}/{len(self.quiz_questions)} questions to question history database")
                except Exception as e:
                    logger.error(f"❌ Failed to save quiz session: {e}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save to question history: {e}")
    
    @pyqtSlot()
    def startQuizWithFirstQuestion(self):
        """Public slot for starting quiz with first question - Qt MetaObject compatible"""
        self._start_quiz_with_first_question()

    def _start_quiz_with_first_question(self):
        """Start the quiz with proper validation and error handling"""
        try:
            # 🚀 CRITICAL FIX: Stop progress monitoring when quiz starts
            self._stop_enhanced_progress_monitoring()

            if not self.quiz_questions or len(self.quiz_questions) == 0:
                logger.error("❌ No questions available to start quiz")
                self.errorOccurred.emit("No questions were generated. Please try again.")
                return
                
            # Validate first question format
            first_question_raw = self.quiz_questions[0]
            if not self._validate_question_format(first_question_raw):
                logger.error("❌ First question has invalid format")
                self.errorOccurred.emit("Generated questions have invalid format. Please try again.")
                return
                
            # Create validated question data
            first_question = first_question_raw.copy()
            first_question["question_number"] = 1
            first_question["total_questions"] = len(self.quiz_questions)
            first_question["is_loading"] = False
            
            # Reset quiz index
            self.current_question_index = 0
            
            # Emit to UI
            self.questionReceived.emit(first_question)
            logger.info("🚀 Quiz started with validated first question")
            
        except Exception as e:
            logger.error(f"❌ Failed to start quiz: {e}")
            self.errorOccurred.emit(f"Failed to start quiz: {str(e)}")

    def _validate_question_format(self, question_data):
        """🔥 OPTIMIZED: Simplified validation that's less likely to reject good questions"""
        try:
            if not isinstance(question_data, dict):
                logger.debug("❌ Question data is not a dictionary")
                return False
                
            # Essential fields check
            if "question" not in question_data or not question_data["question"]:
                logger.debug("❌ Missing or empty question field")
                return False
                
            if "options" not in question_data:
                logger.debug("❌ Missing options field")  
                return False
                
            options = question_data["options"]
            
            # Handle both list and dict format options
            if isinstance(options, dict):
                # Convert dict to list
                options_list = []
                for key in sorted(options.keys()):
                    options_list.append(options[key])
                options = options_list
                question_data["options"] = options
                
            if not isinstance(options, list) or len(options) < 2:
                logger.debug(f"❌ Invalid options format: expected list with 2+ items, got {type(options)} with {len(options) if isinstance(options, list) else 'unknown'} items")
                return False
                
            # Pad options to 4 if needed
            while len(options) < 4:
                options.append(f"Option {len(options) + 1}")
                
            # Ensure only 4 options
            if len(options) > 4:
                options = options[:4]
                question_data["options"] = options
                
            # Handle correct answer
            correct_answer = question_data.get("correct_answer") or question_data.get("correct")
            
            if not correct_answer:
                logger.debug("❌ No correct answer found")
                # Auto-fix: use first option as correct
                correct_answer = options[0]
                question_data["correct_answer"] = correct_answer
                
            # Find or set correct index
            correct_index = 0  # Default to first option
            
            if correct_answer in options:
                correct_index = options.index(correct_answer)
            else:
                # More forgiving matching
                correct_lower = correct_answer.lower().strip()
                for i, option in enumerate(options):
                    if option and correct_lower in option.lower().strip():
                        correct_index = i
                        break
                        
            question_data["correct_index"] = correct_index
            question_data["correct_answer"] = options[correct_index]
            
            # Add explanation if missing
            if "explanation" not in question_data or not question_data["explanation"]:
                question_data["explanation"] = f"The correct answer is {chr(65 + correct_index)}: {options[correct_index]}"
                
            logger.debug("✅ Question validation passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Question validation error: {e}")
            return False
    
    def _strings_are_similar(self, str1, str2, threshold=0.7):
        """Check if two strings are similar enough (simple similarity check)"""
        try:
            # Remove common words and punctuation for comparison
            import re
            clean1 = re.sub(r'[^\w\s]', '', str1).strip()
            clean2 = re.sub(r'[^\w\s]', '', str2).strip()
            
            if not clean1 or not clean2:
                return False
            
            # Check overlap of words
            words1 = set(clean1.split())
            words2 = set(clean2.split())
            
            if len(words1) == 0 or len(words2) == 0:
                return False
            
            intersection = len(words1.intersection(words2))
            union = len(words1.union(words2))
            
            similarity = intersection / union if union > 0 else 0
            return similarity >= threshold
            
        except Exception as e:
            logger.error(f"❌ String similarity error: {e}")
            return False

    def _initialize_mcq_manager(self):
        """Initialize MCQ manager if not already done"""
        if not self.mcq_manager:
            try:
                from .core.mcq_manager import get_mcq_manager
                self.mcq_manager = get_mcq_manager()
                logger.info("✅ MCQ Manager initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize MCQ Manager: {e}")

    def _get_best_available_generator(self):
        """Get the best available generator with proper validation"""
        try:
            if not self.mcq_manager:
                logger.error("❌ MCQ manager not available")
                return None
                
            # Ensure generators are initialized
            self.mcq_manager._ensure_generators_initialized()
            
            # Check generators in priority order
            generators_to_check = [
                ('enhanced_lmstudio_generator', 'Enhanced LM Studio'),
                ('offline_generator', 'Ollama JSON'),
                ('ollama_json_generator', 'Ollama Direct'),
                ('lm_studio_generator', 'LM Studio'),
                ('online_generator', 'Online API')
            ]
            
            for attr_name, display_name in generators_to_check:
                if hasattr(self.mcq_manager, attr_name):
                    generator = getattr(self.mcq_manager, attr_name)
                    
                    # Check if generator exists and is available
                    if generator and hasattr(generator, 'is_available'):
                        try:
                            if generator.is_available():
                                logger.info(f"✅ Selected generator: {display_name}")
                                return generator
                            else:
                                logger.debug(f"⚠️ {display_name} not available")
                        except Exception as e:
                            logger.warning(f"⚠️ Error checking {display_name} availability: {e}")
                            continue
                    else:
                        logger.debug(f"⚠️ {display_name} not properly initialized")
            
            logger.error("❌ No available generators found")
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting available generator: {e}")
            return None

    def _cleanup_generation_resources(self):
        """Clean up generation threads and resources"""
        try:
            # Stop generation thread
            if hasattr(self, 'generation_thread') and self.generation_thread:
                if self.generation_thread.isRunning():
                    self.generation_thread.requestInterruption()
                    if not self.generation_thread.wait(2000):  # 2 second timeout
                        self.generation_thread.terminate()
                self.generation_thread = None
                
            # Stop fast generator
            if hasattr(self, 'fast_generator') and self.fast_generator:
                if self.fast_generator.isRunning():
                    self.fast_generator.stop()
                    if not self.fast_generator.wait(2000):
                        self.fast_generator.terminate()
                self.fast_generator = None
                
            # Stop init wait timer
            if hasattr(self, 'init_wait_timer') and self.init_wait_timer:
                self.init_wait_timer.stop()
                self.init_wait_timer = None
                
            logger.info("🧹 Generation resources cleaned up")
            
        except Exception as e:
            logger.error(f"❌ Error cleaning up generation resources: {e}")

    @pyqtSlot(str)
    def startModelTraining(self, training_params_json: str):
        """
        🚀 Start model training using proper enterprise-grade orchestrator
        
        FIXED: No longer creates QThreads from background threads.
        Uses TrainingOrchestrator for proper Qt threading model compliance.
        """
        try:
            # Use the training orchestrator for proper threading
            if not hasattr(self, 'training_orchestrator'):
                self._initialize_training_orchestrator()
            
            # CRITICAL FIX: Sanitize training parameters to remove null bytes
            from knowledge_app.utils.memory_manager import get_memory_manager
            memory_manager = get_memory_manager()
            
            # First sanitize the JSON string directly
            clean_json = memory_manager.sanitize_string(training_params_json)
            
            # Then pass the sanitized JSON to the training orchestrator
            self.training_orchestrator.start_training(clean_json)
            
        except Exception as e:
            logger.error(f"❌ Training startup failed: {e}")
            self.errorOccurred.emit(f"Training failed to start: {str(e)}")
    
    @pyqtSlot()
    def cancelModelTraining(self):
        """
        🛑 Cancel current model training
        
        NEW: Provides user control over training operations.
        """
        try:
            if hasattr(self, 'training_orchestrator'):
                self.training_orchestrator.cancel_training()
            else:
                logger.warning("⚠️ No training orchestrator available to cancel")
                self.errorOccurred.emit("No active training to cancel")
                
        except Exception as e:
            logger.error(f"❌ Error cancelling training: {e}")
            self.errorOccurred.emit(f"Error cancelling training: {str(e)}")
    
    def _initialize_training_orchestrator(self):
        """Initialize the training orchestrator with proper signal connections"""
        try:
            # Explicitly import Any to ensure it's defined in the namespace
            from typing import Any
            from .core.training_orchestrator import TrainingOrchestrator
            
            self.training_orchestrator = TrainingOrchestrator(self)
            
            # Connect orchestrator signals to UI signals
            self.training_orchestrator.training_started.connect(self._on_training_orchestrator_started)
            self.training_orchestrator.progress_update.connect(self._on_training_orchestrator_progress)
            self.training_orchestrator.training_completed.connect(self._on_training_orchestrator_completed)
            self.training_orchestrator.training_cancelled.connect(self._on_training_orchestrator_cancelled)
            self.training_orchestrator.error_occurred.connect(self.errorOccurred.emit)
            
            logger.info("🚀 Training orchestrator initialized with proper signal connections")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize training orchestrator: {e}")
            raise
    
    @pyqtSlot(dict)
    def _on_training_orchestrator_started(self, training_info: dict):
        """Handle training start from orchestrator"""
        message = training_info.get("message", "Training started")
        self.updateStatus.emit(message)
        
        # Emit structured data if we have FIRE integration
        if hasattr(self, 'fire_integration') and self.fire_integration:
            self.fireTrainingStarted.emit(training_info)
    
    @pyqtSlot(dict)
    def _on_training_orchestrator_progress(self, progress_info: dict):
        """Handle training progress from orchestrator with enhanced UI feedback"""
        message = progress_info.get("message", "Training in progress")
        status = progress_info.get("status", "unknown")
        progress_percent = progress_info.get("progress_percent", 0)
        
        # Emit basic status update
        self.updateStatus.emit(message)
        
        # 🚀 Phase 2: Emit structured progress for rich UI updates
        structured_progress = {
            "message": message,
            "status": status,
            "progress_percent": progress_percent,
            "phase": progress_info.get("phase", "unknown"),
            "can_cancel": progress_info.get("can_cancel", False),
            "gpu_utilization": progress_info.get("gpu_utilization", 0),
            "timestamp": time.time()
        }
        self.trainingProgressStructured.emit(structured_progress)
        
        # Emit status change if different
        if hasattr(self, '_last_training_status'):
            if self._last_training_status != status:
                self.trainingStatusChanged.emit(status)
                self._last_training_status = status
        else:
            self._last_training_status = status
            self.trainingStatusChanged.emit(status)
        
        # Emit metrics if available
        if "metrics" in progress_info:
            self.trainingMetricsUpdate.emit(progress_info["metrics"])
        
        # Emit structured progress if we have FIRE integration
        if hasattr(self, 'fire_integration') and self.fire_integration:
            self.fireRealtimeUpdate.emit(progress_info)
    
    @pyqtSlot(dict)
    def _on_training_orchestrator_completed(self, completion_info: dict):
        """Handle training completion from orchestrator"""
        success = completion_info.get("success", False)
        message = completion_info.get("message", "Training completed")
        
        if success:
            self.updateStatus.emit(f"✅ {message}")
        else:
            self.errorOccurred.emit(f"❌ {message}")
        
        # Emit completion if we have FIRE integration
        if hasattr(self, 'fire_integration') and self.fire_integration:
            self.fireTrainingCompleted.emit(completion_info)
    
    @pyqtSlot(str)
    def _on_training_orchestrator_cancelled(self, reason: str):
        """Handle training cancellation from orchestrator"""
        self.updateStatus.emit(f"🛑 Training cancelled: {reason}")
        logger.info(f"🛑 Training cancelled: {reason}")
    
    def _process_books_for_training(self, training_params):
        """
        ⚠️ DEPRECATED: Process uploaded books and start training with FIRE monitoring
        
        CRITICAL BUG: This method violates Qt threading model by creating QThreads
        from background threads. Use TrainingOrchestrator instead.
        
        This method is kept temporarily for backwards compatibility but should be removed.
        """
        import logging
        process_logger = logging.getLogger(__name__)
        
        try:
            from .core.document_processor import AdvancedDocumentProcessor
            from pathlib import Path
            
            # 🔥 Try to use FIRE-enhanced training with monitoring
            if self.fire_integration:
                process_logger.info("🔥 Using FIRE-enhanced training with real-time monitoring")
                self._fire_enhanced_training(training_params)
                return
            else:
                process_logger.warning("🔥 FIRE not available, forcing GPU BEAST MODE")
                self._force_gpu_beast_mode_training(training_params)
                return
            
            self.updateStatus.emit("🔄 Processing uploaded books for training...")
            
            # CRITICAL FIX: Move ALL file system operations to background thread
            # to prevent UI blocking
            import threading
            def process_and_train():
                # Capture logger in local scope for nested function access
                import logging
                local_logger = logging.getLogger(__name__)
                
                try:
                    # CRITICAL FIX: Move file discovery to background thread
                    # Find all uploaded files from both possible directories
                    uploaded_files = []
                    
                    # Check root level uploaded_books directory
                    root_books_dir = Path("uploaded_books")
                    if root_books_dir.exists():
                        uploaded_files.extend(list(root_books_dir.glob("*.pdf")) + list(root_books_dir.glob("*.txt")))
                        local_logger.info(f"📚 Found {len(uploaded_files)} files in root uploaded_books")
                    
                    # Also check data/uploaded_books directory
                    data_books_dir = Path("data/uploaded_books")
                    if data_books_dir.exists():
                        additional_files = list(data_books_dir.glob("*.pdf")) + list(data_books_dir.glob("*.txt"))
                        # Avoid duplicates by checking filenames
                        existing_names = {f.name for f in uploaded_files}
                        for file in additional_files:
                            if file.name not in existing_names:
                                uploaded_files.append(file)
                        local_logger.info(f"📚 Found {len(additional_files)} files in data/uploaded_books")
                    
                    if not uploaded_files:
                        QMetaObject.invokeMethod(
                            self, "_emit_error", 
                            Qt.QueuedConnection,
                            Q_ARG(str, "No supported files found in uploaded books directories.")
                        )
                        return
                    
                    local_logger.info(f"📚 Total {len(uploaded_files)} unique files to process for training")
                    
                    # Create processing directory
                    processing_dir = Path("data/processed_docs")
                    processing_dir.mkdir(exist_ok=True)
                    
                    # Process documents
                    processor = AdvancedDocumentProcessor()
                    file_paths = [str(f) for f in uploaded_files]
                    
                    # Process documents
                    result = processor.process_documents_advanced(
                        file_paths, 
                        str(processing_dir),
                        chunk_size=500
                    )
                    
                    local_logger.info(f"📊 Processed {result['stats']['successful_files']} files into {result['stats']['total_chunks']} chunks")
                    
                    # Get training configuration
                    training_config = self._get_training_config(training_params)
                    
                    # Ensure training data path exists
                    training_data_path = processing_dir / "training_dataset.jsonl"
                    if not training_data_path.exists():
                        raise FileNotFoundError(f"Training data not found: {training_data_path}")
                    
                    training_config["training_data_path"] = str(training_data_path)
                    
                    # Start training
                    self.updateStatus.emit("🚀 Starting Golden Path training...")
                    self._start_training_thread(training_config)
                    
                except Exception as e:
                    local_logger.error(f"❌ Processing failed: {e}")
                    QMetaObject.invokeMethod(
                        self, "_emit_error", 
                        Qt.QueuedConnection,
                        Q_ARG(str, f"Document processing failed: {str(e)}")
                    )
            
            thread = threading.Thread(target=process_and_train, daemon=True)
            thread.start()
            
        except Exception as e:
            process_logger.error(f"❌ Book processing setup failed: {e}")
            self.errorOccurred.emit(f"Failed to setup book processing: {str(e)}")
    
    def _get_training_config(self, training_params):
        """Get training configuration from unified config with robust fallback"""
        import logging
        config_logger = logging.getLogger(__name__)
        
        try:
            from .core.app_config import AppConfig
            app_config = AppConfig()
            
            # Get training preset
            preset_name = training_params.get('preset', 'standard_training')
            training_config = app_config.get_config_value(f"training.presets.{preset_name}", {})
            
            # CRITICAL FIX: Validate and provide robust fallback configuration
            if not training_config or not isinstance(training_config, dict):
                config_logger.warning(f"⚠️ Invalid training config for preset '{preset_name}', using robust fallback")
                training_config = self._get_robust_fallback_config()
            else:
                # CRITICAL FIX: Log detailed missing sections for debugging
                missing_sections = []
                if 'lora' not in training_config:
                    missing_sections.append('lora')
                if 'training' not in training_config:
                    missing_sections.append('training')
                if 'base_model' not in training_config:
                    missing_sections.append('base_model')
                
                if missing_sections:
                    config_logger.warning(f"⚠️ Training preset '{preset_name}' missing sections: {missing_sections}")
                    config_logger.warning("⚠️ These sections will be filled with defaults - check unified_config.json")
                
                # Validate essential fields and fill missing ones
                training_config = self._validate_and_complete_config(training_config)
            
            return training_config
            
        except Exception as e:
            config_logger.error(f"❌ Failed to get training config: {e}")
            config_logger.info("🔄 Using emergency fallback configuration")
            return self._get_robust_fallback_config()
    
    def _get_robust_fallback_config(self):
        """CRITICAL FIX: Robust fallback configuration with all required fields"""
        return {
            "base_model": "microsoft/DialoGPT-small",  # Lightweight model for stability
            "lora": {
                "r": 16,
                "alpha": 32,
                "dropout": 0.1,
                "target_modules": ["c_attn", "c_proj"]
            },
            "training": {
                "epochs": 2,  # Reduced epochs for faster completion
                "batch_size": 2,  # Small batch size for stability
                "learning_rate": 0.0001,  # Conservative learning rate
                "gradient_accumulation_steps": 4,  # Compensate for small batch
                "warmup_steps": 50,
                "max_steps": 500,  # Prevent runaway training
                "save_steps": 100,
                "logging_steps": 10
            },
            "output_dir": "lora_adapters_mistral/emergency_adapter",
            "training_data_path": None,  # Will be set by caller
            "description": "Emergency fallback configuration for stable training"
        }
    
    def _validate_and_complete_config(self, config):
        """CRITICAL FIX: Validate and complete training configuration"""
        import logging
        validate_logger = logging.getLogger(__name__)
        
        # Ensure all required top-level keys exist
        if "base_model" not in config:
            config["base_model"] = "microsoft/DialoGPT-small"
            validate_logger.warning("⚠️ Missing base_model in config, using default")
        
        if "lora" not in config or not isinstance(config["lora"], dict):
            config["lora"] = {
                "r": 16,
                "alpha": 32,
                "dropout": 0.1,
                "target_modules": ["c_attn"]
            }
            validate_logger.warning("⚠️ Missing or invalid LoRA config, using default")
        else:
            # Validate LoRA sub-fields
            lora_defaults = {
                "r": 16,
                "alpha": 32,
                "dropout": 0.1,
                "target_modules": ["c_attn"]
            }
            for key, default_value in lora_defaults.items():
                if key not in config["lora"]:
                    config["lora"][key] = default_value
                    validate_logger.warning(f"⚠️ Missing LoRA.{key}, using default: {default_value}")
        
        if "training" not in config or not isinstance(config["training"], dict):
            config["training"] = {
                "epochs": 3,
                "batch_size": 4,
                "learning_rate": 0.0002,
                "gradient_accumulation_steps": 2,
                "warmup_steps": 100
            }
            validate_logger.warning("⚠️ Missing or invalid training config, using default")
        else:
            # Validate training sub-fields
            training_defaults = {
                "epochs": 3,
                "batch_size": 4,
                "learning_rate": 0.0002,
                "gradient_accumulation_steps": 2,
                "warmup_steps": 100,
                "max_steps": 1000,
                "save_steps": 100,
                "logging_steps": 10
            }
            for key, default_value in training_defaults.items():
                if key not in config["training"]:
                    config["training"][key] = default_value
                    validate_logger.warning(f"⚠️ Missing training.{key}, using default: {default_value}")
        
        if "output_dir" not in config:
            config["output_dir"] = "lora_adapters_mistral/validated_adapter"
            validate_logger.warning("⚠️ Missing output_dir in config, using default")
        
        return config
    
    def _start_training_thread(self, training_config):
        """
        ⚠️ DEPRECATED: Start the training in a separate thread with robust error handling
        
        CRITICAL BUG: This method creates QThreads from background threads, violating
        Qt's threading model. This causes crashes and unpredictable behavior.
        
        Use TrainingOrchestrator.start_training() instead.
        """
        import logging
        thread_logger = logging.getLogger(__name__)
        
        try:
            # CRITICAL FIX: Validate training config before starting thread
            if not training_config:
                error_msg = "❌ No training configuration provided"
                thread_logger.error(error_msg)
                self.errorOccurred.emit(error_msg)
                return
            
            # Validate essential fields
            required_fields = ["base_model", "lora", "training", "output_dir"]
            missing_fields = [field for field in required_fields if field not in training_config]
            if missing_fields:
                error_msg = f"❌ Missing required training config fields: {missing_fields}"
                thread_logger.error(error_msg)
                self.errorOccurred.emit(error_msg)
                return
            
            # Import here to catch import errors properly
            try:
                from .core.golden_path_trainer import GoldenPathTrainer
            except ImportError as e:
                error_msg = f"❌ Cannot import GoldenPathTrainer: {str(e)}"
                thread_logger.error(error_msg)
                self.errorOccurred.emit(error_msg)
                return
            
            # CRITICAL FIX: Cleanup previous trainer if exists
            if hasattr(self, 'trainer') and self.trainer:
                thread_logger.info("🧹 Cleaning up previous trainer")
                try:
                    if self.trainer.isRunning():
                        self.trainer.stop()
                        self.trainer.wait(3000)  # Wait up to 3 seconds
                    self.trainer = None
                except Exception as e:
                    thread_logger.warning(f"⚠️ Error cleaning up previous trainer: {e}")
            
            # Create trainer with error handling
            try:
                self.trainer = GoldenPathTrainer(training_config)
            except Exception as e:
                error_msg = f"❌ Failed to create trainer: {str(e)}"
                thread_logger.error(error_msg)
                self.errorOccurred.emit(error_msg)
                return
            
            # CRITICAL FIX: Connect signals with error handling
            try:
                self.trainer.progress.connect(self.updateStatus.emit)
                self.trainer.finished.connect(self._on_training_finished)
                
                # Optional GPU utilization signal (may not exist)
                if hasattr(self.trainer, 'gpu_utilization'):
                    self.trainer.gpu_utilization.connect(self._on_gpu_utilization_update)
                    
            except Exception as e:
                thread_logger.warning(f"⚠️ Error connecting trainer signals: {e}")
                # Continue anyway, basic training might still work
            
            # Start training
            try:
                self.trainer.start()
                thread_logger.info("🚀 Training thread started with Golden Path trainer")
            except Exception as e:
                error_msg = f"❌ Failed to start training thread: {str(e)}"
                thread_logger.error(error_msg)
                self.errorOccurred.emit(error_msg)
                return
            
        except Exception as e:
            error_msg = f"❌ Critical error in training thread setup: {str(e)}"
            thread_logger.error(error_msg, exc_info=True)
            self.errorOccurred.emit(error_msg)
    
    @pyqtSlot(bool, str)
    def _on_training_finished(self, success, message):
        """Handle training completion with robust cleanup"""
        import logging
        finish_logger = logging.getLogger(__name__)
        
        try:
            if success:
                finish_logger.info(f"✅ Training completed successfully: {message}")
                self.updateStatus.emit(f"✅ Training completed! {message}")
            else:
                finish_logger.error(f"❌ Training failed: {message}")
                self.errorOccurred.emit(f"Training failed: {message}")
                
            # CRITICAL FIX: Always clean up trainer resources
            if hasattr(self, 'trainer') and self.trainer:
                finish_logger.info("🧹 Cleaning up trainer resources")
                try:
                    if self.trainer.isRunning():
                        self.trainer.wait(1000)  # Wait up to 1 second
                    self.trainer = None
                except Exception as e:
                    finish_logger.warning(f"⚠️ Error during trainer cleanup: {e}")
                    
        except Exception as e:
            finish_logger.error(f"❌ Error in training completion handler: {e}")
            self.errorOccurred.emit(f"Error handling training completion: {str(e)}")
    
    @pyqtSlot(float)
    def _on_gpu_utilization_update(self, utilization):
        """Monitor GPU utilization in real-time with error handling"""
        import logging
        gpu_logger = logging.getLogger(__name__)
        
        try:
            gpu_logger.info(f"🔥 GPU Utilization: {utilization:.1f}%")
            if utilization > 90:
                self.updateStatus.emit(f"🔥 BEAST MODE: GPU at {utilization:.1f}% - MAXIMUM POWER!")
            else:
                self.updateStatus.emit(f"⚡ GPU Utilization: {utilization:.1f}%")
        except Exception as e:
            gpu_logger.warning(f"⚠️ Error updating GPU utilization: {e}")
    
    @pyqtSlot(result=str)
    def getTrainingConfiguration(self):
        """
        🚀 Phase 2: Get saved training configuration for UI pre-population
        
        Returns the last used training settings so users don't have to
        reconfigure everything each time.
        """

        try:
            import time
            import logging
            method_logger = logging.getLogger(__name__)
            
            settings = self._load_user_settings()
            training_config = settings.get("training", {})
            
            # Provide sensible defaults
            default_config = {
                "selected_files": [],
                "adapter_name": f"my_adapter_{int(time.time())}",
                "base_model": "microsoft/DialoGPT-small",
                "training_preset": "standard_training",
                "last_used": time.time()
            }
            
            # Merge with saved config
            result_config = {**default_config, **training_config}
            
            method_logger.info(f"🔧 Retrieved training configuration: {result_config}")
            json_result = json.dumps(result_config, ensure_ascii=False)
            # Remove any null bytes that might cause JavaScript issues
            clean_json = json_result.replace('\x00', '').replace('\0', '')
            method_logger.info(f"🔧 JSON result: {clean_json[:200]}...")
            # Use special prefix to bypass PyQt's automatic JSON conversion
            return f"JSON_STRING:{clean_json}"
            
        except Exception as e:
            import logging
            import traceback
            error_logger = logging.getLogger(__name__)
            error_logger.error(f"❌ Error getting training configuration: {e}")
            error_logger.error(f"❌ Traceback: {traceback.format_exc()}")
            # Return safe default as guaranteed valid JSON
            import time
            default_config = {
                "selected_files": [],
                "adapter_name": f"my_adapter_{int(time.time())}",
                "base_model": "microsoft/DialoGPT-small", 
                "training_preset": "standard_training",
                "last_used": time.time()
            }
            try:
                fallback_json = json.dumps(default_config, ensure_ascii=False)
                return f"JSON_STRING:{fallback_json}"
            except Exception as json_error:
                error_logger.error(f"❌ JSON serialization failed: {json_error}")
                # Return minimal valid JSON as absolute fallback
                return 'JSON_STRING:{"selected_files":[],"adapter_name":"fallback_adapter","base_model":"microsoft/DialoGPT-small","training_preset":"standard_training"}'

    @pyqtSlot(str)
    def saveTrainingConfiguration(self, config_json: str):
        """
        🚀 Phase 2: Save training configuration for persistence
        
        Saves user's training preferences so they persist across sessions.
        """
        try:
            config = json.loads(config_json)
            
            # Load current user settings
            settings = self._load_user_settings()
            
            # Update training section
            import time
            settings["training"] = {
                **settings.get("training", {}),
                **config,
                "last_saved": time.time()
            }
            
            # Save back to file
            self._save_user_settings(settings)
            
            # Emit signal for UI feedback
            self.trainingConfigSaved.emit({
                "success": True,
                "message": "Training configuration saved",
                "config": config
            })
            
            logger.info(f"💾 Training configuration saved: {config}")
            
        except Exception as e:
            logger.error(f"❌ Error saving training configuration: {e}")
            self.trainingConfigSaved.emit({
                "success": False,
                "message": f"Error saving configuration: {str(e)}",
                "config": {}
            })

    @pyqtSlot(result=str)
    def getAvailableBaseModels(self):
        """
        🚀 Phase 2: Get list of available base models for user selection
        
        Returns curated list of base models suitable for fine-tuning.
        """

        try:
            import logging
            method_logger = logging.getLogger(__name__)
            
            # Curated list of reliable base models for fine-tuning
            base_models = [
                {
                    "id": "microsoft/DialoGPT-small",
                    "name": "DialoGPT Small", 
                    "description": "Fast training, good for testing (117M params)",
                    "size": "small",
                    "recommended": True
                },
                {
                    "id": "microsoft/DialoGPT-medium",
                    "name": "DialoGPT Medium",
                    "description": "Balanced performance and speed (345M params)",
                    "size": "medium", 
                    "recommended": False
                },
                {
                    "id": "distilgpt2",
                    "name": "DistilGPT-2",
                    "description": "Lightweight GPT-2 variant (82M params)",
                    "size": "small",
                    "recommended": False
                },
                {
                    "id": "gpt2",
                    "name": "GPT-2",
                    "description": "Original GPT-2 base model (124M params)",
                    "size": "small",
                    "recommended": False
                }
            ]
            
            method_logger.info(f"📋 Returning {len(base_models)} available base models")
            json_result = json.dumps(base_models, ensure_ascii=False)
            # Remove any null bytes that might cause JavaScript issues
            clean_json = json_result.replace('\x00', '').replace('\0', '')
            # Use special prefix to bypass PyQt's automatic JSON conversion
            return f"JSON_STRING:{clean_json}"
            
        except Exception as e:
            import logging
            import traceback
            error_logger = logging.getLogger(__name__)
            error_logger.error(f"❌ Error getting base models: {e}")
            error_logger.error(f"❌ Traceback: {traceback.format_exc()}")
            # 🚫 NO HARDCODED MODEL FALLBACK - Raise error instead
            error_logger.error("🚫 HARDCODED MODEL FALLBACK DISABLED - No demo models")
            error_logger.error("❌ Model discovery failed - cannot provide hardcoded model list")
            error_logger.error("🚨 Real model discovery required - no placeholder models available")
            raise Exception("Model discovery failed - no hardcoded models available")

    @pyqtSlot(result=str)
    def getTrainingPresets(self):
        """
        🚀 Phase 2: Get available training presets for user selection
        
        Returns different training configurations optimized for different use cases.
        """

        try:
            import logging
            method_logger = logging.getLogger(__name__)
            
            presets = [
                {
                    "id": "quick_training",
                    "name": "Quick Training",
                    "description": "Fast training for testing (1 epoch, small batch)",
                    "estimated_time": "5-15 minutes",
                    "recommended_for": "Testing and experimentation",
                    "config": {
                        "epochs": 1,
                        "batch_size": 2,
                        "learning_rate": 0.0001,
                        "max_steps": 100
                    }
                },
                {
                    "id": "standard_training", 
                    "name": "Standard Training",
                    "description": "Balanced training for most use cases (2 epochs)",
                    "estimated_time": "15-45 minutes",
                    "recommended_for": "General fine-tuning",
                    "recommended": True,
                    "config": {
                        "epochs": 2,
                        "batch_size": 4,
                        "learning_rate": 0.0002,
                        "max_steps": 500
                    }
                },
                {
                    "id": "intensive_training",
                    "name": "Intensive Training", 
                    "description": "Deep training for best results (5 epochs, larger batch)",
                    "estimated_time": "1-3 hours",
                    "recommended_for": "Production models",
                    "config": {
                        "epochs": 5,
                        "batch_size": 8,
                        "learning_rate": 0.0003,
                        "max_steps": 1000
                    }
                }
            ]
            
            method_logger.info(f"⚙️ Returning {len(presets)} training presets")
            json_result = json.dumps(presets, ensure_ascii=False)
            # Remove any null bytes that might cause JavaScript issues
            clean_json = json_result.replace('\x00', '').replace('\0', '')
            method_logger.info(f"⚙️ JSON result: {clean_json[:200]}...")
            # Use special prefix to bypass PyQt's automatic JSON conversion
            return f"JSON_STRING:{clean_json}"
            
        except Exception as e:
            import logging
            import traceback
            error_logger = logging.getLogger(__name__)
            error_logger.error(f"❌ Error getting training presets: {e}")
            error_logger.error(f"❌ Traceback: {traceback.format_exc()}")
            # Return minimal fallback as guaranteed valid JSON
            try:
                fallback = [{
                    "id": "standard_training",
                    "name": "Standard Training",
                    "description": "Default training configuration",
                    "estimated_time": "15-45 minutes",
                    "recommended": True,
                    "config": {"epochs": 2, "batch_size": 4, "learning_rate": 0.0002}
                }]
                fallback_json = json.dumps(fallback, ensure_ascii=False)
                return f"JSON_STRING:{fallback_json}"
            except Exception as json_error:
                error_logger.error(f"❌ JSON serialization failed: {json_error}")
                # Return minimal valid JSON string as absolute fallback
                return 'JSON_STRING:[{"id":"standard_training","name":"Standard Training","description":"Default training","estimated_time":"15-45 minutes","recommended":true,"config":{"epochs":2,"batch_size":4,"learning_rate":0.0002}}]'
    
    async def _load_user_settings(self) -> Dict[str, Any]:
        """Load user settings from file"""
        try:
            settings_file = Path("user_data/user_settings.json")
            if settings_file.exists():
                content = await async_file_read(settings_file)
                # Remove null bytes that might cause issues
                clean_content = content.replace('\x00', '').replace('\0', '')
                return json.loads(clean_content)
            return {}
        except Exception as e:
            logger.error(f"❌ Error loading user settings: {e}")
            return {}
    
    def _save_user_settings(self, settings: Dict[str, Any]):
        """Save user settings to file"""
        try:
            settings_file = Path("user_data/user_settings.json")
            settings_file.parent.mkdir(exist_ok=True)
            
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"❌ Error saving user settings: {e}")
            raise
    
    @pyqtSlot(result=str)
    def getUploadedFiles(self):
        """
        🔍 Get list of uploaded files for UI display
        """
        import logging
        files_logger = logging.getLogger(__name__)
        
        try:
            from pathlib import Path
            
            # Check both possible directories for uploaded books
            files_info = []
            
            # Check root level uploaded_books directory
            root_books_dir = Path("uploaded_books")
            if root_books_dir.exists():
                for file_path in root_books_dir.glob("*"):
                    if file_path.is_file() and file_path.suffix.lower() in ['.pdf', '.txt', '.docx']:
                        file_size = file_path.stat().st_size
                        files_info.append({
                            "name": file_path.name,
                            "size": file_size,
                            "path": str(file_path)
                        })
            
            # Also check data/uploaded_books directory
            data_books_dir = Path("data/uploaded_books")
            if data_books_dir.exists():
                for file_path in data_books_dir.glob("*"):
                    if file_path.is_file() and file_path.suffix.lower() in ['.pdf', '.txt', '.docx']:
                        # Avoid duplicates if file exists in both directories
                        if not any(f["name"] == file_path.name for f in files_info):
                            file_size = file_path.stat().st_size
                            files_info.append({
                                "name": file_path.name,
                                "size": file_size,
                                "path": str(file_path)
                            })
            
            files_logger.info(f"📚 Found {len(files_info)} uploaded files")
            json_result = json.dumps(files_info, ensure_ascii=False)
            files_logger.info(f"🔧 Returning JSON: {json_result[:100]}...")
            # Use special prefix to bypass PyQt's automatic JSON conversion
            result = f"JSON_STRING:{json_result}"
            files_logger.info(f"🔧 Final result type: {type(result)}, length: {len(result)}")
            return result
            
        except Exception as e:
            files_logger.error(f"❌ Error getting uploaded files: {e}")
            return json.dumps([])
    
    @pyqtSlot(str)
    def _emit_error(self, error_message):
        """Thread-safe error emission"""
        self.errorOccurred.emit(error_message)

    def _simple_training_fallback(self, training_params):
        """Simple fallback training approach when GoldenPathTrainer is not available"""
        import logging
        import threading
        fallback_logger = logging.getLogger(__name__)

        # CRITICAL FIX: Move ALL processing to background thread to prevent UI blocking
        def fallback_process():
            try:
                # CRITICAL FIX: Make it very clear this is fallback mode
                self.updateStatus.emit("⚠️ FALLBACK MODE: Document processing only (full training requires additional dependencies)...")

                # Just process the documents without actual training for now
                from .core.document_processor import AdvancedDocumentProcessor
                from pathlib import Path

                # Find all uploaded files
                uploaded_files = []

                # Check root level uploaded_books directory
                root_books_dir = Path("uploaded_books")
                if root_books_dir.exists():
                    uploaded_files.extend(list(root_books_dir.glob("*.pdf")) + list(root_books_dir.glob("*.txt")))
                    fallback_logger.info(f"📚 Found {len(uploaded_files)} files in root uploaded_books")

                if not uploaded_files:
                    self.errorOccurred.emit("No supported files found in uploaded books directories.")
                    return

                fallback_logger.info(f"📚 Total {len(uploaded_files)} unique files to process")

                # Create processing directory
                processing_dir = Path("data/processed_docs")
                processing_dir.mkdir(exist_ok=True)

                # Process documents
                processor = AdvancedDocumentProcessor()
                file_paths = [str(f) for f in uploaded_files]

                self.updateStatus.emit("🔄 FALLBACK MODE: Processing uploaded books...")

                # Process documents without training for now
                result = processor.process_documents_advanced(
                    file_paths,
                    str(processing_dir),
                    chunk_size=500
                )

                fallback_logger.info(f"📊 Processed {result['stats']['successful_files']} files into {result['stats']['total_chunks']} chunks")

                # CRITICAL FIX: Make it very clear this is fallback mode
                self.updateStatus.emit("✅ FALLBACK MODE Complete: Document processing finished. Full training requires additional dependencies.")

            except Exception as e:
                fallback_logger.error(f"❌ Simple training fallback failed: {e}")
                self.errorOccurred.emit(f"Training fallback failed: {str(e)}")

        # Start the background processing thread
        thread = threading.Thread(target=fallback_process, daemon=True)
        thread.start()
    
    def _force_gpu_beast_mode_training(self, training_params):
        """🚀 FORCE GPU BEAST MODE - Direct training without fallbacks"""
        import logging
        import threading
        beast_logger = logging.getLogger(__name__)

        # CRITICAL FIX: Move ALL processing to background thread to prevent UI blocking
        def beast_mode_process():
            try:
                from .core.document_processor import AdvancedDocumentProcessor
                from .core.golden_path_trainer import GoldenPathTrainer
                from pathlib import Path

                self.updateStatus.emit("🚀 FORCING GPU BEAST MODE TRAINING - NO FALLBACKS!")

                # Find all uploaded files
                uploaded_files = []

                # Check root level uploaded_books directory
                root_books_dir = Path("uploaded_books")
                if root_books_dir.exists():
                    uploaded_files.extend(list(root_books_dir.glob("*.pdf")) + list(root_books_dir.glob("*.txt")))
                    beast_logger.info(f"📚 Found {len(uploaded_files)} files in root uploaded_books")

                if not uploaded_files:
                    self.errorOccurred.emit("No supported files found for GPU BEAST MODE.")
                    return

                beast_logger.info(f"📚 BEAST MODE: Processing {len(uploaded_files)} files for GPU training")

                # Create processing directory
                processing_dir = Path("data/processed_docs")
                processing_dir.mkdir(exist_ok=True)

                # Process documents with smart caching
                processor = AdvancedDocumentProcessor()
                file_paths = [str(f) for f in uploaded_files]

                self.updateStatus.emit("🔄 BEAST MODE: Processing documents for GPU training...")

                # Process documents (uses smart caching)
                result = processor.process_documents_advanced(
                    file_paths,
                    str(processing_dir),
                    chunk_size=500
                )

                beast_logger.info(f"📊 BEAST MODE: Processed {result['stats']['successful_files']} files into {result['stats']['total_chunks']} chunks")

                # Get BEAST MODE training configuration
                training_config = self._get_beast_mode_config(training_params)

                # Ensure training data path exists
                training_data_path = processing_dir / "training_dataset.jsonl"
                if not training_data_path.exists():
                    raise FileNotFoundError(f"Training data not found: {training_data_path}")

                training_config["training_data_path"] = str(training_data_path)

                # 🚀 START GPU BEAST MODE DIRECTLY
                self.updateStatus.emit("💥 STARTING GPU BEAST MODE - 100% UTILIZATION!")
                self._start_beast_mode_training_thread(training_config)

            except Exception as e:
                beast_logger.error(f"❌ GPU BEAST MODE failed: {e}")
                self.errorOccurred.emit(f"GPU BEAST MODE failed: {str(e)}")

        # Start the background processing thread
        thread = threading.Thread(target=beast_mode_process, daemon=True)
        thread.start()
    
    def _get_beast_mode_config(self, training_params):
        """Get optimized BEAST MODE configuration for maximum GPU utilization"""
        return {
            "base_model": "microsoft/DialoGPT-small",  # Smaller model for faster training
            "lora": {
                "r": 16,
                "alpha": 32,
                "dropout": 0.1,
                "target_modules": ["c_attn"]
            },
            "training": {
                "epochs": 2,  # Quick training for immediate GPU utilization
                "batch_size": 16,  # Larger batch size for GPU utilization
                "learning_rate": 3e-4,  # Slightly higher learning rate
                "gradient_accumulation_steps": 1,  # Direct training
                "warmup_steps": 20
            },
            "output_dir": "lora_adapters_mistral/beast_mode_adapter"
        }
    
    def _start_beast_mode_training_thread(self, training_config):
        """🚀 Start direct GPU BEAST MODE training"""
        import logging
        thread_logger = logging.getLogger(__name__)
        
        try:
            
            from .core.golden_path_trainer import GoldenPathTrainer
            
            # Create BEAST MODE trainer
            self.trainer = GoldenPathTrainer(training_config)
            
            # Connect signals for real-time monitoring
            self.trainer.progress.connect(self.updateStatus.emit)
            self.trainer.finished.connect(self._on_beast_mode_training_finished)
            self.trainer.gpu_utilization.connect(self._on_gpu_utilization_update)
            
            # 🔥 START BEAST MODE TRAINING
            self.trainer.start()
            thread_logger.info("🚀 GPU BEAST MODE TRAINING STARTED - Maximum utilization!")
            
        except Exception as e:
            thread_logger.error(f"❌ Failed to start BEAST MODE training: {e}")
            self.errorOccurred.emit(f"BEAST MODE startup failed: {str(e)}")
    
    @pyqtSlot(bool, str)
    def _on_beast_mode_training_finished(self, success, message):
        """Handle BEAST MODE training completion"""
        import logging
        beast_finish_logger = logging.getLogger(__name__)
        
        if success:
            beast_finish_logger.info(f"🚀 BEAST MODE TRAINING COMPLETED: {message}")
            self.updateStatus.emit(f"🚀 BEAST MODE COMPLETE! {message}")
        else:
            beast_finish_logger.error(f"❌ BEAST MODE TRAINING FAILED: {message}")
            self.errorOccurred.emit(f"BEAST MODE failed: {message}")
    
    def _fire_enhanced_training(self, training_params):
        """🔥 FIRE-enhanced training with real-time monitoring and beautiful visuals"""
        import logging
        fire_logger = logging.getLogger(__name__)
        
        try:
            
            from .core.document_processor import AdvancedDocumentProcessor
            from .core.golden_path_trainer import GoldenPathTrainer
            from pathlib import Path
            
            self.updateStatus.emit("🔥 Starting FIRE-enhanced training with real-time monitoring...")
            
            # Find all uploaded files
            uploaded_files = []
            
            # Check root level uploaded_books directory
            root_books_dir = Path("uploaded_books")
            if root_books_dir.exists():
                uploaded_files.extend(list(root_books_dir.glob("*.pdf")) + list(root_books_dir.glob("*.txt")))
                fire_logger.info(f"📚 Found {len(uploaded_files)} files in root uploaded_books")
            
            if not uploaded_files:
                self.errorOccurred.emit("No supported files found in uploaded books directories.")
                return
            
            fire_logger.info(f"📚 Total {len(uploaded_files)} unique files to process for FIRE training")
            
            # Create processing directory
            processing_dir = Path("data/processed_docs")
            processing_dir.mkdir(exist_ok=True)
            
            # Process documents
            processor = AdvancedDocumentProcessor()
            file_paths = [str(f) for f in uploaded_files]
            
            self.updateStatus.emit("🔄 Processing uploaded books for FIRE training...")
            
            # Process documents in a separate thread to prevent UI blocking
            import threading
            def fire_process_and_train():
                # Capture logger in local scope for nested function access
                import logging
                local_logger = logging.getLogger(__name__)
                
                try:
                    # Process documents
                    result = processor.process_documents_advanced(
                        file_paths, 
                        str(processing_dir),
                        chunk_size=500
                    )
                    
                    # 🔥 CRITICAL FIX: Add a check to ensure 'result' is not None and has the expected structure
                    if not result or 'stats' not in result:
                        error_message = "Document processing failed to return valid results."
                        local_logger.error(f"❌ {error_message}")
                        # Use QMetaObject to safely emit the signal from a non-main thread
                        QMetaObject.invokeMethod(
                            self, "_emit_error", 
                            Qt.QueuedConnection,
                            Q_ARG(str, error_message)
                        )
                        return # Stop execution

                    local_logger.info(f"📊 Processed {result['stats']['successful_files']} files into {result['stats']['total_chunks']} chunks")
                    
                    # 🔥 CRITICAL FIX: Check if any chunks were actually generated before proceeding
                    if result['stats']['total_chunks'] == 0:
                        error_message = "No training data could be generated from the uploaded books. Please check the file contents and logs."
                        local_logger.error(f"❌ {error_message}")
                        QMetaObject.invokeMethod(
                            self, "_emit_error", 
                            Qt.QueuedConnection,
                            Q_ARG(str, error_message)
                        )
                        return # Stop execution

                    # Get training configuration
                    training_config = self._get_training_config(training_params)
                    
                    # Ensure training data path exists
                    training_data_path = processing_dir / "training_dataset.jsonl"
                    if not training_data_path.exists():
                        raise FileNotFoundError(f"Training data not found: {training_data_path}")
                    
                    training_config["training_data_path"] = str(training_data_path)
                    
                    # 🔥 Start FIRE-enhanced training with monitoring
                    self.updateStatus.emit("🔥 Starting FIRE-enhanced Golden Path training...")
                    self._start_fire_training_thread(training_config)
                    
                except Exception as e:
                    local_logger.error(f"❌ FIRE processing failed: {e}")
                    from PyQt5.QtCore import QMetaObject, Qt, Q_ARG
                    QMetaObject.invokeMethod(
                        self, "_emit_error", 
                        Qt.QueuedConnection,
                        Q_ARG(str, f"FIRE training setup failed: {str(e)}")
                    )
            
            thread = threading.Thread(target=fire_process_and_train, daemon=True)
            thread.start()
            
        except Exception as e:
            import logging
            method_logger = logging.getLogger(__name__)
            method_logger.error(f"❌ FIRE-enhanced training setup failed: {e}")
            # FORCE GPU BEAST MODE - NO FALLBACK!
            method_logger.info("🚀 FORCING GPU BEAST MODE TRAINING...")
            self._force_gpu_beast_mode_training(training_params)
    
    def _start_fire_training_thread(self, training_config):
        """🔥 Start FIRE-enhanced training with real-time monitoring"""
        import logging
        fire_thread_logger = logging.getLogger(__name__)
        
        try:
            
            from .core.golden_path_trainer import GoldenPathTrainer
            
            # Create FIRE-enhanced trainer
            self.trainer = GoldenPathTrainer(training_config)
            
            # 🔥 CRITICAL: Connect FIRE monitoring BEFORE starting training
            if self.fire_integration:
                fire_estimator = self.fire_integration.create_fire_estimator()
                
                # Set FIRE estimator on the trainer for callback integration
                self.trainer.set_fire_estimator(fire_estimator)
                
                # Connect FIRE web signals to the bridge
                fire_widget = self.fire_integration.get_web_widget()
                fire_widget.trainingStarted.connect(self.fireTrainingStarted.emit)
                fire_widget.initialEstimate.connect(self.fireInitialEstimate.emit)
                fire_widget.realtimeUpdate.connect(self.fireRealtimeUpdate.emit)
                fire_widget.trainingCompleted.connect(self.fireTrainingCompleted.emit)
                
                fire_thread_logger.info("🔥 FIRE monitoring signals connected to web UI")
            
            # Connect trainer signals
            self.trainer.progress.connect(self.updateStatus.emit)
            self.trainer.finished.connect(self._on_fire_training_finished)
            
            # Start FIRE-enhanced training
            self.trainer.start()
            fire_thread_logger.info("🔥 FIRE-enhanced training thread started with real-time monitoring")
            
        except Exception as e:
            fire_thread_logger.error(f"❌ Failed to start FIRE training thread: {e}")
            # FORCE GPU BEAST MODE - NO FALLBACK!
            self._force_gpu_beast_mode_training({'files': []})
    
    @pyqtSlot(bool, str)
    def _on_fire_training_finished(self, success, message):
        """Handle FIRE training completion"""
        import logging
        fire_finish_logger = logging.getLogger(__name__)
        
        if success:
            fire_finish_logger.info(f"🔥 FIRE training completed successfully: {message}")
            self.updateStatus.emit(f"🔥 FIRE training completed! {message}")
            
            # Notify FIRE monitoring system
            if self.fire_integration:
                fire_widget = self.fire_integration.get_web_widget()
                fire_widget.training_completed(True, message)
        else:
            fire_finish_logger.error(f"❌ FIRE training failed: {message}")
            self.errorOccurred.emit(f"FIRE training failed: {message}")
            
            # Notify FIRE monitoring system
            if self.fire_integration:
                fire_widget = self.fire_integration.get_web_widget()
                fire_widget.training_completed(False, message)
    
    # 🎯 Phase 3: Enterprise Training Management Methods
    
    @pyqtSlot(result=str)
    def getTrainingHistory(self):
        """Get training history for management UI"""
        try:
            from .core.training_management import TrainingManagementSystem
            training_manager = TrainingManagementSystem()
            
            dashboard_data = training_manager.get_training_dashboard_data()
            
            return json.dumps(dashboard_data, ensure_ascii=False, separators=(',', ':'))
            
        except Exception as e:
            logger.error(f"❌ Failed to get training history: {e}")
            return json.dumps({"error": str(e)})
    
    @pyqtSlot(result=str)
    def getRecentTrainingRuns(self):
        """Get recent training runs for display"""
        try:
            from .core.training_management import TrainingManagementSystem
            training_manager = TrainingManagementSystem()
            
            recent_runs = training_manager.get_recent_training_runs(limit=20)
            
            return json.dumps({"runs": recent_runs}, ensure_ascii=False, separators=(',', ':'))
            
        except Exception as e:
            logger.error(f"❌ Failed to get recent training runs: {e}")
            return json.dumps({"error": str(e), "runs": []})
    
    @pyqtSlot(result=str)
    def getTopPerformingAdapters(self):
        """Get top performing adapters for management UI"""
        try:
            from .core.training_management import TrainingManagementSystem
            training_manager = TrainingManagementSystem()
            
            top_adapters = training_manager.get_top_performing_adapters(limit=10)
            
            return json.dumps({"adapters": top_adapters}, ensure_ascii=False, separators=(',', ':'))
            
        except Exception as e:
            logger.error(f"❌ Failed to get top performing adapters: {e}")
            return json.dumps({"error": str(e), "adapters": []})
    
    @pyqtSlot(str, bool, result=str)
    def deleteAdapter(self, adapter_name: str, delete_files: bool):
        """Delete a trained adapter"""
        try:
            from .core.training_management import TrainingManagementSystem
            training_manager = TrainingManagementSystem()
            
            result = training_manager.delete_adapter(adapter_name, delete_files)
            
            if result.get("success"):
                self.trainingHistoryUpdated.emit({"action": "adapter_deleted", "adapter_name": adapter_name})
            
            return json.dumps(result, ensure_ascii=False, separators=(',', ':'))
            
        except Exception as e:
            logger.error(f"❌ Failed to delete adapter: {e}")
            return json.dumps({"success": False, "error": str(e)})
    
    @pyqtSlot(result=str)
    def cleanupFailedAdapters(self):
        """Clean up failed training runs and orphaned files"""
        try:
            from .core.training_management import TrainingManagementSystem
            training_manager = TrainingManagementSystem()
            
            result = training_manager.cleanup_failed_adapters()
            
            if result.get("success"):
                self.trainingHistoryUpdated.emit({"action": "cleanup_completed", "result": result})
            
            return json.dumps(result, ensure_ascii=False, separators=(',', ':'))
            
        except Exception as e:
            logger.error(f"❌ Failed to cleanup failed adapters: {e}")
            return json.dumps({"success": False, "error": str(e)})
    
    @pyqtSlot(result=str)
    def getTrainingStatistics(self):
        """Get comprehensive training statistics"""
        try:
            from .core.question_history_storage import QuestionHistoryStorage
            storage = QuestionHistoryStorage()
            
            stats = storage.get_training_statistics()
            
            return json.dumps(stats, ensure_ascii=False, separators=(',', ':'))
            
        except Exception as e:
            logger.error(f"❌ Failed to get training statistics: {e}")
            return json.dumps({"error": str(e)})


class WebEnginePage(QWebEnginePage):
    """Custom web engine page with error handling"""
    
    def javaScriptConsoleMessage(self, level, message, lineNumber, sourceID):
        """Handle JavaScript console messages safely"""
        # Only log errors and warnings to help debug issues
        if level >= QWebEnginePage.WarningMessageLevel:
            # Sanitize the message to prevent null byte issues
            safe_message = message.replace('\x00', '').replace('\0', '') if message else ''
            safe_source = sourceID.replace('\x00', '').replace('\0', '') if sourceID else 'unknown'
            logger.warning(f"JS Console: {safe_message} (line {lineNumber} in {safe_source})")


class KnowledgeAppWebEngine(QWebEngineView):
    """Main application window using QtWebEngine"""
    
    def __init__(self):
        super().__init__()
        self.bridge = PythonBridge(self)
        self.web_engine_bridge = WebEngineBridge(self, mcq_manager=self.bridge.mcq_manager, training_manager=self.bridge.training_manager)
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Knowledge App - TURBO Web UI")
        self.setGeometry(100, 100, 1200, 800)

        # Set up web channel for Python-JS communication
        self.channel = QWebChannel()
        self.channel.registerObject("pythonBridge", self.bridge)
        self.channel.registerObject("webEngineBridge", self.web_engine_bridge)
        self.setPage(WebEnginePage())
        self.page().setWebChannel(self.channel)

        # Load the HTML interface
        html_path = Path(__file__).parent / "web" / "app.html"
        if html_path.exists():
            self.load(QUrl.fromLocalFile(str(html_path)))
        else:
            import logging
            init_logger = logging.getLogger(__name__)
            init_logger.error(f"HTML file not found: {html_path}")
            self._show_error_page()

        # Connect bridge signals
        self.bridge.updateStatus.connect(self._update_status_bar)
        self.web_engine_bridge.trainingProgress.connect(self._handle_training_progress)
        self.web_engine_bridge.trainingComplete.connect(self._handle_training_complete)
        self.web_engine_bridge.trainingError.connect(self._handle_training_error)

    def _handle_training_progress(self, message):
        self.page().runJavaScript(f"window.handleTrainingProgress('{message}');")

    def _handle_training_complete(self, message):
        self.page().runJavaScript(f"window.handleTrainingComplete('{message}');")

    def _handle_training_error(self, message):
        self.page().runJavaScript(f"window.handleTrainingError('{message}');")

    def _update_status_bar(self, message):
        """Update status bar message"""
        # Sanitize message to remove null bytes and escape quotes
        if message:
            sanitized_message = message.replace('\x00', '').replace('\0', '').replace("'", "\'").replace('"', '\"')
        else:
            sanitized_message = ''
        self.page().runJavaScript(f"updateStatus('{sanitized_message}');")

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Knowledge App - TURBO Web UI")
        self.setGeometry(100, 100, 1200, 800)
        
        # Set up web channel for Python-JS communication
        self.channel = QWebChannel()
        self.channel.registerObject("pythonBridge", self.bridge)
        self.setPage(WebEnginePage())
        self.page().setWebChannel(self.channel)
        
        # Load the HTML interface
        html_path = Path(__file__).parent / "web" / "app.html"
        if html_path.exists():
            self.load(QUrl.fromLocalFile(str(html_path)))
        else:
            import logging
            init_logger = logging.getLogger(__name__)
            init_logger.error(f"HTML file not found: {html_path}")
            self._show_error_page()
            
        # Connect bridge signals
        self.bridge.updateStatus.connect(self._update_status_bar)
        
    def _update_status_bar(self, message):
        """Update status bar message"""
        # Sanitize message to remove null bytes and escape quotes
        if message:
            sanitized_message = message.replace('\x00', '').replace('\0', '').replace("'", "\\'").replace('"', '\\"')
        else:
            sanitized_message = ''
        self.page().runJavaScript(f"updateStatus('{sanitized_message}');")
        
    def _show_error_page(self):
        """Show error page when HTML file is not found"""
        error_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Error</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    margin: 0;
                    background-color: #f0f0f0;
                }
                .error-container {
                    text-align: center;
                    padding: 2rem;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                h1 { color: #d32f2f; }
                p { color: #666; }
            </style>
        </head>
        <body>
            <div class="error-container">
                <h1>Error Loading Application</h1>
                <p>The web interface files could not be found.</p>
                <p>Please ensure the 'web' directory exists with app.html, styles.css, and app.js files.</p>
            </div>
        </body>
        </html>
        """
        self.setHtml(error_html)
        
    def closeEvent(self, event):
        """Handle application close event with IMMEDIATE shutdown"""
        import logging
        close_logger = logging.getLogger(__name__)
        close_logger.info("🚨 Window close event - executing IMMEDIATE shutdown...")
        
        try:
            # Stop UI monitoring first
            if self.bridge.ui_monitor:
                self.bridge.ui_monitor.stop_monitoring()
                close_logger.info("🔍 UI monitoring stopped")
            
            # 💾 SAVE UNUSED QUESTIONS TO CACHE FIRST - PRESERVE GPU WORK!
            close_logger.info("💾 Saving unused questions to cache before shutdown...")
            self.bridge._save_question_cache()
            
            # CRITICAL FIX: Use enhanced cleanup for all generation resources
            close_logger.info("🧹 Cleaning up generation resources...")
            self.bridge._cleanup_generation_resources()
                
            # Clear generation state
            if hasattr(self.bridge, 'generation_lock'):
                with self.bridge.generation_lock:
                    self.bridge.pending_generations = 0
                
            # Force cleanup connections
            if self.bridge.mcq_manager:
                try:
                    if hasattr(self.bridge.mcq_manager, 'lm_studio_generator') and self.bridge.mcq_manager.lm_studio_generator:
                        if hasattr(self.bridge.mcq_manager.lm_studio_generator, 'cleanup'):
                            self.bridge.mcq_manager.lm_studio_generator.cleanup()
                except:
                    pass  # Ignore cleanup errors
                    
        except Exception as e:
            close_logger.error(f"❌ Error during close event: {e}")
            
        # Accept the close event immediately - no delays
        event.accept()
        
        # Emergency exit if needed
        import os
        import threading
        
        def emergency_exit():
            import time
            import logging
            emergency_logger = logging.getLogger(__name__)
            import time
            time.sleep(1.0)  # Give 1 second max for normal exit (this runs in separate thread)
            emergency_logger.info("💥 EMERGENCY EXIT - process still running, forcing termination")
            os._exit(0)
            
        emergency_thread = threading.Thread(target=emergency_exit, daemon=True)
        emergency_thread.start()


# This module is not meant to be run directly as an entry point.
# Use main.py instead as the single unified entry point.