#!/usr/bin/env python3
"""
Test DeepSeek R1 thinking tokens and streaming
"""

import requests
import json
import time

def test_deepseek_thinking_stream():
    """Test if DeepSeek R1 actually streams thinking tokens"""
    
    prompt = """Generate a truly PhD-level question about quantum mechanics and atomic structure that would challenge a graduate student or postdoc. 

The question should involve:
- Advanced quantum mechanical concepts (not basic energy level calculations)
- Multi-step reasoning requiring deep understanding
- Complex interactions between quantum phenomena
- Research-level knowledge

Examples of PhD-level topics:
- Quantum field theory applications to atomic physics
- Many-body quantum systems
- Advanced spectroscopy and selection rules
- Quantum entanglement in atomic systems
- Relativistic quantum mechanics effects
- Advanced perturbation theory
- Quantum phase transitions in atomic systems

Generate a calculation-based question that requires graduate-level knowledge."""

    try:
        print("🧠 Testing DeepSeek R1 thinking tokens...")
        print("=" * 60)
        
        # Test with streaming to see thinking tokens
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "deepseek-r1:14b",  # Use the 14B model (32B might be too heavy)
                "prompt": prompt,
                "stream": True,  # Enable streaming to see thinking
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_predict": 2000
                }
            },
            stream=True,
            timeout=300
        )
        
        if response.status_code == 200:
            print("📡 Streaming DeepSeek R1 response...")
            print("🧠 Looking for thinking tokens...")
            print("-" * 60)
            
            full_response = ""
            thinking_detected = False
            
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        chunk = data.get('response', '')
                        
                        if chunk:
                            # Check for thinking tokens (usually marked with special tokens)
                            if '<think>' in chunk or '<thinking>' in chunk or '思考' in chunk:
                                thinking_detected = True
                                print(f"🧠 THINKING: {chunk}")
                            else:
                                print(f"💭 OUTPUT: {chunk}", end='', flush=True)
                            
                            full_response += chunk
                        
                        if data.get('done', False):
                            break
                            
                    except json.JSONDecodeError:
                        continue
            
            print("\n" + "=" * 60)
            print(f"✅ Full response received ({len(full_response)} chars)")
            print(f"🧠 Thinking tokens detected: {'✅ YES' if thinking_detected else '❌ NO'}")
            
            if not thinking_detected:
                print("⚠️ No thinking tokens detected - DeepSeek may not be in reasoning mode")
            
            print("\n📝 GENERATED QUESTION:")
            print("-" * 40)
            print(full_response)
            print("-" * 40)
            
            # Analyze if it's actually PhD-level
            phd_indicators = [
                'perturbation theory', 'many-body', 'field theory', 'relativistic',
                'entanglement', 'decoherence', 'quantum field', 'second quantization',
                'green\'s function', 'feynman diagram', 'renormalization', 'symmetry breaking',
                'phase transition', 'critical point', 'correlation function', 'scattering matrix'
            ]
            
            phd_found = [term for term in phd_indicators if term.lower() in full_response.lower()]
            
            print(f"\n🎓 PhD-LEVEL ANALYSIS:")
            print(f"   Advanced terms found: {phd_found}")
            print(f"   PhD-level: {'✅ YES' if len(phd_found) >= 2 else '❌ NO'}")
            
            if len(phd_found) >= 2:
                print("🎉 SUCCESS: This appears to be PhD-level!")
                return True
            else:
                print("❌ FAILED: Still undergraduate level")
                return False
            
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_multiple_deepseek_attempts():
    """Test multiple attempts with DeepSeek R1"""
    print("🔄 Testing multiple DeepSeek R1 attempts...")
    
    successes = 0
    attempts = 3
    
    for i in range(attempts):
        print(f"\n{'='*20} ATTEMPT {i+1}/{attempts} {'='*20}")
        if test_deepseek_thinking_stream():
            successes += 1
        time.sleep(2)  # Brief pause between attempts
    
    print(f"\n📊 FINAL RESULTS: {successes}/{attempts} PhD-level questions")
    return successes >= 1

if __name__ == "__main__":
    success = test_multiple_deepseek_attempts()
    
    if success:
        print("\n✅ DeepSeek R1 can generate PhD-level questions!")
    else:
        print("\n❌ DeepSeek R1 failed to generate PhD-level questions")
        print("💡 Need to improve the prompt or check model configuration")
