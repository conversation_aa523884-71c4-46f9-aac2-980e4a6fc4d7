#!/usr/bin/env python3
"""
Test DeepSeek integration directly to see what's happening
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

import logging
logging.basicConfig(level=logging.INFO, format='%(name)s - %(levelname)s - %(message)s')

def test_deepseek_integration():
    """Test DeepSeek integration directly"""
    
    print("🧠 TESTING DEEPSEEK INTEGRATION DIRECTLY")
    print("=" * 60)
    
    try:
        from knowledge_app.core.deepseek_integration import DeepSeekTwoModelPipeline
        
        # Initialize DeepSeek pipeline
        pipeline = DeepSeekTwoModelPipeline()
        
        print("✅ DeepSeek pipeline initialized")
        
        # Test expert question generation
        result = pipeline.generate_expert_question(
            topic="atoms",
            difficulty="expert",
            question_type="numerical",
            context="",
            progress_callback=None,
            generation_instructions="Generate a PhD-level question about quantum mechanics and atomic structure"
        )
        
        if result:
            print(f"✅ DeepSeek generated result: {type(result)}")
            
            # Check if it's a dictionary
            if isinstance(result, dict):
                question = result.get('question', '')
                options = result.get('options', [])
                explanation = result.get('explanation', '')
                
                print(f"📝 Question: {question}")
                print(f"🔢 Options: {options}")
                print(f"📚 Explanation: {explanation[:100]}...")
                
                # Check if it's PhD-level
                phd_terms = ['relativistic', 'dirac', 'perturbation', 'fine structure', 'hyperfine', 'quantum field', 'many-body']
                is_phd = any(term in question.lower() for term in phd_terms)
                print(f"🎓 PhD-level: {'YES' if is_phd else 'NO'}")
                
                if not is_phd:
                    print("❌ DeepSeek is generating basic questions, not PhD-level!")
                    print("🔍 This explains why the UI shows basic questions")
                else:
                    print("✅ DeepSeek IS generating PhD-level questions!")
                    print("🔍 The issue must be elsewhere in the pipeline")
            else:
                print(f"❌ DeepSeek returned unexpected format: {type(result)}")
                print(f"📄 Content: {result}")
        else:
            print("❌ DeepSeek returned no result")
            
    except Exception as e:
        print(f"❌ DeepSeek integration test failed: {e}")
        import traceback
        traceback.print_exc()

def test_deepseek_models():
    """Test if DeepSeek models are available"""
    
    print("\n🔍 TESTING DEEPSEEK MODEL AVAILABILITY")
    print("=" * 60)
    
    try:
        import requests
        
        # Check available models
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json()
            model_names = [model['name'] for model in models.get('models', [])]
            
            print(f"📋 Available models: {model_names}")
            
            # Check for DeepSeek models
            deepseek_models = [name for name in model_names if 'deepseek' in name.lower()]
            print(f"🧠 DeepSeek models: {deepseek_models}")
            
            if 'deepseek-r1:14b' in model_names:
                print("✅ deepseek-r1:14b is available")
            else:
                print("❌ deepseek-r1:14b is NOT available")
                print("🔍 This could be why PhD-level generation fails")
                
        else:
            print(f"❌ Failed to get models: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Model availability check failed: {e}")

if __name__ == "__main__":
    test_deepseek_models()
    test_deepseek_integration()
