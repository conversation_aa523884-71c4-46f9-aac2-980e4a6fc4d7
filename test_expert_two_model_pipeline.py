#!/usr/bin/env python3
"""
Expert Mode Two-Model Pipeline Test Suite
Tests the DeepSeek + Llama two-model pipeline specifically for expert difficulty
"""

import asyncio
import time
import random
from datetime import datetime

# PhD/Graduate-level expert topics with specific advanced concepts
PHD_LEVEL_TOPICS = [
    # Advanced Physics - Cutting-edge research topics
    "quantum field theory renormalization", "AdS/CFT correspondence", "topological quantum field theory",
    "non-Abelian gauge theories", "supersymmetric quantum mechanics", "quantum chromodynamics",
    "string theory compactification", "holographic principle", "quantum entanglement entropy",

    # Advanced Chemistry - Research-level concepts
    "asymmetric catalysis mechanisms", "organometallic reaction pathways", "supramolecular chemistry",
    "photochemical reaction dynamics", "computational quantum chemistry", "enzyme kinetics modeling",
    "molecular orbital theory applications", "transition metal catalysis", "chemical thermodynamics",

    # Advanced Mathematics - Graduate-level theory
    "Riemann hypothesis implications", "algebraic topology applications", "differential manifolds",
    "Galois theory extensions", "measure theory integration", "functional analysis operators",
    "homological algebra", "category theory functors", "algebraic geometry schemes",

    # Advanced Medicine - Clinical research level
    "pharmacokinetic modeling", "molecular pathogenesis mechanisms", "immunotherapy resistance",
    "epigenetic regulation", "protein folding diseases", "neurotransmitter signaling pathways",
    "cancer metabolism reprogramming", "stem cell differentiation", "gene therapy vectors",

    # Advanced Computer Science - Research frontiers
    "quantum algorithm complexity", "distributed consensus protocols", "cryptographic zero-knowledge proofs",
    "machine learning theoretical foundations", "computational complexity hierarchies", "formal verification methods",
    "neural network optimization theory", "blockchain consensus mechanisms", "compiler optimization techniques",

    # Advanced Philosophy - Graduate seminar level
    "modal logic semantics", "philosophy of quantum mechanics", "consciousness and qualia",
    "moral realism vs anti-realism", "epistemic justification theories", "metaphysical naturalism",
    "phenomenological reduction", "analytic philosophy of language", "philosophy of mathematics foundations"
]

async def test_expert_mode_generation(topic, test_num, total_tests):
    """Test expert mode generation with two-model pipeline"""
    print(f"\n🧠 Expert Test {test_num:2d}/{total_tests}: {topic}")
    
    try:
        # Import the unified inference manager
        from src.knowledge_app.core.unified_inference_manager import generate_mcq_unified
        
        start_time = time.time()
        
        # Generate with EXPERT difficulty - this should trigger DeepSeek two-model pipeline
        print(f"      🧠 Triggering DeepSeek two-model pipeline for expert difficulty...")

        result = generate_mcq_unified(
            topic=topic,
            difficulty="expert",  # CRITICAL: This triggers DeepSeek + Llama pipeline
            question_type="mixed",
            timeout=180.0  # Longer timeout for two-model pipeline
        )
        
        duration = time.time() - start_time
        
        if not result:
            print(f"   ❌ FAIL - No result generated")
            return False, f"No result after {duration:.1f}s"
        
        # Validate expert-level result
        question = result.get('question', '')
        options = result.get('options', [])
        correct = result.get('correct_answer', '') or result.get('correct', '')
        
        # PhD-level content validation
        explanation = result.get('explanation', '')

        # Advanced validation criteria for PhD-level content
        checks = {
            'has_question': bool(question.strip()),
            'question_length': len(question) >= 150,  # PhD questions should be very detailed
            'has_question_mark': question.endswith('?'),
            'has_4_options': len(options) == 4,
            'has_correct': correct in ['A', 'B', 'C', 'D'],
            'has_explanation': bool(explanation.strip()),
            'substantial_options': all(len(str(opt).strip()) >= 20 for opt in options),  # More detailed options
            'phd_complexity_keywords': any(word in question.lower() for word in [
                'theoretical', 'mechanism', 'framework', 'phenomenon', 'hypothesis',
                'methodology', 'paradigm', 'empirical', 'quantitative', 'qualitative',
                'systematic', 'comprehensive', 'interdisciplinary', 'multifaceted'
            ]),
            'technical_terminology': any(term in (question + ' '.join(options)).lower() for term in [
                'coefficient', 'parameter', 'variable', 'function', 'equation', 'model',
                'theory', 'principle', 'law', 'theorem', 'hypothesis', 'correlation',
                'regression', 'analysis', 'synthesis', 'optimization', 'algorithm'
            ]),
            'research_level_language': any(phrase in question.lower() for phrase in [
                'research indicates', 'studies show', 'empirical evidence', 'theoretical framework',
                'experimental design', 'statistical significance', 'peer-reviewed', 'meta-analysis',
                'systematic review', 'longitudinal study', 'cross-sectional', 'randomized controlled'
            ]),
            'advanced_concepts': len([word for word in question.lower().split() if len(word) > 8]) >= 3,  # Complex vocabulary
            'numerical_precision': any(char.isdigit() for char in question) or any('.' in str(opt) for opt in options),  # Specific values
            'explanation_depth': len(explanation) >= 100 if explanation else False  # Detailed explanations
        }
        
        passed_checks = sum(checks.values())
        total_checks = len(checks)

        # PhD-level should pass at least 80% of advanced checks
        success = passed_checks >= (total_checks * 0.80)

        print(f"   📊 ANALYSIS: {passed_checks}/{total_checks} PhD-level criteria met ({duration:.1f}s)")

        # Always display the actual content for manual PhD-level review
        print(f"   📝 QUESTION ({len(question)} chars):")
        print(f"      {question}")
        print(f"   📋 OPTIONS:")
        for i, opt in enumerate(options):
            marker = "✓" if chr(65 + i) == correct else " "
            print(f"      {chr(65 + i)}) {marker} {opt}")

        if explanation:
            print(f"   💡 EXPLANATION ({len(explanation)} chars):")
            print(f"      {explanation[:200]}{'...' if len(explanation) > 200 else ''}")

        # Detailed analysis
        if success:
            print(f"   ✅ PASS - PhD-level quality achieved")
        else:
            print(f"   ❌ FAIL - Not meeting PhD-level standards")
            failed = [k for k, v in checks.items() if not v]
            print(f"      ❌ Missing: {', '.join(failed[:3])}{'...' if len(failed) > 3 else ''}")

        # Content quality assessment
        complexity_score = sum([
            checks['phd_complexity_keywords'],
            checks['technical_terminology'],
            checks['research_level_language'],
            checks['advanced_concepts'],
            checks['numerical_precision']
        ])

        print(f"   🎓 PhD Complexity Score: {complexity_score}/5")

        return success, f"{passed_checks}/{total_checks} checks, complexity {complexity_score}/5, {duration:.1f}s"
        
    except Exception as e:
        print(f"   ❌ ERROR - {e}")
        return False, f"Exception: {e}"

async def run_expert_pipeline_test_batch(batch_size=20):
    """Run a batch of expert mode tests"""
    print("🧠 EXPERT MODE TWO-MODEL PIPELINE TEST SUITE")
    print("=" * 70)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialize system
    print("\n🔧 Initializing system for expert mode...")
    try:
        from src.knowledge_app.core.unified_inference_manager import initialize_unified_inference
        
        success = initialize_unified_inference({
            'timeout': 120.0,
            'mode': 'auto',
            'prefer_local': True
        })
        
        if not success:
            print("❌ System initialization failed")
            return False
        
        print("✅ System initialized for expert mode")
        
        # Check if DeepSeek pipeline is available
        try:
            from src.knowledge_app.core.deepseek_integration import DeepSeekTwoModelPipeline
            pipeline = DeepSeekTwoModelPipeline()
            print("✅ DeepSeek two-model pipeline available")
        except Exception as e:
            print(f"⚠️ DeepSeek pipeline issue: {e}")
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return False
    
    # Select diverse PhD-level topics
    selected_topics = random.sample(PHD_LEVEL_TOPICS, min(batch_size, len(PHD_LEVEL_TOPICS)))
    
    print(f"\n🎯 Testing {len(selected_topics)} expert topics with two-model pipeline...")
    print("📋 Topics selected:")
    for i, topic in enumerate(selected_topics, 1):
        print(f"   {i:2d}. {topic}")
    
    # Run tests
    start_time = time.time()
    passed = 0
    results = []
    
    for i, topic in enumerate(selected_topics, 1):
        success, message = await test_expert_mode_generation(topic, i, len(selected_topics))
        results.append((topic, success, message))
        if success:
            passed += 1
    
    duration = time.time() - start_time
    pass_rate = (passed / len(selected_topics)) * 100
    
    # Results summary
    print("\n" + "=" * 70)
    print("🏆 EXPERT MODE TWO-MODEL PIPELINE RESULTS")
    print("=" * 70)
    
    print(f"📊 Performance:")
    print(f"   Expert Tests: {passed}/{len(selected_topics)} passed ({pass_rate:.1f}%)")
    print(f"   Duration: {duration:.1f} seconds")
    print(f"   Average per test: {duration/len(selected_topics):.1f} seconds")
    print(f"   Speed: {len(selected_topics)/duration:.2f} tests/second")
    
    print(f"\n📈 Detailed Results:")
    for topic, success, message in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} - {topic:<30} ({message})")
    
    # Expert mode success criteria
    if pass_rate >= 85:
        print(f"\n🎉 EXCELLENT: {pass_rate:.1f}% expert mode success!")
        print("🧠 Two-model pipeline performing exceptionally well!")
        return True
    elif pass_rate >= 75:
        print(f"\n👏 GOOD: {pass_rate:.1f}% expert mode success!")
        print("🧠 Two-model pipeline performing well!")
        return True
    elif pass_rate >= 65:
        print(f"\n📊 ACCEPTABLE: {pass_rate:.1f}% expert mode success!")
        print("🧠 Two-model pipeline needs some improvement")
        return True
    else:
        print(f"\n⚠️ NEEDS WORK: {pass_rate:.1f}% expert mode success")
        print("🧠 Two-model pipeline requires optimization")
        return False

async def main():
    """Main test runner"""
    print("🚀 EXPERT MODE TWO-MODEL PIPELINE TESTER")
    
    try:
        success = await run_expert_pipeline_test_batch(20)
        return 0 if success else 1
    except Exception as e:
        print(f"❌ Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
