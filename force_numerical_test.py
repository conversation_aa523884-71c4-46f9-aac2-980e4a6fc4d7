#!/usr/bin/env python3
"""
Force numerical question generation by directly modifying the prompt
"""

import sys
import os
import time
import json
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_direct_ollama_numerical():
    """Test direct Ollama generation with forced numerical prompt"""
    try:
        print("🔢 TESTING DIRECT OLLAMA WITH FORCED NUMERICAL PROMPT")
        print("=" * 60)
        
        import requests
        
        # Create a VERY explicit numerical prompt
        prompt = """You are an expert physics teacher. Generate EXACTLY ONE multiple choice question about atoms that MUST include numbers, calculations, or quantitative analysis.

CRITICAL REQUIREMENTS:
- The question MUST involve calculations, numbers, formulas, or quantitative analysis
- Include specific numerical values (masses, energies, wavelengths, etc.)
- Focus on atomic calculations like energy levels, electron transitions, atomic masses, etc.
- The question should require mathematical problem-solving
- All answer options must be numerical values with units

FORBIDDEN:
- Conceptual questions about theories or principles
- Questions asking "what is" or "which theory"
- Qualitative comparisons
- Descriptive explanations

EXAMPLE NUMERICAL TOPICS:
- Calculate the energy of an electron in the n=3 shell
- Determine the wavelength of light emitted during electron transition
- Find the number of neutrons in an isotope
- Calculate binding energy or ionization energy

Generate the question in this EXACT JSON format:
{
  "question": "Calculate the [specific numerical problem about atoms]?",
  "options": {
    "A": "[numerical answer with units]",
    "B": "[numerical answer with units]", 
    "C": "[numerical answer with units]",
    "D": "[numerical answer with units]"
  },
  "correct": "A",
  "explanation": "Step-by-step calculation showing the work"
}

Generate the question now:"""

        # Send to Ollama
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "llama3.1:8b",  # Use a common model
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,  # Lower temperature for more focused output
                    "top_p": 0.9
                }
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            generated_text = result.get('response', '')
            
            print("✅ Ollama response received!")
            print(f"📝 Generated text: {generated_text[:500]}...")
            
            # Try to extract JSON
            import re
            json_match = re.search(r'\{.*\}', generated_text, re.DOTALL)
            if json_match:
                try:
                    question_data = json.loads(json_match.group())
                    
                    question = question_data.get('question', '')
                    options = question_data.get('options', {})
                    
                    print(f"\n🔢 EXTRACTED QUESTION:")
                    print(f"   Question: {question}")
                    print(f"   Options: {options}")
                    
                    # Check if it's numerical
                    import re
                    has_numbers = bool(re.search(r'\d+', question)) or any(
                        re.search(r'\d+', str(opt)) for opt in options.values()
                    )
                    
                    numerical_keywords = ['calculate', 'compute', 'mass', 'energy', 'electrons', 'protons', 'neutrons', 'atomic number', 'wavelength', 'frequency', 'joules', 'eV', 'nm']
                    has_numerical_keywords = any(keyword in question.lower() for keyword in numerical_keywords)
                    
                    if has_numbers or has_numerical_keywords:
                        print("✅ SUCCESS: This is a NUMERICAL question!")
                        return True
                    else:
                        print("❌ FAILED: Still conceptual despite explicit prompt")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON parsing failed: {e}")
                    return False
            else:
                print("❌ No JSON found in response")
                return False
        else:
            print(f"❌ Ollama request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Direct Ollama test failed: {e}")
        return False

def main():
    print("🧪 FORCE NUMERICAL QUESTION TEST")
    print("=" * 50)
    
    success = test_direct_ollama_numerical()
    
    if success:
        print("\n🎉 SUCCESS: Forced numerical generation works!")
        print("💡 The issue is in the UnifiedInferenceManager prompt generation")
    else:
        print("\n💥 FAILED: Even direct prompting doesn't work")
        print("💡 The issue might be with the Ollama model itself")

if __name__ == "__main__":
    main()
