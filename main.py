#!/usr/bin/env python3
"""
Knowledge App - Pure QtWebEngine Main Entry Point
Clean, modern web-based UI with zero QtWidgets bloatware
"""

import sys
import os
import logging
import warnings
from pathlib import Path

# COMPLETELY SUPPRESS ALL CONSOLE OUTPUT FROM THE VERY START
# This prevents empty lines and console spam from Qt and other libraries

# Smart output filter to prevent empty lines but allow critical errors
class SmartOutputFilter:
    def __init__(self, original_stream):
        self.original = original_stream
        self.buffer = ""
        
    def write(self, data):
        # Only write non-empty lines and critical errors
        if data and data.strip():
            # Filter out sandboxing message
            if "Sandboxing disabled" in data:
                return
            # Only allow critical errors through
            if "CRITICAL ERROR:" in data or "IMPORT ERROR:" in data:
                self.original.write(data)
                
    def flush(self):
        if hasattr(self.original, 'flush'):
            self.original.flush()

# Store original streams
_original_stdout = sys.stdout
_original_stderr = sys.stderr

# Replace with smart filters to eliminate empty lines but keep critical errors
sys.stdout = SmartOutputFilter(_original_stdout)
sys.stderr = SmartOutputFilter(_original_stderr)

# Suppress all Qt debug messages by setting Qt logging rules
os.environ['QT_LOGGING_RULES'] = '*.debug=false;*.info=false;*.warning=false;qt.*=false'

# Suppress Qt WebEngine messages
os.environ['QTWEBENGINE_CHROMIUM_FLAGS'] = '--disable-logging --disable-gpu-sandbox --no-sandbox --disable-dev-shm-usage --log-level=3'
os.environ['QTWEBENGINE_DISABLE_SANDBOX'] = '1'

# SUPPRESS FAISS GPU WARNINGS IMMEDIATELY AT STARTUP
warnings.filterwarnings("ignore", message=r".*Failed to load GPU Faiss.*", category=UserWarning)
warnings.filterwarnings("ignore", message=r".*GpuIndexIVFFlat.*not defined.*", category=UserWarning)
warnings.filterwarnings("ignore", message=r".*FAISS.*", category=UserWarning)

# Add src directory to path for imports
src_dir = Path(__file__).parent / "src"
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

# CONFIGURE SILENT LOGGING - NO CONSOLE OUTPUT TO PREVENT SPACING ISSUES
def setup_clean_logging():
    """Setup completely silent logging to prevent ALL spacing issues"""
    
    # Clear any existing handlers first
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
        handler.close()
    
    # Create a null handler to suppress ALL console output
    null_handler = logging.NullHandler()
    
    # TEMPORARILY ENABLE INFO LOGGING FOR EXPERT MODE DEBUGGING
    root_logger.setLevel(logging.INFO)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    
    # Completely silence ALL modules
    modules_to_silence = [
        'faiss', 'faiss.loader', 'torch', 'transformers', 'datasets', 'PIL', 
        'matplotlib', 'bitsandbytes', 'knowledge_app', 'PyQt5', 'asyncio',
        'urllib3', 'requests', 'httpx', 'openai', 'anthropic', 'google',
        'groq', 'openrouter', '__main__', 'root'
    ]
    for module_name in modules_to_silence:
        module_logger = logging.getLogger(module_name)
        module_logger.setLevel(logging.CRITICAL)
        module_logger.addHandler(null_handler)
        module_logger.propagate = False

# Setup clean logging immediately
setup_clean_logging()

def check_dependencies():
    """Check if required dependencies are available"""
    try:
        from PyQt5.QtCore import QUrl, QCoreApplication, Qt
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtWebEngineWidgets import QWebEngineView
        from PyQt5.QtWebChannel import QWebChannel
        return True
    except ImportError as e:
        _original_stderr.write(f"IMPORT ERROR: {e}\n")
        return False


def main():
    """Main application entry point - Pure QtWebEngine - SILENT"""
    try:
        
        # Check dependencies
        if not check_dependencies():
            return 1
        
        # CRITICAL: Set Qt attributes BEFORE creating QApplication for WebEngine
        from PyQt5.QtCore import QCoreApplication, Qt, QDir, QStandardPaths
        from PyQt5.QtWebEngineWidgets import QWebEngineProfile
        
        # Fix QtWebEngine cache issues and startup problems
        QCoreApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
        QCoreApplication.setAttribute(Qt.AA_DisableWindowContextHelpButton)
        QCoreApplication.setAttribute(Qt.AA_SynthesizeTouchForUnhandledMouseEvents, False)
        QCoreApplication.setAttribute(Qt.AA_SynthesizeMouseForUnhandledTouchEvents, False)
        
        # Import and run the QtWebEngine app 
        from PyQt5.QtWidgets import QApplication
        from knowledge_app.webengine_app import KnowledgeAppWebEngine
        
        # Create QApplication (required for QWebEngineView widgets)
        app = QApplication(sys.argv)
        app.setApplicationName("Knowledge App")
        app.setApplicationVersion("1.0.0")
        
        # CRITICAL FIX: Configure QtWebEngine to reduce cache errors
        try:
            
            # Create a writable cache directory in the app folder
            cache_dir = Path(__file__).parent / "cache" / "webengine"
            cache_dir.mkdir(parents=True, exist_ok=True)
            
            # Set the cache directory for the default profile
            profile = QWebEngineProfile.defaultProfile()
            profile.setCachePath(str(cache_dir))
            profile.setPersistentStoragePath(str(cache_dir / "storage"))
            
            # Disable ALL caching to prevent permission issues
            profile.setHttpCacheType(QWebEngineProfile.NoCache)
            profile.setHttpCacheMaximumSize(0)
            
            # Disable other problematic features
            from PyQt5.QtWebEngineWidgets import QWebEngineSettings
            settings = profile.settings()
            settings.setAttribute(QWebEngineSettings.PluginsEnabled, False)
            settings.setAttribute(QWebEngineSettings.JavascriptCanOpenWindows, False)
            settings.setAttribute(QWebEngineSettings.LocalStorageEnabled, True)
            
        except Exception:
            # Silent fallback - continue without cache fixes
            pass
        
        # Create and show main window (pure QtWebEngine)
        window = KnowledgeAppWebEngine()
        window.show()
        
        # Run the application
        return app.exec_()
        
    except KeyboardInterrupt:
        return 0
    except Exception as e:
        # Show critical startup errors only
        _original_stderr.write(f"CRITICAL ERROR: {e}\n")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)


