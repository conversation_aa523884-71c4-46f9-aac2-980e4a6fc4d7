"""
🌊 Streaming Token Inference System
Real-time token streaming for quiz generation with beautiful visualizations
"""

import asyncio
import json
import time
import uuid
from typing import Dict, Any, Optional, Callable, AsyncGenerator
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class TokenStreamSession:
    """Manages a single token streaming session"""
    
    def __init__(self, session_id: str, topic: str, difficulty: str, question_type: str):
        self.session_id = session_id
        self.topic = topic
        self.difficulty = difficulty
        self.question_type = question_type
        self.start_time = time.time()
        self.tokens_streamed = 0
        self.is_active = True
        self.final_question = None
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "session_id": self.session_id,
            "topic": self.topic,
            "difficulty": self.difficulty,
            "question_type": self.question_type,
            "start_time": self.start_time,
            "tokens_streamed": self.tokens_streamed,
            "is_active": self.is_active,
            "duration": time.time() - self.start_time if not self.is_active else None
        }

class StreamingInferenceEngine:
    """
    🌊 Real-time token streaming engine for quiz generation
    
    Provides beautiful token-by-token visualization of AI thinking process
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.active_sessions: Dict[str, TokenStreamSession] = {}
        self.token_callback: Optional[Callable] = None
        
        # Streaming configuration
        self.stream_delay = self.config.get('stream_delay', 0.05)  # 50ms between tokens
        self.chunk_size = self.config.get('chunk_size', 3)  # Tokens per chunk
        self.enable_thinking_simulation = self.config.get('enable_thinking_simulation', True)
        
        logger.info("🌊 StreamingInferenceEngine initialized")
    
    def set_token_callback(self, callback: Callable[[str, str], None]):
        """Set callback function for token streaming"""
        self.token_callback = callback
        logger.info("✅ Token callback registered")
    
    async def stream_question_generation(
        self, 
        topic: str, 
        difficulty: str = "medium", 
        question_type: str = "mixed"
    ) -> str:
        """
        🌊 Stream question generation with real-time token visualization
        
        Returns:
            session_id: Unique identifier for this streaming session
        """
        session_id = str(uuid.uuid4())
        session = TokenStreamSession(session_id, topic, difficulty, question_type)
        self.active_sessions[session_id] = session
        
        logger.info(f"🌊 Starting token stream for {topic} ({difficulty}) - Session: {session_id}")
        
        try:
            # Start streaming in background
            asyncio.create_task(self._stream_tokens_async(session))
            return session_id
            
        except Exception as e:
            logger.error(f"❌ Failed to start token stream: {e}")
            session.is_active = False
            if self.token_callback:
                self.token_callback(session_id, f"ERROR: {str(e)}")
            raise
    
    async def _stream_tokens_async(self, session: TokenStreamSession):
        """Internal method to handle token streaming"""
        try:
            # Phase 1: Thinking simulation
            if self.enable_thinking_simulation:
                await self._stream_thinking_phase(session)
            
            # Phase 2: Question generation
            await self._stream_question_generation_phase(session)
            
            # Phase 3: Completion
            await self._complete_streaming_session(session)
            
        except Exception as e:
            logger.error(f"❌ Token streaming error for session {session.session_id}: {e}")
            session.is_active = False
            if self.token_callback:
                self.token_callback(session.session_id, f"ERROR: {str(e)}")
    
    async def _stream_thinking_phase(self, session: TokenStreamSession):
        """Stream the AI thinking process"""
        thinking_tokens = [
            "🤔", "Analyzing", "topic:", f'"{session.topic}"...',
            "🧠", "Considering", "difficulty:", f'"{session.difficulty}"',
            "📝", "Planning", "question", "structure...",
            "🎯", "Focusing", "on", f'"{session.question_type}"', "format",
            "⚡", "Generating", "content..."
        ]
        
        for token in thinking_tokens:
            if not session.is_active:
                break
                
            if self.token_callback:
                self.token_callback(session.session_id, token)
            
            session.tokens_streamed += 1
            await asyncio.sleep(self.stream_delay)
    
    async def _stream_question_generation_phase(self, session: TokenStreamSession):
        """Stream the actual question generation"""
        # Simulate realistic question generation
        question_parts = await self._generate_question_parts(session)
        
        for part in question_parts:
            if not session.is_active:
                break
            
            # Stream tokens in chunks for smooth visualization
            tokens = part.split()
            for i in range(0, len(tokens), self.chunk_size):
                chunk = " ".join(tokens[i:i + self.chunk_size])
                
                if self.token_callback:
                    self.token_callback(session.session_id, chunk)
                
                session.tokens_streamed += len(tokens[i:i + self.chunk_size])
                await asyncio.sleep(self.stream_delay)
    
    async def _generate_question_parts(self, session: TokenStreamSession) -> list:
        """Generate realistic question parts for streaming"""
        # This would normally call the actual AI model
        # For now, simulate with topic-specific content
        
        if "physics" in session.topic.lower():
            return [
                "What is the relationship between",
                "force and acceleration according to",
                "Newton's second law of motion?",
                "A) F = ma",
                "B) F = mv",
                "C) F = m/a", 
                "D) F = a/m",
                "The correct answer is A) F = ma.",
                "This fundamental equation shows that force",
                "equals mass times acceleration."
            ]
        elif "chemistry" in session.topic.lower() or "atoms" in session.topic.lower():
            return [
                "In molecular orbital theory,",
                "how do electrons behave differently",
                "compared to VSEPR theory?",
                "A) Electrons are localized to specific atoms",
                "B) Electrons are delocalized across the molecule", 
                "C) Electrons follow classical physics",
                "D) Electrons have no wave properties",
                "The correct answer is B.",
                "MO theory treats electrons as delocalized",
                "across the entire molecular framework."
            ]
        else:
            return [
                f"What is a key concept in {session.topic}",
                "that demonstrates advanced understanding?",
                "A) Basic definition",
                "B) Complex interaction",
                "C) Advanced application",
                "D) Theoretical framework",
                "The correct answer depends on context.",
                "Advanced topics require deep analysis",
                "of underlying principles."
            ]
    
    async def _complete_streaming_session(self, session: TokenStreamSession):
        """Complete the streaming session"""
        session.is_active = False
        
        # Create final question object
        final_question = {
            "question": f"Generated question about {session.topic}",
            "options": ["Option A", "Option B", "Option C", "Option D"],
            "correct_answer": "Option A",
            "explanation": f"This {session.difficulty} question tests understanding of {session.topic}",
            "metadata": {
                "session_id": session.session_id,
                "tokens_streamed": session.tokens_streamed,
                "generation_time": time.time() - session.start_time,
                "difficulty": session.difficulty,
                "question_type": session.question_type
            }
        }
        
        session.final_question = final_question
        
        # Notify completion
        if self.token_callback:
            self.token_callback(session.session_id, "STREAM_COMPLETE")
        
        logger.info(f"✅ Token stream completed - Session: {session.session_id}, Tokens: {session.tokens_streamed}")
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a streaming session"""
        session = self.active_sessions.get(session_id)
        return session.to_dict() if session else None
    
    def stop_session(self, session_id: str) -> bool:
        """Stop an active streaming session"""
        session = self.active_sessions.get(session_id)
        if session:
            session.is_active = False
            logger.info(f"🛑 Stopped token stream - Session: {session_id}")
            return True
        return False
    
    def cleanup_completed_sessions(self):
        """Clean up completed streaming sessions"""
        completed = [sid for sid, session in self.active_sessions.items() if not session.is_active]
        for session_id in completed:
            del self.active_sessions[session_id]
        
        if completed:
            logger.info(f"🧹 Cleaned up {len(completed)} completed streaming sessions")

# Global streaming engine instance
_streaming_engine: Optional[StreamingInferenceEngine] = None

def get_streaming_engine(config: Optional[Dict] = None) -> StreamingInferenceEngine:
    """Get or create the global streaming inference engine"""
    global _streaming_engine
    if _streaming_engine is None:
        _streaming_engine = StreamingInferenceEngine(config)
    return _streaming_engine
