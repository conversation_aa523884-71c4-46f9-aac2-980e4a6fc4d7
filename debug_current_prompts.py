#!/usr/bin/env python3
"""
Debug what prompts are actually being used right now
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_current_prompt_for_atoms():
    """Test what prompt is actually being generated for 'atoms'"""
    print("🔍 Testing current prompt generation for 'atoms'...")
    
    try:
        from knowledge_app.core.intelligent_prompt_generator import get_intelligent_prompt_generator
        
        # Get the generator
        prompt_generator = get_intelligent_prompt_generator()
        
        # Test with atoms
        print("🧪 Testing with 'atoms'...")
        result = prompt_generator.generate_intelligent_prompt(
            raw_input="atoms",
            difficulty="medium",
            question_type="mixed"
        )
        
        print(f"📝 Intelligent prompt result:")
        print(f"   Confidence: {result.get('confidence', 'N/A')}")
        print(f"   Resolved topic: {result.get('metadata', {}).get('resolved_topic', 'N/A')}")
        print(f"   Subject area: {result.get('metadata', {}).get('subject_area', 'N/A')}")
        print(f"   Resolution method: {result.get('metadata', {}).get('resolution_method', 'N/A')}")
        
        # Show the ACTUAL prompt being sent
        print("\n📄 ACTUAL PROMPT BEING SENT TO AI:")
        print("=" * 80)
        print(result.get('prompt', ''))
        print("=" * 80)
        
        return result
        
    except Exception as e:
        print(f"❌ Prompt test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_inquisitor_prompt():
    """Test the Inquisitor's Mandate prompt directly"""
    print("\n🎯 Testing Inquisitor's Mandate prompt...")
    
    try:
        from knowledge_app.core.inquisitor_prompt import _create_inquisitor_prompt
        
        # Test with atoms context
        context_text = "Atoms are the basic units of matter. They consist of protons, neutrons, and electrons."
        topic = "atoms"
        difficulty = "medium"
        
        inquisitor_prompt = _create_inquisitor_prompt(context_text, topic, difficulty)
        
        print("📄 INQUISITOR'S MANDATE PROMPT:")
        print("=" * 80)
        print(inquisitor_prompt)
        print("=" * 80)
        
        return inquisitor_prompt
        
    except Exception as e:
        print(f"❌ Inquisitor test failed: {e}")
        return None

def test_what_generator_is_used():
    """Test which generator is actually being used"""
    print("\n🔍 Testing which generator is actually being used...")
    
    try:
        from knowledge_app.core.unified_inference_manager import get_unified_inference_manager
        
        manager = get_unified_inference_manager()
        
        print(f"📊 Manager state: {manager._state}")
        print(f"🌐 Cloud available: {manager._cloud_available}")
        print(f"🎮 Local available: {manager._local_available}")
        print(f"🔄 Mode: {manager._mode}")
        
        # Check what generators are available
        print(f"🔍 Available generators:")
        for attr in dir(manager):
            if 'generator' in attr.lower() and not attr.startswith('_'):
                value = getattr(manager, attr, None)
                print(f"   {attr}: {value is not None}")
        
        # Check internal generators
        if hasattr(manager, '_ollama_generator'):
            print(f"   _ollama_generator: {manager._ollama_generator is not None}")
        if hasattr(manager, '_intelligent_generator'):
            print(f"   _intelligent_generator: {manager._intelligent_generator is not None}")
        if hasattr(manager, '_advanced_rag_generator'):
            print(f"   _advanced_rag_generator: {manager._advanced_rag_generator is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Generator check failed: {e}")
        return False

def test_ollama_generator_prompts():
    """Test what prompts the Ollama generator is actually using"""
    print("\n🔥 Testing Ollama generator prompts...")
    
    try:
        from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator
        
        generator = OllamaJSONGenerator()
        
        # Check if it has intelligent prompt generation
        if hasattr(generator, '_generate_intelligent_prompt'):
            print("✅ Has intelligent prompt generation")
        else:
            print("❌ No intelligent prompt generation")
        
        # Check what methods it has for generation
        generation_methods = [method for method in dir(generator) if 'generate' in method.lower() and not method.startswith('_')]
        print(f"🔍 Generation methods: {generation_methods}")
        
        # Try to see what prompt it would generate for atoms
        if hasattr(generator, 'intelligent_prompt_generator'):
            print("✅ Has intelligent_prompt_generator attribute")
            prompt_gen = generator.intelligent_prompt_generator
            if prompt_gen:
                result = prompt_gen.generate_intelligent_prompt("atoms", "medium", "mixed")
                print(f"📝 Ollama generator would use this prompt:")
                print("-" * 50)
                print(result.get('prompt', 'No prompt found'))
                print("-" * 50)
        else:
            print("❌ No intelligent_prompt_generator attribute")
        
        return True
        
    except Exception as e:
        print(f"❌ Ollama generator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔍 Current Prompt Debug Tool")
    print("=" * 60)
    
    # Test 1: What prompt is being generated?
    current_prompt = test_current_prompt_for_atoms()
    
    # Test 2: What would Inquisitor's Mandate generate?
    inquisitor_prompt = test_inquisitor_prompt()
    
    # Test 3: Which generator is being used?
    generator_check = test_what_generator_is_used()
    
    # Test 4: What prompts does Ollama generator use?
    ollama_check = test_ollama_generator_prompts()
    
    print(f"\n📊 SUMMARY:")
    print(f"   Current prompt generated: {'✅' if current_prompt else '❌'}")
    print(f"   Inquisitor prompt works: {'✅' if inquisitor_prompt else '❌'}")
    print(f"   Generator check: {'✅' if generator_check else '❌'}")
    print(f"   Ollama generator check: {'✅' if ollama_check else '❌'}")
    
    if current_prompt and inquisitor_prompt:
        print(f"\n💡 COMPARISON:")
        current_len = len(current_prompt.get('prompt', ''))
        inquisitor_len = len(inquisitor_prompt)
        print(f"   Current prompt length: {current_len}")
        print(f"   Inquisitor prompt length: {inquisitor_len}")
        
        if current_len < inquisitor_len:
            print(f"⚠️ WARNING: Current prompt is much shorter than Inquisitor!")
            print(f"💡 This suggests the advanced prompting is NOT being used!")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
