#!/usr/bin/env python3
"""
Debug connection issues between Knowledge App and Ollama
"""

import requests
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_different_connections():
    """Test different ways to connect to Ollama"""
    print("🔍 TESTING DIFFERENT OLLAMA CONNECTIONS")
    print("=" * 60)
    
    # Test different URLs
    urls_to_test = [
        "http://localhost:11434/api/tags",
        "http://127.0.0.1:11434/api/tags",
        "http://0.0.0.0:11434/api/tags"
    ]
    
    for url in urls_to_test:
        print(f"\n🔄 Testing: {url}")
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                print(f"   ✅ SUCCESS! Found {len(models)} models")
                # Show some model names
                for model in models[:3]:
                    print(f"      - {model.get('name', 'Unknown')}")
            else:
                print(f"   ❌ HTTP {response.status_code}")
        except requests.exceptions.ConnectionError as e:
            print(f"   ❌ Connection Error: {e}")
        except requests.exceptions.Timeout:
            print(f"   ❌ Timeout")
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_knowledge_app_connection():
    """Test how the Knowledge App connects to Ollama"""
    print("\n🧪 TESTING KNOWLEDGE APP CONNECTION")
    print("=" * 60)
    
    try:
        # Test the same way the Knowledge App does
        from knowledge_app.core.deepseek_integration import DeepSeekPipeline
        
        print("\n1️⃣ Testing DeepSeek Pipeline initialization...")
        pipeline = DeepSeekPipeline()
        print(f"   📊 Available models: {len(pipeline.available_models)}")
        print(f"   🧠 Thinking model: {pipeline.thinking_model}")
        print(f"   📝 JSON model: {pipeline.json_model}")
        print(f"   🚀 Ready: {pipeline.is_ready()}")
        
        if pipeline.available_models:
            print("   ✅ DeepSeek can see Ollama models!")
            for model in pipeline.available_models[:5]:
                print(f"      - {model}")
        else:
            print("   ❌ DeepSeek cannot see any Ollama models")
            
    except Exception as e:
        print(f"   ❌ DeepSeek Pipeline error: {e}")
        import traceback
        traceback.print_exc()
    
    try:
        print("\n2️⃣ Testing Unified Inference Manager...")
        from knowledge_app.core.unified_inference_manager import get_unified_inference_manager
        
        manager = get_unified_inference_manager()
        status = manager.get_status()
        print(f"   📊 Status: {status}")
        
        # Check local availability
        print(f"   🏠 Local available: {status.get('local_available', False)}")
        print(f"   ☁️ Cloud available: {status.get('cloud_available', False)}")
        
    except Exception as e:
        print(f"   ❌ Unified Inference Manager error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_different_connections()
    test_knowledge_app_connection()
