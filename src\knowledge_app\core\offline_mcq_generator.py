# src/knowledge_app/core/offline_mcq_generator.py

from .async_converter import async_requests_post, async_requests_get
from .async_converter import async_time_sleep


import logging
import asyncio
import traceback
import time
import sys
from typing import Dict, List, Any, Optional
import json
import re

from .mcq_generator import MCQGenerator
from .ollama_model_inference import OllamaModelInference

# Create specialized loggers for offline MCQ generation
logger = logging.getLogger(__name__)
offline_logger = logging.getLogger("offline_mcq")
performance_logger = logging.getLogger("performance_offline_mcq")


class OfflineMCQGenerator(MCQGenerator):
    """
    High-performance offline MCQ generator using local models with GPU acceleration
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Offline MCQ Generator with ultra-comprehensive logging"""
        offline_logger.info("🚀 INITIALIZING OfflineMCQGenerator")
        offline_logger.info(f"🔍 CONFIG RECEIVED: {config}")
        offline_logger.info(f"🔍 PYTHON VERSION: {sys.version}")
        
        super().__init__(config)
        self.ollama_interface: Optional[OllamaModelInference] = None
        
        # Handle different config types with detailed logging
        offline_logger.info("🔍 PROCESSING CONFIG for model name...")
        if self.config and hasattr(self.config, 'get_value'):
            self.model_name = self.config.get_value("model_name", "llama3.1:8b")
            offline_logger.info(f"🔍 CONFIG TYPE: Attribute-based, MODEL: {self.model_name}")
        elif self.config and hasattr(self.config, 'get'):
            self.model_name = self.config.get("model_name", "llama3.1:8b")
            offline_logger.info(f"🔍 CONFIG TYPE: Dict-like, MODEL: {self.model_name}")
        elif isinstance(self.config, dict):
            self.model_name = self.config.get("model_name", "llama3.1:8b")
            offline_logger.info(f"🔍 CONFIG TYPE: Dictionary, MODEL: {self.model_name}")
        else:
            self.model_name = "llama3.1:8b"
            offline_logger.info(f"🔍 CONFIG TYPE: Default fallback, MODEL: {self.model_name}")
            
        self.is_engine_initialized = False
        self.generation_stats = {"total_generated": 0, "avg_time": 0, "gpu_utilization": 0}
        
        # 🚀 ENHANCED: Available models for BatchTwoModelPipeline
        self.available_models = self._get_available_models()
        self.thinking_model = self._select_thinking_model()
        self.json_model = self._select_json_model()
        
        offline_logger.info("🏁 OfflineMCQGenerator initialization completed")
        offline_logger.info(f"   • MODEL_NAME: {self.model_name}")
        offline_logger.info(f"   • THINKING_MODEL: {self.thinking_model}")
        offline_logger.info(f"   • JSON_MODEL: {self.json_model}")
        offline_logger.info(f"   • ENGINE_INITIALIZED: {self.is_engine_initialized}")

    def _get_available_models(self) -> List[str]:
        """Get list of available Ollama models"""
        try:
            import requests
            response = requests.get('http://127.0.0.1:11434/api/tags', timeout=5)
            if response.status_code == 200:
                data = response.json()
                models = [model['name'] for model in data.get('models', [])]
                offline_logger.info(f"🤖 Available models: {models}")
                return models
            return []
        except Exception as e:
            offline_logger.warning(f"⚠️ Error getting models: {e}")
            return []
    
    def _select_thinking_model(self) -> str:
        """Select best available model for thinking (expert mode)"""
        thinking_options = [
            "deepseek-r1:14b", "deepseek-r1:7b", "deepseek-r1", 
            "deepseek-v3", "qwq:32b", "qwq", "llama3.1:8b"
        ]
        
        for model in thinking_options:
            if model in self.available_models:
                offline_logger.info(f"🧠 Selected thinking model: {model}")
                return model
        
        # Fallback to default model
        return self.model_name
    
    def _select_json_model(self) -> str:
        """Select best available model for JSON formatting"""
        json_options = [
            "llama3.1:8b", "llama3.1", "llama3:8b", "llama3", 
            "mistral", "phi3"
        ]
        
        for model in json_options:
            if model in self.available_models:
                offline_logger.info(f"📄 Selected JSON model: {model}")
                return model
        
        # Fallback to default model
        return self.model_name

    def _generate_expert_questions_optimized(self, topic: str, context: str, num_questions: int, question_type: str) -> List[Dict[str, Any]]:
        """🚀 OPTIMIZED expert-level questions using single model for speed"""

        offline_logger.info("🚀 OPTIMIZED SINGLE-MODEL EXPERT GENERATION")
        offline_logger.info(f"🧠 Model: {self.model_name}")

        questions = []

        for i in range(num_questions):
            try:
                # Create optimized expert prompt (single model, direct JSON)
                prompt = self._create_optimized_expert_prompt(topic, context, question_type, i)

                # Generate with timeout protection
                result = self._generate_single_question_with_timeout(prompt, timeout=45)

                if result and self._is_valid_mcq_response(result):
                    questions.append(result)
                    offline_logger.info(f"✅ Expert question {i+1}/{num_questions} generated successfully")
                else:
                    offline_logger.warning(f"❌ Expert question {i+1}/{num_questions} failed - using fallback")
                    # Quick fallback to ensure we get a question
                    fallback = self._generate_fallback_expert_question(topic, question_type, i)
                    if fallback:
                        questions.append(fallback)

            except Exception as e:
                offline_logger.error(f"❌ Expert question {i+1} generation failed: {e}")
                # Ensure we always return something
                fallback = self._generate_fallback_expert_question(topic, question_type, i)
                if fallback:
                    questions.append(fallback)

        offline_logger.info(f"🎓 Expert generation complete: {len(questions)}/{num_questions} questions")
        return questions

    def _create_optimized_expert_prompt(self, topic: str, context: str, question_type: str, question_index: int) -> str:
        """Create optimized expert prompt for single-model generation"""

        # Simplified expert prompt - direct and efficient
        prompt = f"""Generate a PhD-level {question_type} question about {topic}.

REQUIREMENTS:
- Advanced, graduate-level complexity
- Specific, detailed scenarios
- No basic "what is" questions
- Include recent research concepts (2020-2024)

TOPIC: {topic}
CONTEXT: {context}

OUTPUT FORMAT (JSON):
{{
  "question": "Advanced question text here",
  "options": ["A) option", "B) option", "C) option", "D) option"],
  "correct_answer": "A",
  "explanation": "Detailed explanation with advanced concepts"
}}

Generate the JSON now:"""

        return prompt

    def _generate_fallback_expert_question(self, topic: str, question_type: str, index: int) -> Dict[str, Any]:
        """Generate a quick fallback expert question"""

        return {
            "question": f"Which advanced concept in {topic} involves complex multi-step analysis and recent research developments?",
            "options": [
                "A) Basic theoretical framework",
                "B) Advanced computational modeling with machine learning integration",
                "C) Simple observational study",
                "D) Elementary textbook definition"
            ],
            "correct_answer": "B",
            "explanation": f"Advanced {topic} research involves sophisticated computational approaches and interdisciplinary methodologies that go beyond basic theoretical frameworks.",
            "metadata": {
                "difficulty": "expert",
                "question_type": question_type,
                "generation_method": "optimized_fallback",
                "question_index": index
            }
        }

    def _generate_expert_questions_batch(self, topic: str, context: str, num_questions: int, question_type: str) -> List[Dict[str, Any]]:
        """🎓 Generate expert-level questions using BatchTwoModelPipeline"""
        
        offline_logger.info("🎓 BATCH TWO-MODEL PIPELINE for Expert Questions")
        offline_logger.info(f"🧠 Thinking: {self.thinking_model} → 📄 JSON: {self.json_model}")
        
        try:
            # Create test cases for batch processing
            test_cases = []
            for i in range(num_questions):
                test_cases.append({
                    'domain': self._extract_domain_from_topic(topic),
                    'topic': topic,
                    'difficulty': 'expert',
                    'context': context
                })
            
            # Step 1: Generate thinking for all questions
            batch_thinking = self._batch_generate_thinking(test_cases, topic, question_type)
            if not batch_thinking:
                offline_logger.error("❌ Failed to generate batch thinking")
                return []
            
            # Step 2: Extract individual thinking blocks
            individual_thinking = self._extract_individual_thinking(batch_thinking, test_cases)
            
            # Step 3: Generate JSON for each question
            results = self._batch_generate_json(individual_thinking, test_cases)
            
            # Filter successful results
            expert_questions = [r for r in results if r is not None]
            
            offline_logger.info(f"🎓 Expert pipeline generated {len(expert_questions)}/{num_questions} questions")
            return expert_questions
            
        except Exception as e:
            offline_logger.error(f"❌ Expert generation failed: {e}")
            offline_logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return []
    
    def _extract_domain_from_topic(self, topic: str) -> str:
        """Extract domain from topic string"""
        topic_lower = topic.lower()
        if any(word in topic_lower for word in ['physics', 'force', 'energy', 'quantum', 'atom']):
            return 'Physics'
        elif any(word in topic_lower for word in ['chemistry', 'molecule', 'reaction', 'chemical']):
            return 'Chemistry'
        elif any(word in topic_lower for word in ['math', 'equation', 'calculus', 'algebra']):
            return 'Mathematics'
        else:
            return 'General'
    
    def _batch_generate_thinking(self, test_cases: List[Dict], topic: str, question_type: str) -> Optional[str]:
        """Step 1: Generate thinking for multiple expert questions - AGGRESSIVE PhD-LEVEL DEMANDS"""
        
        # Question type specific instructions for expert mode
        type_instruction = ""
        if question_type.lower() == "numerical":
            type_instruction = """
🔢🚨🔢 EXPERT MODE NUMERICAL REQUIREMENTS 🔢🚨🔢

**PhD-LEVEL NUMERICAL PURITY:**
🎯 MANDATORY CALCULATION VERBS: "Calculate", "Compute", "Solve", "Determine", "Find", "Evaluate"
🎯 ADVANCED MATHEMATICS: Complex equations, multi-step derivations, sophisticated analysis
🎯 QUANTITATIVE RIGOR: PhD-level formulas, numerical methods, computational techniques
🎯 NUMERICAL OPTIONS: ALL 4 options = advanced numerical values with proper units
🎯 CALCULATION COMPLEXITY: Multi-step problem solving requiring expert mathematical skills
🎯 MINIMUM 150+ CHARACTERS: Highly complex numerical problems

🚫 CONCEPTUAL CONTAMINATION - ZERO TOLERANCE:
❌ "explain" ❌ "why" ❌ "how" ❌ "describe" ❌ "analyze" ❌ "discuss"
❌ Text-based options ❌ Conceptual explanations ❌ Theoretical discussions
❌ NO qualitative reasoning, NO descriptive content

**EXPERT MODE NUMERICAL EXAMPLES:**
✅ "Calculate the relativistic kinetic energy correction for an electron accelerated through a potential difference of 1.5 MV"
✅ "Determine the magnetic dipole moment of a hydrogen atom in the 2p₁/₂ state including spin-orbit coupling effects"
✅ "Solve for the reaction quotient Q when [A] = 0.025 M, [B] = 0.040 M in the equilibrium: 2A + 3B ⇌ C + 2D"
"""
        elif question_type.lower() == "conceptual":
            type_instruction = """
🧠🚨🧠 EXPERT MODE CONCEPTUAL REQUIREMENTS 🧠🚨🧠

**PhD-LEVEL CONCEPTUAL PURITY:**
🎯 MANDATORY UNDERSTANDING VERBS: "Explain", "Why", "How", "What happens", "Describe", "Analyze"
🎯 THEORETICAL DEPTH: Advanced principles, complex mechanisms, sophisticated theories
🎯 QUALITATIVE REASONING: Deep understanding of cause-effect relationships
🎯 CONCEPTUAL OPTIONS: ALL 4 options = sophisticated theoretical explanations
🎯 ANALYTICAL COMPLEXITY: Multi-layered reasoning requiring expert theoretical knowledge
🎯 MINIMUM 150+ CHARACTERS: Highly complex conceptual problems

🚫 NUMERICAL CONTAMINATION - ZERO TOLERANCE:
❌ "calculate" ❌ "compute" ❌ "solve" ❌ "determine" ❌ "find" ❌ "evaluate"
❌ Numbers with units ❌ Mathematical expressions ❌ Equations ❌ Formulas
❌ NO computational elements, NO numerical analysis

**EXPERT MODE CONCEPTUAL EXAMPLES:**
✅ "Explain the quantum mechanical basis for the Pauli exclusion principle and its role in determining electron configurations in multi-electron atoms"
✅ "Why does the entropy of the universe increase during spontaneous chemical reactions according to the second law of thermodynamics?"
✅ "How does electromagnetic induction relate to relativistic effects in moving conductors according to special relativity theory?"
"""
        else:
            type_instruction = """
🔀 EXPERT MODE MIXED REQUIREMENTS:
✅ Can include either advanced numerical OR sophisticated conceptual elements
✅ Vary between quantitative PhD-level analysis and theoretical understanding
✅ Mix complex calculations with deep theoretical reasoning
✅ Balance advanced mathematical skills with conceptual expertise
✅ MINIMUM 150+ CHARACTERS for all questions
"""

        # 🎓 USING SUCCESSFUL TEST PROMPTS: Much more demanding requirements
        prompt_parts = [f"""Generate PhD-level multiple choice questions that require DEEP EXPERTISE. Each question MUST be:

1. **HIGHLY COMPLEX** - Minimum 150+ characters, requiring advanced understanding
2. **MULTI-STEP REASONING** - Cannot be answered by simple recall
3. **MATHEMATICALLY RIGOROUS** - Include equations, derivations, or quantitative analysis where applicable
4. **DOMAIN-SPECIFIC TERMINOLOGY** - Use advanced technical vocabulary extensively
5. **THEORETICAL DEPTH** - Test understanding of underlying principles, not just facts

{type_instruction}

Generate questions for these test cases with MAXIMUM COMPLEXITY:

"""]
        
        for i, case in enumerate(test_cases, 1):
            domain = case.get('domain', 'General')
            topic_text = case.get('topic', 'General')
            context = case.get('context', '')
            difficulty = case.get('difficulty', 'expert')
            
            complexity_instruction = f"""
**EXPERT LEVEL REQUIREMENTS for {question_type.upper()} questions:**
- Question must require PhD-level knowledge
- Include complex mathematical relationships or advanced theoretical concepts
- Test synthesis of multiple advanced concepts
- Require multi-step logical reasoning
- Use domain-specific technical terminology extensively
- Question length: MINIMUM 150 characters
- Should be challenging even for graduate students and professors
- MUST follow {question_type.upper()} requirements above
"""
            
            prompt_parts.append(f"""
Test Case {i}: {domain} - {topic_text} (EXPERT difficulty - PhD Level {question_type.upper()})
{complexity_instruction}

Think through:
- What ADVANCED {question_type} concept should be tested that requires deep expertise?
- What complex mathematical or theoretical relationship is involved?
- What the correct answer should be (must require advanced reasoning)
- What sophisticated misconceptions could be used as distractors?
- How to make this question challenge even expert-level students?
- What advanced terminology and concepts must be included?

MAKE THIS QUESTION EXTREMELY CHALLENGING AND COMPLEX FOR {question_type.upper()} TYPE!

""")
        
        prompt_parts.append(f"""
CRITICAL INSTRUCTIONS:
- EXPERT questions must be PhD dissertation-level complexity
- Use advanced mathematical notation, complex terminology
- Test deep theoretical understanding, not simple facts
- Questions should require multiple steps of reasoning
- Include quantitative analysis where appropriate
- Make questions that would challenge university professors
- MINIMUM 150 characters for expert questions
- Use domain-specific advanced vocabulary extensively
- MUST STRICTLY FOLLOW {question_type.upper()} REQUIREMENTS

Generate the most sophisticated, complex {question_type} questions possible!""")
        
        full_prompt = "".join(prompt_parts)
        
        offline_logger.info(f"🧠 AGGRESSIVE PhD-LEVEL {question_type} thinking generation with {self.thinking_model}")
        offline_logger.info(f"🎓 Using successful test prompts for maximum complexity")
        response = self._generate_with_retry(full_prompt, model_override=self.thinking_model, max_tokens=12000)
        
        if response:
            offline_logger.info(f"✅ PhD-level {question_type} thinking generated ({len(response)} chars)")
            return response
        else:
            offline_logger.error(f"❌ No PhD-level {question_type} thinking generated")
            return None
    
    def _extract_individual_thinking(self, response: str, test_cases: List[Dict]) -> List[str]:
        """Step 2: Extract thinking for each individual test case"""
        
        # Extract content from <think> blocks if present
        think_blocks = re.findall(r'<think>(.*?)</think>', response, re.DOTALL)
        
        if think_blocks:
            extracted_thinking = "\n\n".join(think_blocks).strip()
        else:
            extracted_thinking = response.strip()
        
        # Try to split thinking by test case markers
        individual_thinking = []
        
        for i in range(len(test_cases)):
            case_marker = f"Test Case {i+1}:"
            
            # Find the section for this test case
            start_idx = extracted_thinking.find(case_marker)
            if start_idx != -1:
                # Find the next test case marker or end of text
                next_case_marker = f"Test Case {i+2}:"
                end_idx = extracted_thinking.find(next_case_marker, start_idx)
                
                if end_idx == -1:
                    case_thinking = extracted_thinking[start_idx:]
                else:
                    case_thinking = extracted_thinking[start_idx:end_idx]
                
                individual_thinking.append(case_thinking.strip())
            else:
                # If we can't find specific markers, split evenly
                chunk_size = len(extracted_thinking) // len(test_cases)
                start = i * chunk_size
                end = (i + 1) * chunk_size if i < len(test_cases) - 1 else len(extracted_thinking)
                individual_thinking.append(extracted_thinking[start:end].strip())
        
        offline_logger.info(f"📋 Extracted {len(individual_thinking)} individual thinking blocks")
        return individual_thinking
    
    def _batch_generate_json(self, thinking_list: List[str], test_cases: List[Dict]) -> List[Optional[Dict]]:
        """Step 3: Generate JSON for each question individually with retry logic"""
        
        results = []
        
        for i, (thinking, test_case) in enumerate(zip(thinking_list, test_cases)):
            domain = test_case.get('domain', 'General')
            topic = test_case.get('topic', 'General') 
            difficulty = test_case.get('difficulty', 'expert')
            
            # 🎓 USING SUCCESSFUL TEST PROMPTS: Aggressive PhD-level JSON demands
            complexity_reminder = """
EXPERT LEVEL JSON REQUIREMENTS:
- Question MUST be PhD dissertation-level complexity (minimum 150 characters)
- Include advanced mathematical notation, equations, or technical terminology
- Test deep theoretical understanding requiring multi-step reasoning
- Use sophisticated domain-specific vocabulary extensively
- Question should challenge even university professors
- Options must be technically sophisticated, not simple choices
- QUESTION MUST END WITH A QUESTION MARK (?)
"""
            
            prompt = f"""Generate a valid JSON MCQ from this thinking:

THINKING: {thinking}

🚨 CRITICAL JSON FORMAT - EXACT OUTPUT REQUIRED:
{{
  "question": "EXACTLY ONE PhD-level question ending with ?",
  "options": {{
    "A": "First option",
    "B": "Second option", 
    "C": "Third option",
    "D": "Fourth option"
  }},
  "correct": "A",
  "explanation": "Brief explanation"
}}

{complexity_reminder}

🚨 MANDATORY RULES:
- EXACTLY 4 options in A,B,C,D format  
- Use "correct" not "correct_answer"
- Question must end with ?
- No extra commas or brackets
- No explanatory text outside JSON
- Generate ONLY the JSON object above"""
            
            offline_logger.info(f"📄 JSON generation {i+1}/{len(test_cases)} with {self.json_model}")
            
            # Try up to 3 times for better reliability
            json_data = None
            for attempt in range(3):
                # 🎓 PhD-LEVEL: Buffer-based generation to avoid JSON truncation
                response = self._generate_with_chunked_buffer(prompt, model_override=self.json_model, max_tokens=2000)
                
                if response:
                    json_data = self._parse_json_response_robust(response)
                    if json_data:
                        break  # Success, exit retry loop
                    elif attempt < 2:  # Not the last attempt
                        offline_logger.warning(f"   ⚠️  Retry {attempt + 1}/3 for JSON {i+1}")
                
                if attempt == 2:  # Last attempt failed
                    offline_logger.error(f"   ❌ JSON {i+1} failed after 3 attempts")
            
            if json_data:
                # Fix question mark if missing
                question = json_data.get('question', '')
                if question and not question.endswith('?'):
                    json_data['question'] = question.rstrip('.!') + '?'
                
                # Add metadata
                json_data['difficulty'] = difficulty
                json_data['domain'] = domain
                json_data['topic'] = topic
                json_data['generated_by'] = f"{self.thinking_model} + {self.json_model}"
                json_data['pipeline'] = "batch_two_model_expert"
                results.append(json_data)
                offline_logger.info(f"   ✅ JSON {i+1} generated")
            else:
                results.append(None)
                offline_logger.error(f"   ❌ JSON {i+1} no valid response")
        
        return results
    
    def _generate_with_retry(self, prompt: str, model_override: str = None, max_tokens: int = 2000) -> Optional[str]:
        """Generate text with robust retry logic and connection handling"""
        
        model_to_use = model_override or self.model_name
        max_retries = 3
        
        # 🚀 CRITICAL FIX: Use longer timeouts for DeepSeek-R1 reasoning model
        if model_override and 'deepseek' in model_override.lower():
            base_timeout = 180  # 3 minutes for reasoning models
            offline_logger.info(f"🧠 Using extended timeout for reasoning model {model_to_use}: {base_timeout}s base")
        else:
            base_timeout = 45  # Increased from 30s for better reliability
        
        for attempt in range(max_retries):
            try:
                offline_logger.info(f"🔄 Generation attempt {attempt + 1}/{max_retries} with {model_to_use}")
                
                # 🚀 OPTIMIZED: Progressive timeout for reasoning vs fast models
                if model_override and 'deepseek' in model_override.lower():
                    timeout_duration = base_timeout + (attempt * 60)  # 180s, 240s, 300s (reasoning)
                else:
                    timeout_duration = base_timeout + (attempt * 15)  # 45s, 60s, 75s (fast models)
                
                offline_logger.info(f"⏱️ Using timeout: {timeout_duration}s for attempt {attempt + 1}")
                
                # Create temporary interface if using different model
                if model_override and model_override != self.model_name:
                    # Use direct API call for different model
                    import requests
                    
                    # 🚀 SPEED OPTIMIZATIONS: Model-specific parameters
                    if 'deepseek' in model_to_use.lower():
                        # DeepSeek-R1 optimized for fast expert reasoning
                        options = {
                            "num_predict": min(max_tokens, 800),  # Limit tokens for speed
                            "temperature": 0.6,  # Lower for focused thinking
                            "top_p": 0.85,  # Reduced for speed
                            "top_k": 30,  # Reduced for speed  
                            "repeat_penalty": 1.02,  # Minimal to avoid slowdown
                            "num_gpu": -1,  # Use all GPU layers
                            "num_thread": 12,  # Max threads for CPU
                            "batch_size": 1024,  # Large batch for GPU efficiency
                        }
                    else:
                        # Standard model optimizations
                        options = {
                            "num_predict": max_tokens,
                            "temperature": 0.75,
                            "top_p": 0.9,
                            "top_k": 40,
                            "num_gpu": -1,
                            "batch_size": 512,
                        }
                    
                    payload = {
                        "model": model_to_use,
                        "prompt": prompt,
                        "stream": False,
                        "options": options
                    }
                    
                    # 🚀 CRITICAL FIX: Use streaming for DeepSeek to show progress
                    if 'deepseek' in model_to_use.lower():
                        # Enable streaming for DeepSeek to prevent UI freeze
                        payload["stream"] = True
                        offline_logger.info(f"🤖 Starting DeepSeek {model_to_use} with streaming...")

                        import requests
                        response = requests.post(
                            'http://127.0.0.1:11434/api/generate',
                            json=payload,
                            timeout=timeout_duration,
                            stream=True
                        )
                        response.raise_for_status()

                        # Process streaming response with progress updates
                        result = ""
                        chunk_count = 0
                        for line in response.iter_lines():
                            if line:
                                try:
                                    chunk_data = json.loads(line.decode('utf-8'))
                                    chunk_response = chunk_data.get('response', '')
                                    result += chunk_response

                                    # Show progress every 10 chunks
                                    chunk_count += 1
                                    if chunk_count % 10 == 0:
                                        offline_logger.info(f"🤖 DeepSeek generating... ({len(result)} chars)")

                                    if chunk_data.get('done', False):
                                        offline_logger.info(f"✅ DeepSeek completed ({len(result)} chars)")
                                        break

                                except json.JSONDecodeError:
                                    continue

                        result = result.strip()
                    else:
                        # Use synchronous request for non-streaming
                        import requests
                        response = requests.post(
                            'http://127.0.0.1:11434/api/generate',
                            json=payload,
                            timeout=timeout_duration
                        )
                        response.raise_for_status()

                        data = response.json()
                        result = data.get('response', '').strip()
                else:
                    # Use existing interface with appropriate timeout
                    result = self.ollama_interface.generate_text(prompt, request_timeout=timeout_duration)
                
                if result:  # Success with valid response
                    if attempt > 0:  # Recovered from previous failures
                        offline_logger.info(f"   ✅ Recovered on attempt {attempt + 1}")
                    return result
                else:
                    offline_logger.warning(f"   ⚠️  Empty response from {model_to_use} (attempt {attempt + 1})")
                    
            except Exception as e:
                offline_logger.warning(f"   ❌ Error calling {model_to_use} (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    import time
                    backoff_time = 2 ** attempt  # Exponential backoff: 1s, 2s, 4s
                    offline_logger.info(f"   ⏳ Waiting {backoff_time}s before retry...")
                    time.sleep(backoff_time)
        
        offline_logger.error(f"   ❌ All {max_retries} attempts failed for {model_to_use}")
        return None

    def _generate_with_chunked_buffer(self, prompt: str, model_override: str = None, max_tokens: int = 2000) -> Optional[str]:
        """🚀 WEB SEARCH SOLUTION: Buffer-based generation to prevent JSON truncation"""
        offline_logger.info(f"🔧 Using chunked buffer generation for {max_tokens} tokens")
        
        # Try normal generation first for smaller requests
        if max_tokens <= 1000:
            return self._generate_with_retry(prompt, model_override, max_tokens)
        
        try:
            model_to_use = model_override or self.model_name
            
            # 🔧 SOLUTION 1: Multiple smaller requests that concatenate
            chunk_size = 800
            accumulated_response = ""
            complete_json_found = False
            
            for chunk_num in range(1, 4):  # Max 3 chunks
                current_tokens = min(chunk_size, max_tokens - len(accumulated_response.split()))
                
                if chunk_num == 1:
                    chunk_prompt = prompt
                else:
                    # Continue from previous response
                    chunk_prompt = f"Continue this JSON response exactly where it left off:\n\n{accumulated_response}\n\nComplete the JSON structure:"
                
                offline_logger.info(f"🔧 Generating chunk {chunk_num} with {current_tokens} tokens")
                
                chunk_response = self._generate_with_retry(
                    chunk_prompt, 
                    model_override, 
                    max_tokens=current_tokens
                )
                
                if chunk_response:
                    if chunk_num == 1:
                        accumulated_response = chunk_response
                    else:
                        # Smart concatenation: remove duplicate JSON starts
                        chunk_cleaned = chunk_response.strip()
                        if chunk_cleaned.startswith('{'):
                            # Find where previous JSON likely ended
                            if accumulated_response.rstrip().endswith(','):
                                accumulated_response = accumulated_response.rstrip()[:-1] + chunk_cleaned[1:]
                            else:
                                accumulated_response += chunk_cleaned[1:]
                        else:
                            accumulated_response += chunk_cleaned
                    
                    # Check if we have complete JSON
                    if self._is_complete_json(accumulated_response):
                        offline_logger.info(f"✅ Complete JSON found in chunk {chunk_num}")
                        complete_json_found = True
                        break
                        
                else:
                    offline_logger.warning(f"⚠️ Chunk {chunk_num} generation failed")
                    break
            
            if complete_json_found:
                return accumulated_response
            
            # 🔧 SOLUTION 2: Fallback to very direct, short prompt
            offline_logger.info("🔧 Fallback: Using ultra-short direct prompt")
            
            # Extract topic from prompt for better fallback
            topic_match = re.search(r'about (.+?) with', prompt, re.IGNORECASE)
            fallback_topic = topic_match.group(1) if topic_match else 'magnetism'
            
            # 🚫 NO HARDCODED FALLBACK QUESTIONS - Raise error instead
            offline_logger.error("🚫 HARDCODED FALLBACK QUESTIONS DISABLED")
            offline_logger.error(f"❌ Cannot create hardcoded fallback for topic '{fallback_topic}' - AI generation required")
            raise Exception(f"AI model generation failed for '{fallback_topic}' - no hardcoded content available")
            
            return self._generate_with_retry(fallback_prompt, model_override, max_tokens=600)
            
        except Exception as e:
            offline_logger.error(f"❌ Chunked buffer generation failed: {str(e)}")
            # Final fallback to original method
            return self._generate_with_retry(prompt, model_override, max_tokens)
    
    def _is_complete_json(self, text: str) -> bool:
        """Check if text contains a complete, valid JSON object"""
        try:
            # Clean the text
            cleaned = text.strip()
            
            # Find JSON boundaries
            start_brace = cleaned.find('{')
            end_brace = cleaned.rfind('}')
            
            if start_brace == -1 or end_brace == -1:
                return False
            
            json_part = cleaned[start_brace:end_brace + 1]
            
            # Check brace balance
            open_braces = json_part.count('{')
            close_braces = json_part.count('}')
            
            if open_braces != close_braces:
                return False
            
            # Try to parse
            import json
            parsed = json.loads(json_part)
            
            # Basic structure check
            return (isinstance(parsed, dict) and 
                   'question' in parsed and 
                   'options' in parsed and
                   ('correct' in parsed or 'correct_answer' in parsed))
                   
        except:
            return False
    
    def _parse_json_response_robust(self, content: str) -> Optional[Dict[str, Any]]:
        """🚀 ENHANCED: Robust JSON extraction with streaming-aware parsing"""
        
        if not content or not content.strip():
            return None
        
        # 🔧 WEB SEARCH SOLUTION: Handle streaming/chunked responses
        # Concatenate all content and then parse (GitHub issue solution)
        accumulated_content = self._concatenate_streaming_chunks(content)
        
        # Method 1: Look for JSON blocks with code fences
        json_block_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', accumulated_content, re.DOTALL)
        if json_block_match:
            json_str = json_block_match.group(1)
            try:
                parsed = json.loads(json_str)
                if self._validate_json_structure_robust(parsed):
                    return parsed
            except:
                pass
        
        # Method 1b: Look for JSON blocks with backticks but no language identifier
        json_block_match2 = re.search(r'```\s*(\{.*?\})\s*```', accumulated_content, re.DOTALL)
        if json_block_match2:
            json_str = json_block_match2.group(1)
            try:
                parsed = json.loads(json_str)
                if self._validate_json_structure_robust(parsed):
                    return parsed
            except:
                pass
        
        # Method 2: Look for any JSON object (most permissive) - improved regex
        # This handles nested braces better
        json_matches = re.findall(r'\{(?:[^{}]|{[^{}]*})*\}', accumulated_content, re.DOTALL)
        for json_match in json_matches:
            try:
                parsed = json.loads(json_match)
                if self._validate_json_structure_robust(parsed):
                    return parsed
            except:
                continue
        
        # Method 3: Try parsing entire response after aggressive cleaning
        try:
            # Clean response more aggressively
            cleaned = accumulated_content.strip()
            
            # Remove common intro patterns - ENHANCED for all cases
            intro_patterns = [
                r'Here is the (?:valid )?JSON object.*?:',
                r'Here is the (?:generated )?JSON.*?:',
                r'Here is the JSON structure.*?:',
                r'Based on.*?here is.*?:',
                r'The JSON object.*?:',
                r'Here\'s the.*?:',
                r'.*?PhD-level.*?question.*?:'
            ]
            for pattern in intro_patterns:
                cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE | re.DOTALL)
            
            # Remove code fences
            if cleaned.startswith('```json'):
                cleaned = cleaned[7:]
            elif cleaned.startswith('```'):
                cleaned = cleaned[3:]
            if cleaned.endswith('```'):
                cleaned = cleaned[:-3]
            cleaned = cleaned.strip()
            
            # Remove any leading/trailing text before/after JSON
            start_brace = cleaned.find('{')
            end_brace = cleaned.rfind('}')
            if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
                json_part = cleaned[start_brace:end_brace + 1]
                parsed = json.loads(json_part)
                if self._validate_json_structure_robust(parsed):
                    return parsed
        except:
            pass
        
        # Method 4: Try to fix common JSON issues
        try:
            fixed_response = self._fix_common_json_issues(accumulated_content)
            if fixed_response:
                parsed = json.loads(fixed_response)
                if self._validate_json_structure_robust(parsed):
                    return parsed
        except:
            pass
        
        # Method 5: 🔧 WEB SEARCH SOLUTION: Progressive JSON building
        try:
            progressive_json = self._build_progressive_json(accumulated_content)
            if progressive_json:
                parsed = json.loads(progressive_json)
                if self._validate_json_structure_robust(parsed):
                    return parsed
        except:
            pass
        
        offline_logger.error(f"❌ All JSON parsing methods failed for content: {accumulated_content[:200]}...")
        return None
    
    def _concatenate_streaming_chunks(self, content: str) -> str:
        """🔧 WEB SEARCH SOLUTION: Handle streaming response concatenation"""
        try:
            # Split content by potential streaming separators and recombine
            # This addresses the GitHub issue about partial JSON chunks
            lines = content.split('\n')
            json_parts = []
            
            for line in lines:
                line = line.strip()
                if line and ('{' in line or '}' in line or '"' in line):
                    json_parts.append(line)
            
            # Recombine JSON parts
            recombined = ' '.join(json_parts)
            
            # Clean up obvious streaming artifacts
            recombined = re.sub(r'\}\s*\{', '}{', recombined)  # Fix split braces
            recombined = re.sub(r'"\s*"', '""', recombined)    # Fix split quotes
            
            return recombined if recombined else content
            
        except:
            return content
    
    def _build_progressive_json(self, content: str) -> Optional[str]:
        """🔧 WEB SEARCH SOLUTION: Build JSON progressively from fragments"""
        try:
            # Extract potential JSON key-value pairs
            question_match = re.search(r'"question"\s*:\s*"([^"]*)"', content, re.IGNORECASE)
            options_match = re.search(r'"options"\s*:\s*(\[.*?\]|\{.*?\})', content, re.DOTALL)
            correct_match = re.search(r'"(?:correct|correct_answer)"\s*:\s*"([ABCD])"', content, re.IGNORECASE)
            explanation_match = re.search(r'"explanation"\s*:\s*"([^"]*)"', content, re.IGNORECASE)
            
            if question_match and options_match and correct_match:
                # Build complete JSON from parts
                question = question_match.group(1)
                options = options_match.group(1)
                correct = correct_match.group(1)
                explanation = explanation_match.group(1) if explanation_match else "Expert explanation"
                
                # 🔧 FIX: Ensure options are always in dictionary format for UI consistency
                if options.startswith('['):
                    # Convert array format to dict
                    try:
                        options_list = json.loads(options)
                        if len(options_list) == 4:
                            options = json.dumps({
                                'A': options_list[0],
                                'B': options_list[1], 
                                'C': options_list[2],
                                'D': options_list[3]
                            })
                    except:
                        pass
                elif options.startswith('{'):
                    # Validate that dict format has correct keys
                    try:
                        options_dict = json.loads(options)
                        option_keys = ['A', 'B', 'C', 'D']
                        if not all(key in options_dict for key in option_keys):
                            offline_logger.warning("⚠️ Options dict missing required keys, using fallback")
                            return None
                        # Options already in correct format
                    except:
                        pass
                
                progressive_json = f"""{{
                    "question": "{question}",
                    "options": {options},
                    "correct": "{correct}",
                    "explanation": "{explanation}"
                }}"""
                
                offline_logger.info("✅ Successfully built progressive JSON from fragments")
                return progressive_json
                
        except Exception as e:
            offline_logger.warning(f"⚠️ Progressive JSON building failed: {str(e)}")
            
        return None
    
    def _validate_json_structure_robust(self, parsed_json: Dict) -> bool:
        """Validate that the JSON has the required MCQ structure"""
        
        if not isinstance(parsed_json, dict):
            return False
            
        required_fields = ['question', 'options', 'correct']
        if not all(field in parsed_json for field in required_fields):
            return False
        
        # Check question is non-empty string
        question = parsed_json.get('question', '')
        if not isinstance(question, str) or not question.strip():
            return False
        
        # 🔧 FIX: Ensure options are always in dictionary format for UI consistency
        options = parsed_json.get('options', [])
        if isinstance(options, dict):
            # Handle {"A": "...", "B": "...", "C": "...", "D": "..."} format
            option_keys = ['A', 'B', 'C', 'D']
            if not all(key in options for key in option_keys):
                offline_logger.warning(f"⚠️ Options missing required keys: {set(option_keys) - set(options.keys())}")
                return False
            if not all(isinstance(options[key], str) and options[key].strip() for key in option_keys):
                offline_logger.warning("⚠️ Options contain empty or non-string values")
                return False
            # Options are already in correct dictionary format
        elif isinstance(options, list):
            # Handle ["...", "...", "...", "..."] format - convert to dict format
            if len(options) < 4:
                offline_logger.warning(f"⚠️ Options list has {len(options)} items, need at least 4")
                return False
            elif len(options) > 4:
                offline_logger.warning(f"⚠️ Options list has {len(options)} items, using first 4")
                options = options[:4]  # Truncate to first 4 options
            
            if not all(isinstance(opt, str) and opt.strip() for opt in options[:4]):
                offline_logger.warning("⚠️ Options list contains empty or non-string values")
                return False
            # Convert array to dict format for UI consistency
            offline_logger.info("🔧 Converting options from list to dictionary format")
            parsed_json['options'] = {
                'A': options[0],
                'B': options[1], 
                'C': options[2],
                'D': options[3]
            }
        else:
            offline_logger.warning(f"⚠️ Options has invalid type: {type(options)}")
            return False
        
        # Check correct answer (support both "correct" and "correct_answer" formats)
        correct = parsed_json.get('correct', parsed_json.get('correct_answer', ''))
        if not isinstance(correct, str) or correct not in ['A', 'B', 'C', 'D']:
            offline_logger.warning(f"⚠️ Invalid correct answer: '{correct}'")
            return False
        
        # Normalize to "correct" format if using "correct_answer"
        if 'correct_answer' in parsed_json and 'correct' not in parsed_json:
            parsed_json['correct'] = parsed_json['correct_answer']
        
        offline_logger.info("✅ JSON structure validation passed")
        return True
    
    def _fix_common_json_issues(self, response: str) -> Optional[str]:
        """Attempt to fix common JSON formatting issues"""
        
        try:
            # Find the JSON part
            start_brace = response.find('{')
            end_brace = response.rfind('}')
            if start_brace == -1 or end_brace == -1:
                return None
            
            json_part = response[start_brace:end_brace + 1]
            
            # Fix common issues
            # Remove trailing commas before } or ]
            json_part = re.sub(r',(\s*[}\]])', r'\1', json_part)
            
            # Ensure proper quoting of keys
            json_part = re.sub(r'(\w+):', r'"\1":', json_part)
            
            # Fix single quotes to double quotes
            json_part = json_part.replace("'", '"')
            
            return json_part
        except:
            return None
    
    def _parse_batch_mcq_response_robust(self, response: str, expected_questions: int, topic: str) -> List[Dict[str, Any]]:
        """🚀 ENHANCED: Parse multiple MCQ questions with robust JSON extraction"""
        try:
            offline_logger.info(f"🔍 Robust parsing batch response for {expected_questions} questions...")
            
            # First try the original parsing method
            original_result = self._parse_batch_mcq_response(response, expected_questions, topic)
            if original_result and len(original_result) >= expected_questions // 2:
                offline_logger.info(f"✅ Original parser succeeded: {len(original_result)} questions")
                return original_result
            
            # Enhanced parsing with robust JSON extraction
            offline_logger.info("🔄 Trying enhanced parsing methods...")
            
            # Try to extract JSON array using robust methods
            json_arrays = re.findall(r'\[.*?\]', response, re.DOTALL)
            
            for json_array in json_arrays:
                try:
                    questions_data = json.loads(json_array)
                    
                    if isinstance(questions_data, list):
                        parsed_questions = []
                        for i, q_data in enumerate(questions_data[:expected_questions]):
                            if self._validate_json_structure_robust(q_data):
                                # 🔧 FIX: Keep options in dictionary format for consistency
                                # The validation method already converts list to dict, so don't convert back
                                options = q_data.get('options', {})
                                if isinstance(options, dict):
                                    # Ensure we have all required option keys
                                    option_keys = ['A', 'B', 'C', 'D']
                                    if all(key in options for key in option_keys):
                                        # Options are already in correct dict format
                                        pass
                                    else:
                                        offline_logger.warning(f"⚠️ Question {i+1} missing required option keys")
                                        continue
                                elif isinstance(options, list) and len(options) == 4:
                                    # Convert list to dict format for UI consistency
                                    q_data['options'] = {
                                        'A': options[0],
                                        'B': options[1], 
                                        'C': options[2],
                                        'D': options[3]
                                    }
                                else:
                                    offline_logger.warning(f"⚠️ Question {i+1} has invalid options format")
                                    continue
                                
                                parsed_questions.append(q_data)
                                offline_logger.info(f"✅ Robust parsed question {i+1}: {q_data.get('question', '')[:50]}...")
                            else:
                                offline_logger.warning(f"⚠️ Question {i+1} failed robust validation")
                        
                        if parsed_questions:
                            offline_logger.info(f"📋 Robust parsing succeeded: {len(parsed_questions)} questions")
                            return parsed_questions
                            
                except json.JSONDecodeError:
                    continue
            
            # Final fallback: try individual question parsing
            offline_logger.info("🔄 Final fallback: individual question parsing...")
            return self._parse_individual_questions_from_text_robust(response, expected_questions, topic)
            
        except Exception as e:
            offline_logger.error(f"❌ Robust batch parsing error: {e}")
            return []

    def _parse_individual_questions_from_text_robust(self, text: str, expected_questions: int, topic: str) -> List[Dict[str, Any]]:
        """Robust fallback parser to extract individual questions from text response"""
        try:
            questions = []
            
            # Split by common delimiters that might separate questions
            potential_questions = re.split(r'(?:\n\s*\n|\}\s*,?\s*\{)', text)
            
            for i, potential_q in enumerate(potential_questions):
                if i >= expected_questions:
                    break
                    
                # Try robust JSON extraction on this segment
                result = self._parse_json_response_robust(potential_q)
                if result:
                    questions.append(result)
                    offline_logger.info(f"✅ Robust extracted question {len(questions)}: {result.get('question', '')[:50]}...")
            
            offline_logger.info(f"📋 Robust fallback parsing extracted {len(questions)} questions")
            return questions
            
        except Exception as e:
            offline_logger.error(f"❌ Robust fallback parsing error: {e}")
            return []

    def initialize(self) -> bool:
        """Initialize the offline MCQ generator with GPU optimization and comprehensive logging"""
        offline_logger.info(f"🚀 INITIALIZING HIGH-PERFORMANCE Ollama engine for '{self.model_name}'...")
        performance_logger.info(f"🔧 INITIALIZATION START: model={self.model_name}")
        
        start_time = time.time()
        
        try:
            # Create optimized Ollama interface
            offline_logger.info("🔧 CREATING OllamaModelInference...")
            self.ollama_interface = OllamaModelInference()
            offline_logger.info(f"🔧 OllamaModelInference created: {self.ollama_interface is not None}")
            offline_logger.info(f"🔧 OllamaModelInference type: {type(self.ollama_interface)}")
            
            if not self.ollama_interface.is_available():
                offline_logger.error("❌ OLLAMA INTERFACE NOT AVAILABLE")
                offline_logger.error("   • Check if Ollama server is running")
                offline_logger.error("   • Verify Ollama installation")
                offline_logger.error("   • Check network connectivity to Ollama")
                self.ollama_interface = None
                performance_logger.error(f"❌ INITIALIZATION FAILED: Ollama not available after {time.time() - start_time:.3f}s")
                return False
                
            offline_logger.info("🔧 OLLAMA INTERFACE IS AVAILABLE, applying optimizations...")
            
            # Apply TURBO speed optimizations
            offline_logger.info("⚡ APPLYING SPEED OPTIMIZATIONS...")
            self.ollama_interface.optimize_for_speed()
            offline_logger.info("✅ SPEED OPTIMIZATIONS APPLIED")
            
            # Skip test generation for faster startup - actual generation will be the test
            offline_logger.info("⚡ SKIPPING TEST GENERATION for TURBO startup speed")
                
            self.is_engine_initialized = True
            
            init_time = time.time() - start_time
            offline_logger.info("✅ HIGH-PERFORMANCE Ollama engine is connected and GPU-optimized!")
            performance_logger.info(f"✅ INITIALIZATION SUCCESS in {init_time:.3f}s")
            
            # Verify the interface is still set
            offline_logger.info(f"🔧 FINAL CHECK - ollama_interface is not None: {self.ollama_interface is not None}")
            
            # Log performance stats
            if self.ollama_interface:
                try:
                    stats = self.ollama_interface.get_performance_stats()
                    offline_logger.info(f"🎮 GPU STATUS: {stats.get('gpu_optimized', False)}")
                    offline_logger.info(f"🚀 MODEL: {stats.get('model', 'unknown')}")
                    offline_logger.info(f"📊 STATS: {stats}")
                except Exception as e:
                    offline_logger.warning(f"⚠️ FAILED TO GET STATS: {e}")
            
            return True
            
        except Exception as e:
            init_time = time.time() - start_time
            offline_logger.error(f"❌ FAILED TO INITIALIZE Ollama engine: {e}")
            offline_logger.error(f"❌ FULL TRACEBACK: {traceback.format_exc()}")
            performance_logger.error(f"❌ INITIALIZATION FAILED after {init_time:.3f}s: {e}")
            self.ollama_interface = None
            return False

    def _test_generation(self) -> bool:
        """Test generation to ensure the engine works properly"""
        try:
            test_prompt = """Create a multiple choice question about magnetism.

Format as JSON:
{
  "question": "What causes magnetic fields?",
  "options": {
    "A": "Moving electric charges",
    "B": "Static electricity", 
    "C": "Sound waves",
    "D": "Light waves"
  },
  "correct": "A",
  "explanation": "Moving electric charges create magnetic fields"
}

Generate only the JSON:"""
            
            start_time = time.time()
            result = self.ollama_interface.generate_text(test_prompt, request_timeout=30)  # Short timeout for test
            elapsed = time.time() - start_time
            
            if result and len(result) > 50:  # Basic validation
                logger.info(f"✅ Test generation successful in {elapsed:.1f}s")
                logger.info(f"📋 Test result preview: {result[:200]}...")
                return True
            else:
                logger.error(f"❌ Test generation failed or insufficient output: {result}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Test generation error: {e}")
            return False

    def is_available(self) -> bool:
        """Check if the offline generator is ready with comprehensive availability logging"""
        offline_logger.info("🔍 CHECKING OFFLINE GENERATOR AVAILABILITY...")
        
        try:
            # Simple check: do we have an Ollama interface and is it connected?
            if not self.ollama_interface:
                offline_logger.info("🔍 AVAILABILITY CHECK: NO ollama_interface")
                return False
                
            offline_logger.info("🔍 CHECKING Ollama server response...")
            # Check if Ollama server is responding
            ollama_available = self.ollama_interface.is_available()
            offline_logger.info(f"🔍 OLLAMA SERVER AVAILABLE: {ollama_available}")
            
            # Check if we have an active model
            has_model = (hasattr(self.ollama_interface, 'active_model') and 
                        self.ollama_interface.active_model is not None)
            
            active_model = getattr(self.ollama_interface, 'active_model', None)
            offline_logger.info(f"🔍 HAS_ACTIVE_MODEL: {has_model}")
            offline_logger.info(f"🔍 ACTIVE_MODEL: '{active_model}'")
            
            overall_available = ollama_available and has_model
            offline_logger.info(f"🔍 OVERALL AVAILABILITY: {overall_available}")
            offline_logger.info(f"   • OLLAMA_AVAILABLE: {ollama_available}")
            offline_logger.info(f"   • HAS_MODEL: {has_model}")
            offline_logger.info(f"   • ACTIVE_MODEL: '{active_model}'")
            
            return overall_available
            
        except Exception as e:
            offline_logger.error(f"❌ ERROR CHECKING AVAILABILITY: {e}")
            offline_logger.error(f"❌ TRACEBACK: {traceback.format_exc()}")
            return False

    def generate_mcq(self, topic: str, context: str = "", num_questions: int = 1, difficulty: str = "medium", game_mode: str = "casual", question_type: str = "mixed") -> List[Dict[str, Any]]:
        """
        Generate MCQ questions using optimized BATCH generation with ultra-comprehensive logging
        """
        # 🎯 COMPREHENSIVE OFFLINE GENERATION LOGGING - START
        performance_logger.info("="*80)
        performance_logger.info("🤖 OFFLINE MCQ GENERATION SESSION STARTED")
        performance_logger.info("="*80)
        
        performance_logger.info(f"🚀 STARTING OFFLINE MCQ GENERATION")
        performance_logger.info(f"   • TOPIC: '{topic}'")
        performance_logger.info(f"   • CONTEXT_LENGTH: {len(context)}")
        performance_logger.info(f"   • NUM_QUESTIONS: {num_questions}")
        performance_logger.info(f"   • DIFFICULTY: '{difficulty}'")
        performance_logger.info(f"   • QUESTION_TYPE: '{question_type}'")
        
        # 🚀 PERFORMANCE FIX: Use optimized single-model approach for expert mode
        if difficulty.lower() == "expert":
            offline_logger.info("🎓 EXPERT MODE DETECTED - Using optimized single-model approach for speed")
            return self._generate_expert_questions_optimized(topic, context, num_questions, question_type)
        
        # 🔥 CHECK FOR HARD MODE - Use Enhanced Graduate-Level Generation
        if difficulty.lower() == "hard":
            offline_logger.info("🔥 HARD MODE DETECTED - Using Graduate-Level Enhanced Generation")
            return self._generate_hard_questions_batch(topic, context, num_questions, question_type)
        
        # 🎯 LOG OLLAMA CONFIGURATION AND MODEL DETAILS
        performance_logger.info("🔍 OLLAMA CONFIGURATION CHECK:")
        performance_logger.info(f"   • CONFIGURED_MODEL: '{self.model_name}'")
        performance_logger.info(f"   • ENGINE_INITIALIZED: {self.is_engine_initialized}")
        performance_logger.info(f"   • OLLAMA_INTERFACE_AVAILABLE: {self.ollama_interface is not None}")
        
        if self.ollama_interface:
            try:
                # Get detailed Ollama status
                is_available = self.ollama_interface.is_available()
                performance_logger.info(f"   • OLLAMA_SERVER_RESPONDING: {is_available}")
                
                active_model = getattr(self.ollama_interface, 'active_model', None)
                performance_logger.info(f"   • ACTIVE_MODEL: '{active_model}'")
                
                # Try to get performance stats if available
                try:
                    stats = self.ollama_interface.get_performance_stats()
                    performance_logger.info(f"   • GPU_OPTIMIZED: {stats.get('gpu_optimized', 'unknown')}")
                    performance_logger.info(f"   • PERFORMANCE_STATS: {stats}")
                except Exception as e:
                    performance_logger.warning(f"   • PERFORMANCE_STATS: unavailable ({e})")
                    
            except Exception as e:
                performance_logger.error(f"   • OLLAMA_STATUS_CHECK_FAILED: {e}")
        else:
            performance_logger.error("   • OLLAMA_INTERFACE: NOT INITIALIZED")
        
        start_time = time.time()
        
        if not self.is_available():
            offline_logger.warning("⚠️ ENGINE NOT INITIALIZED. Attempting to initialize now...")
            offline_logger.info("🔄 ATTEMPTING INITIALIZATION...")
            
            performance_logger.warning("⚠️ OFFLINE ENGINE NOT READY - ATTEMPTING INITIALIZATION...")
            
            init_start = time.time()
            if not self.initialize():
                init_time = time.time() - init_start
                offline_logger.error("❌ FAILED TO INITIALIZE ENGINE for MCQ generation")
                performance_logger.error(f"❌ INITIALIZATION FAILED after {init_time:.3f}s")
                performance_logger.error("="*80)
                return []
            else:
                init_time = time.time() - init_start
                offline_logger.info(f"✅ ENGINE INITIALIZED SUCCESSFULLY in {init_time:.3f}s")
                performance_logger.info(f"✅ ENGINE INITIALIZED SUCCESSFULLY in {init_time:.3f}s")

        try:
            offline_logger.info(f"🚀 BATCH GENERATING {num_questions} MCQ(s) about '{topic}' in ONE API call - TURBO MODE!")
            performance_logger.info(f"🚀 BATCH GENERATION MODE: {num_questions} questions in single API call")
            
            # Create optimized BATCH prompt for ALL questions at once
            offline_logger.info("🔧 CREATING BATCH GENERATION PROMPT...")
            batch_prompt = self._create_batch_generation_prompt(topic, context, num_questions, question_type)
            offline_logger.info(f"🔧 BATCH PROMPT LENGTH: {len(batch_prompt)} characters")
            offline_logger.info(f"🔧 BATCH PROMPT PREVIEW: {batch_prompt[:200]}...")
            
            performance_logger.info("🔧 BATCH PROMPT CONFIGURATION:")
            performance_logger.info(f"   • PROMPT_LENGTH: {len(batch_prompt)} characters")
            performance_logger.info(f"   • TARGET_QUESTIONS: {num_questions}")
            performance_logger.info(f"   • TOPIC_FOCUS: '{topic}'")
            performance_logger.info(f"   • CONTEXT_INCLUDED: {'YES' if context else 'NO'}")
            
            # Generate ALL questions in a single API call with retry logic
            offline_logger.info(f"⚡ MAKING SINGLE API CALL for {num_questions} questions...")
            performance_logger.info(f"🔄 CALLING OLLAMA API: model='{self.model_name}'")
            
            api_start = time.time()
            
            # 🚀 ENHANCED: Use robust generation with retry logic
            raw_response = self._generate_with_retry(batch_prompt)
            
            api_time = time.time() - api_start
            offline_logger.info(f"🔧 API CALL COMPLETED in {api_time:.3f}s")
            offline_logger.info(f"🔧 RAW RESPONSE LENGTH: {len(raw_response) if raw_response else 0}")
            
            performance_logger.info(f"✅ OLLAMA API CALL COMPLETED in {api_time:.3f}s")
            performance_logger.info(f"   • RESPONSE_LENGTH: {len(raw_response) if raw_response else 0} characters")
            performance_logger.info(f"   • RESPONSE_RECEIVED: {'YES' if raw_response else 'NO'}")
            
            if raw_response:
                offline_logger.info(f"🔧 RAW RESPONSE PREVIEW: {raw_response[:300]}...")
                
                # Parse ALL questions from the single response
                offline_logger.info(f"📋 PARSING {num_questions} questions from batch response...")
                performance_logger.info(f"📋 PARSING BATCH RESPONSE: target={num_questions} questions")
                
                parse_start = time.time()
                
                # 🚀 ENHANCED: Use robust parsing
                parsed_questions = self._parse_batch_mcq_response_robust(raw_response, num_questions, topic)
                
                parse_time = time.time() - parse_start
                offline_logger.info(f"📋 PARSING COMPLETED in {parse_time:.3f}s")
                performance_logger.info(f"📋 PARSING COMPLETED in {parse_time:.3f}s")
                
                if parsed_questions and len(parsed_questions) > 0:
                    total_time = time.time() - start_time
                    success_count = len(parsed_questions)
                    avg_time_per_question = total_time / success_count
                    
                    # Update performance stats
                    self.generation_stats["total_generated"] += success_count
                    self.generation_stats["avg_time"] = avg_time_per_question
                    
                    offline_logger.info(f"🏁 BATCH SUCCESS: Generated {success_count}/{num_questions} questions in {total_time:.1f}s")
                    performance_logger.info(f"🏁 BATCH GENERATION SUCCESS")
                    performance_logger.info(f"   • TOTAL_TIME: {total_time:.3f}s")
                    performance_logger.info(f"   • SUCCESS_COUNT: {success_count}")
                    performance_logger.info(f"   • AVG_TIME_PER_QUESTION: {avg_time_per_question:.3f}s")
                    performance_logger.info(f"   • API_TIME: {api_time:.3f}s")
                    performance_logger.info(f"   • PARSE_TIME: {parse_time:.3f}s")
                    performance_logger.info("="*80)
                    performance_logger.info("🤖 OFFLINE MCQ GENERATION SESSION COMPLETED")
                    performance_logger.info("="*80)
                    
                    offline_logger.info(f"⚡ SPEEDUP: {(num_questions * 6.0) / total_time:.1f}x faster than sequential generation!")
                    
                    return parsed_questions
                else:
                    offline_logger.warning(f"⚠️ BATCH PARSING FAILED - falling back to single question mode")
                    performance_logger.warning(f"⚠️ BATCH PARSING FAILED - attempting fallback")
                    # Fallback to single question generation if batch fails
                    fallback_result = self._generate_single_question_fallback(topic, context)
                    performance_logger.info("="*80)
                    return fallback_result
            else:
                offline_logger.error("❌ NO RESPONSE from batch generation")
                offline_logger.error("   • Check Ollama server status")
                offline_logger.error("   • Verify model is loaded")
                offline_logger.error("   • Check prompt format")
                performance_logger.error("❌ NO RESPONSE from Ollama API")
                performance_logger.error("="*80)
                return []

        except Exception as e:
            total_time = time.time() - start_time
            offline_logger.error(f"❌ BATCH MCQ GENERATION FAILED: {e}")
            offline_logger.error(f"❌ FULL TRACEBACK: {traceback.format_exc()}")
            performance_logger.error(f"❌ GENERATION FAILED after {total_time:.3f}s: {e}")
            performance_logger.error("="*80)
            # Fallback to single question if batch completely fails
            return self._generate_single_question_fallback(topic, context)

    def _create_batch_generation_prompt(self, topic: str, context: str, num_questions: int, question_type: str = "mixed") -> str:
        """Create optimized prompt for generating ALL questions in one batch"""
        
        safe_topic = self._make_topic_educational(topic)
        topic_constraints = self._get_topic_specific_constraints(safe_topic)
        
        # Question type specific instructions
        type_instruction = ""
        if question_type.lower() == "numerical":
            type_instruction = """
🔢🚨🔢 NUMERICAL QUESTION - ZERO TOLERANCE ENFORCEMENT 🔢🚨🔢

**ABSOLUTE NUMERICAL PURITY REQUIREMENTS:**
🎯 MANDATORY STARTER: MUST begin with "Calculate", "Compute", "Solve", "Determine", "Find", "Evaluate"
🎯 MATHEMATICS FOCUS: Equations, formulas, specific values, numerical operations
🎯 CALCULATION REQUIRED: Must involve mathematical computation, NOT just recall
🎯 NUMERICAL OPTIONS: ALL 4 options = numbers with units (J, kg, m/s, etc.)
🎯 SOLUTION = COMPUTATION: Answered through calculation, NOT memorization

🚫 CONCEPTUAL CONTAMINATION - AUTOMATIC FAILURE:
❌ "explain" ❌ "why" ❌ "how" ❌ "describe" ❌ "analyze" ❌ "discuss"
❌ Text-based options ❌ Conceptual explanations ❌ Definition-based answers
❌ NO explanatory content, NO theoretical discussions, NO conceptual elements

**ZERO TOLERANCE VERIFICATION:**
🔍 CALCULATION VERB? → Must be "Calculate/Compute/Solve/Determine/Find/Evaluate"
🔍 NUMBERS PRESENT? → Must include specific numerical values and units
🔍 NUMERICAL OPTIONS? → ALL options must be numbers with units
🔍 COMPUTATION REQUIRED? → Must test mathematical skills, NOT conceptual knowledge
🔍 NO CONCEPTUAL WORDS? → Zero explanation verbs or theoretical discussions

💀 FAILURE MODES TO AVOID: Any question asking "why", any options with explanations, any theoretical discussion

🚨 WARNING: Any question that is not purely numerical will be AUTOMATICALLY REJECTED!
🚨 DEMAND: Generate ONLY calculation questions with numerical answers!
"""
        elif question_type.lower() == "conceptual":
            type_instruction = """
🧠🚨🧠 CONCEPTUAL QUESTION - ZERO TOLERANCE ENFORCEMENT 🧠🚨🧠

**ABSOLUTE CONCEPTUAL PURITY REQUIREMENTS:**
🎯 MANDATORY STARTER: MUST begin with "Explain", "Why", "How", "What happens", "Describe", "Analyze"  
🎯 PRINCIPLES FOCUS: Theories, mechanisms, cause-effect relationships, underlying principles
🎯 ZERO MATH: NO calculations, NO specific numerical values, NO mathematical operations
🎯 PURE CONCEPTUAL OPTIONS: ALL 4 options = concept descriptions, mechanism explanations
🎯 SOLUTION = UNDERSTANDING: Answered through theoretical knowledge, NOT computation
🎯 QUALITATIVE LANGUAGE: Relationships, trends, phenomena without numerical specifics

🚫 NUMERICAL CONTAMINATION - AUTOMATIC FAILURE:
❌ "calculate" ❌ "compute" ❌ "solve" ❌ "determine" ❌ "find" ❌ "evaluate"
❌ Numbers with units ❌ Mathematical expressions ❌ Formulas ❌ Equations ❌ Calculations
❌ NO numerical operations, NO specific values, NO computational elements

**ZERO TOLERANCE VERIFICATION:**
🔍 UNDERSTANDING VERB? → Must be "Explain/Why/How/What happens/Describe/Analyze"
🔍 NO NUMBERS? → Zero specific numerical values or calculations required
🔍 PURE CONCEPTUAL OPTIONS? → ALL options describe concepts/mechanisms, NO numbers
🔍 UNDERSTANDING REQUIRED? → Must test theoretical knowledge, NOT math skills
🔍 NO NUMERICAL WORDS? → Zero calculation verbs or mathematical operations

💀 FAILURE MODES TO AVOID: Any question asking to "calculate", any numerical options, any mathematical operations

🚨 WARNING: Any question that includes calculations will be AUTOMATICALLY REJECTED!
🚨 DEMAND: Generate ONLY understanding/explanation questions!
"""
        
        # Different question focus areas for variety in batch
        if question_type.lower() == "numerical":
            question_focuses = [
                "mathematical calculations and problem-solving",
                "quantitative analysis and computations", 
                "numerical problem-solving with formulas",
                "calculation-based applications",
                "mathematical operations and evaluations"
            ]
        elif question_type.lower() == "conceptual":
            question_focuses = [
                "theoretical understanding and principles",
                "conceptual explanations and mechanisms", 
                "qualitative analysis and reasoning",
                "cause-effect relationships and theories",
                "understanding of underlying concepts"
            ]
        else:
            question_focuses = [
                "foundational concepts and definitions",
                "practical applications and examples", 
                "analysis and comparison of concepts",
                "problem-solving and calculations",
                "conceptual understanding and principles"
            ]
        
        # Domain-specific requirements
        domain_requirements = ""
        topic_lower = safe_topic.lower()
        if any(term in topic_lower for term in ["physics", "mechanics", "quantum", "electromagnetic"]):
            domain_requirements = "Must include physics terms like: force, energy, momentum, wave, particle, field, quantum"
        elif any(term in topic_lower for term in ["chemistry", "chemical", "organic", "inorganic"]):
            domain_requirements = "Must include chemistry terms like: molecule, atom, bond, reaction, compound, solution, acid"
        elif any(term in topic_lower for term in ["mathematics", "math", "calculus", "algebra"]):
            domain_requirements = "Must include math terms like: equation, function, derivative, integral, matrix, variable, theorem"

        # Length requirements
        min_length = 120 if difficulty.lower() == "expert" else 80 if difficulty.lower() in ["hard", "medium"] else 50

        # Create focused prompt for batch generation
        prompt = f"""You are an educational content expert specializing in {safe_topic}. Generate {num_questions} diverse {question_type} multiple choice questions STRICTLY about {safe_topic}.

MANDATORY REQUIREMENTS:
- Each question must be at least {min_length} characters long
- Each question MUST end with a question mark (?)
- {domain_requirements}
- All options must be substantive and non-empty
- Expert questions must demonstrate advanced understanding

{type_instruction}

CRITICAL BATCH REQUIREMENTS:
- Generate EXACTLY {num_questions} complete questions
- Each question must focus ONLY on {safe_topic}
- NO mixing with other academic fields (brain anatomy, ecology, computer science, etc.)
- Questions should cover: {', '.join(question_focuses[:num_questions])}
- ALL answer options must relate directly to {safe_topic}

{topic_constraints}

RESPONSE FORMAT - Return EXACTLY {num_questions} questions as a JSON array:
[
  {{
    "question": "Question 1 about {safe_topic}?",
    "options": {{
      "A": "Correct answer related to {safe_topic}",
      "B": "Plausible but incorrect option in {safe_topic}",
      "C": "Another plausible incorrect option in {safe_topic}",
      "D": "Common misconception in {safe_topic}"
    }},
    "correct": "A",
    "explanation": "Brief explanation focusing on {safe_topic} principles"
  }},
  {{
    "question": "Question 2 about {safe_topic}?",
    "options": {{
      "A": "Option A for question 2",
      "B": "Option B for question 2", 
      "C": "Option C for question 2",
      "D": "Option D for question 2"
    }},
    "correct": "B",
    "explanation": "Explanation for question 2"
  }}
  {"," if num_questions > 2 else ""}
  {f"... (continue for all {num_questions} questions)" if num_questions > 2 else ""}
]

Generate EXACTLY {num_questions} complete questions following this format:"""

        return prompt

    def _parse_batch_mcq_response(self, response: str, expected_questions: int, topic: str) -> List[Dict[str, Any]]:
        """Parse multiple MCQ questions from a single batch response"""
        try:
            logger.info(f"🔍 Parsing batch response for {expected_questions} questions...")
            
            # Try to extract JSON array from response
            import re
            
            # Find JSON array in the response
            json_match = re.search(r'\[(.*?)\]', response, re.DOTALL)
            if json_match:
                json_content = '[' + json_match.group(1) + ']'
                try:
                    questions_data = json.loads(json_content)
                    
                    if isinstance(questions_data, list):
                        parsed_questions = []
                        for i, q_data in enumerate(questions_data[:expected_questions]):  # Limit to expected number
                            if self._validate_question_structure(q_data):
                                parsed_questions.append(q_data)
                                logger.info(f"✅ Parsed question {i+1}: {q_data.get('question', '')[:50]}...")
                            else:
                                logger.warning(f"⚠️ Question {i+1} failed validation, skipping")
                        
                        logger.info(f"📋 Successfully parsed {len(parsed_questions)}/{expected_questions} questions from batch")
                        return parsed_questions
                    else:
                        logger.warning("⚠️ Response is not a JSON array")
                except json.JSONDecodeError as e:
                    logger.warning(f"⚠️ JSON parsing failed: {e}")
            
            # Fallback: Try to parse individual questions if array parsing fails
            logger.info("🔄 Trying fallback: parsing individual questions from response...")
            return self._parse_individual_questions_from_text(response, expected_questions, topic)
            
        except Exception as e:
            logger.error(f"❌ Batch parsing error: {e}")
            return []

    def _parse_individual_questions_from_text(self, text: str, expected_questions: int, topic: str) -> List[Dict[str, Any]]:
        """Fallback parser to extract individual questions from text response"""
        try:
            questions = []
            
            # Split by common delimiters that might separate questions
            potential_questions = re.split(r'(?:\n\s*\n|\}\s*,?\s*\{)', text)
            
            for i, potential_q in enumerate(potential_questions):
                if i >= expected_questions:
                    break
                    
                # Try to extract a complete question JSON from this segment
                json_match = re.search(r'\{.*?\}', potential_q, re.DOTALL)
                if json_match:
                    try:
                        question_json = json_match.group(0)
                        question_data = json.loads(question_json)
                        
                        if self._validate_question_structure(question_data):
                            questions.append(question_data)
                            logger.info(f"✅ Extracted question {len(questions)}: {question_data.get('question', '')[:50]}...")
                            
                    except json.JSONDecodeError:
                        continue
            
            logger.info(f"📋 Fallback parsing extracted {len(questions)} questions")
            return questions
            
        except Exception as e:
            logger.error(f"❌ Fallback parsing error: {e}")
            return []

    def _validate_question_structure(self, question_data: Dict[str, Any]) -> bool:
        """Validate that a question has the required structure"""
        try:
            required_keys = ['question', 'options', 'correct', 'explanation']
            
            # Check all required keys exist
            for key in required_keys:
                if key not in question_data:
                    logger.warning(f"⚠️ Missing required key: {key}")
                    return False
            
            # Validate options structure
            options = question_data['options']
            if not isinstance(options, dict):
                logger.warning("⚠️ Options is not a dictionary")
                return False
                
            # Check for required option keys (A, B, C, D)
            required_options = ['A', 'B', 'C', 'D']
            for opt in required_options:
                if opt not in options or not options[opt].strip():
                    logger.warning(f"⚠️ Missing or empty option: {opt}")
                    return False
            
            # Validate correct answer
            correct = question_data['correct']
            if correct not in options:
                logger.warning(f"⚠️ Correct answer '{correct}' not in options")
                return False
            
            # Basic content validation
            question_text = question_data['question']
            if len(question_text.strip()) < 10:
                logger.warning("⚠️ Question too short")
                return False
                
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ Question validation error: {e}")
            return False



    def _create_speed_optimized_prompt(self, topic: str, context: str, question_index: int) -> str:
        """Create an optimized prompt for faster generation"""
        
        # Make topic more educational and acceptable
        safe_topic = self._make_topic_educational(topic)
        
        # Add variety to avoid repetitive generation
        question_types = [
            "factual knowledge",
            "conceptual understanding", 
            "practical application",
            "analysis and comparison",
            "problem-solving"
        ]
        
        focus = question_types[question_index % len(question_types)]
        
        # Create topic-specific constraints to prevent mixing
        topic_constraints = self._get_topic_specific_constraints(safe_topic)
        
        # AGGRESSIVE anti-vague question enforcement
        anti_vague_section = """
🚨 ANTI-VAGUE QUESTION ENFORCEMENT:
❌ COMPLETELY FORBIDDEN: "What is the primary function of..."
❌ COMPLETELY FORBIDDEN: "What is the main purpose of..."
❌ COMPLETELY FORBIDDEN: "What does X do?"
❌ COMPLETELY FORBIDDEN: Basic definition questions
❌ COMPLETELY FORBIDDEN: General overview questions
✅ REQUIRED: Specific mechanisms, pathways, processes
✅ REQUIRED: Detailed analysis and reasoning
✅ REQUIRED: Expert-level specificity
✅ REQUIRED: Questions requiring deep understanding

BIOLOGY/REPRODUCTIVE SYSTEM SPECIAL RULES:
If topic relates to reproduction/reproductive systems:
- Ask about SPECIFIC hormonal pathways (FSH, LH, testosterone, estrogen regulation)
- Focus on MOLECULAR mechanisms (meiosis stages, fertilization processes)
- Include BIOCHEMICAL processes (steroidogenesis, gametogenesis)
- Test understanding of REGULATORY feedback loops
- Ask about SPECIFIC anatomical structures and their precise functions

EXAMPLES OF BANNED vs REQUIRED QUESTIONS:
❌ BANNED: "What is the primary function of the male reproductive system?"
✅ REQUIRED: "During which phase of spermatogenesis do primary spermatocytes undergo the first meiotic division?"

❌ BANNED: "What is the main purpose of the female reproductive system?"
✅ REQUIRED: "Which hormone surge triggers ovulation and what cellular mechanism initiates this process?"
"""
        
        prompt = f"""You are an educational content creator specializing in {safe_topic}. Create a {focus} multiple choice question STRICTLY about {safe_topic}.

{anti_vague_section}

STRICT REQUIREMENTS:
- Question must focus ONLY on {safe_topic}
- All answer options must relate directly to {safe_topic}
- DO NOT mix with other academic fields like neuroscience, ecology, computer science, etc.
- DO NOT include brain anatomy, biodiversity, or unrelated medical topics
- Stay within the bounds of {safe_topic} exclusively
- AVOID VAGUE QUESTIONS - be specific and detailed

{topic_constraints}

Topic: {safe_topic}
Approach: {focus}

Create a focused multiple choice question with exactly 4 options labeled A, B, C, D.
One option must be clearly correct.
Include a brief explanation.

QUALITY REQUIREMENTS:
- Questions must be specific and detailed, not vague
- Use precise terminology and concepts
- Focus on mechanisms and processes, not just basic definitions
- Include challenging but fair distractors
- Test understanding of HOW and WHY, not just WHAT

Example structure for {safe_topic}:
- Question about specific concepts, mechanisms, or processes in {safe_topic}
- All options must be within {safe_topic} domain
- Avoid mixing with other subjects
- Focus on detailed understanding rather than surface-level facts

Return ONLY a JSON object in this exact format:
{{
  "question": "What specific mechanism/process in {safe_topic} explains [detailed scenario]?",
  "options": {{
    "A": "Specific, detailed concept relevant to {safe_topic}",
    "B": "Another specific, detailed concept relevant to {safe_topic}",
    "C": "Related but incorrect specific concept in {safe_topic}", 
    "D": "Common misconception with specific details in {safe_topic}"
  }},
  "correct": "A",
  "explanation": "Detailed explanation focusing on specific {safe_topic} principles and mechanisms"
}}"""

        return prompt

    def _get_topic_specific_constraints(self, safe_topic: str) -> str:
        """Get specific constraints for different topics to prevent mixing"""
        
        constraints_map = {
            'human biology and reproductive health education': """
TOPIC CONSTRAINTS:
- Focus only on reproductive anatomy, physiology, and health
- Include concepts like hormones, reproductive cycles, contraception
- DO NOT mention brain anatomy, neural networks, or neuroscience
- DO NOT include ecology, biodiversity, or environmental topics
- Stay within reproductive health domain exclusively
""",
            'biological sciences': """
TOPIC CONSTRAINTS:
- Focus on cell biology, genetics, evolution, ecology
- Include concepts like DNA, proteins, cellular processes
- DO NOT mix with computer science or programming
- DO NOT include physics concepts like quantum mechanics
- Stay within biological science principles
""",
            'health and wellness education': """
TOPIC CONSTRAINTS:
- Focus on nutrition, exercise, mental health, disease prevention
- Include concepts like healthy lifestyle, medical screening
- DO NOT mix with unrelated medical specialties
- Stay within general health and wellness scope
"""
        }
        
        # Get specific constraints or generic ones
        return constraints_map.get(safe_topic, f"""
TOPIC CONSTRAINTS:
- Focus exclusively on concepts within {safe_topic}
- DO NOT mix with unrelated academic fields
- All options must be relevant to {safe_topic}
- Avoid generic or overly broad questions
""")

    def _make_topic_educational(self, topic: str) -> str:
        """Convert any topic into an educational, appropriate format"""
        topic_lower = topic.lower().strip()
        
        # Educational mappings for sensitive topics
        educational_mappings = {
            'sex': 'human biology and reproductive health education',
            'sexual': 'biology and health education',
            'reproduction': 'biological reproduction processes',
            'anatomy': 'human anatomy and physiology',
            'biology': 'biological sciences',
            'health': 'health and wellness education',
            'science': 'scientific principles and concepts',
            'education': 'educational theory and practice',
            'learning': 'learning processes and methods'
        }
        
        # Check for exact matches first
        if topic_lower in educational_mappings:
            return educational_mappings[topic_lower]
        
        # Check for partial matches
        for key, value in educational_mappings.items():
            if key in topic_lower:
                return value
        
        # If no special mapping needed, make it educational
        if len(topic.strip()) > 0:
            return f"the academic study of {topic}"
        else:
            return "general knowledge and concepts"

    def _create_fallback_prompt(self, topic: str) -> str:
        """Create a simpler fallback prompt for retry attempts"""
        safe_topic = self._make_topic_educational(topic)
        
        return f"""Educational question generator.

Create a simple multiple choice question about {safe_topic}.

The question must be educational and appropriate.

Return exactly this JSON format:
{{
  "question": "What is a key aspect of {safe_topic}?",
  "options": {{
    "A": "Important foundational concept",
    "B": "Secondary supporting idea", 
    "C": "Unrelated information",
    "D": "Incorrect assumption"
  }},
  "correct": "A",
  "explanation": "Brief educational explanation"
}}

Generate only the JSON:"""

    def _parse_mcq_response(self, response: str, attempt: int = 1) -> Optional[Dict[str, Any]]:
        """
        Parse MCQ response with enhanced error handling and multiple strategies
        """
        try:
            # Use the enhanced MCQ parser from utils
            from knowledge_app.utils.enhanced_mcq_parser import EnhancedMCQParser
            
            parser = EnhancedMCQParser()
            parsed_mcq = parser.parse_mcq(response)
            
            if parsed_mcq:
                logger.info(f"✅ Successfully parsed MCQ on attempt {attempt}")
                return parsed_mcq
            else:
                logger.warning(f"⚠️ Parsing failed on attempt {attempt}")
                return None
                
        except Exception as e:
            logger.error(f"❌ MCQ parsing error on attempt {attempt}: {e}")
            return None

    async def generate_quiz_async(self, context: str, topic: str, difficulty: str = "medium") -> Dict[str, Any]:
        """
        Generate a single MCQ question async - FIXED METHOD SIGNATURE
        """
        try:
            logger.info(f"🚀 Generating single MCQ for topic: '{topic}', difficulty: {difficulty}")
            
            # Generate single question using sync method
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(None, self.generate_mcq, topic, context, 1)
            
            if results and len(results) > 0:
                mcq = results[0]
                # Convert to expected format
                return {
                    "question": mcq.get("question", ""),
                    "options": mcq.get("options", {}),
                    "correct": mcq.get("correct", "A"),
                    "explanation": mcq.get("explanation", "")
                }
            else:
                logger.error("❌ No MCQ generated")
                return None
                
        except Exception as e:
            logger.error(f"❌ Async MCQ generation failed: {e}")
            return None

    async def generate_mcq_async(self, topic: str, context: str = "", num_questions: int = 1, 
                                 difficulty: str = "medium", game_mode: str = "casual", 
                                 question_type: str = "mixed") -> List[Dict[str, Any]]:
        """
        Async version of MCQ generation for better performance - FIXED SIGNATURE
        """
        offline_logger.info(f"🔍 ASYNC DEBUG: generate_mcq_async called with difficulty='{difficulty}', question_type='{question_type}'")
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generate_mcq, topic, context, num_questions, difficulty, game_mode, question_type)

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        stats = {
            "engine_initialized": self.is_engine_initialized,
            "available": self.is_available(),
            "model_name": self.model_name,
            "generation_stats": self.generation_stats
        }
        
        if self.ollama_interface:
            stats.update(self.ollama_interface.get_performance_stats())
            
        return stats

    def optimize_for_batch_generation(self):
        """Apply optimizations specifically for batch generation"""
        if self.ollama_interface:
            logger.info("🚀 Applying batch generation optimizations...")
            
            # Further optimize for batch processing
            self.ollama_interface.generation_params.update({
                'temperature': 0.85,  # Slightly higher for variety in batch
                'top_k': 25,  # More restrictive for faster batch processing
                'num_predict': 350,  # Optimized length for MCQs
                'batch_size': 1024,  # Large batch size for GPU efficiency
                'parallel': 8,  # Maximum parallel processing
            })
            
            logger.info("⚡ Batch optimization complete - Ready for high-speed generation!")

    def cleanup(self):
        """Clean up resources"""
        if self.ollama_interface:
            logger.info("🧹 Cleaning up Ollama interface")
            self.ollama_interface = None
        self.is_engine_initialized = False

    def _generate_hard_questions_batch(self, topic: str, context: str, num_questions: int, question_type: str) -> List[Dict[str, Any]]:
        """🔥 HARD MODE: Generate graduate-level complex questions using BatchTwoModelPipeline"""
        
        offline_logger.info(f"🔥 STARTING HARD MODE BATCH GENERATION: {num_questions} questions about '{topic}'")
        
        # Prepare test cases for hard mode generation
        test_cases = []
        domain = self._extract_domain_from_topic(topic)
        
        for i in range(num_questions):
            test_cases.append({
                'domain': domain,
                'topic': topic,
                'context': context,
                'difficulty': 'hard',
                'question_index': i
            })
        
        try:
            # Step 1: Generate graduate-level thinking
            offline_logger.info("🧠 Step 1/3: Generating graduate-level thinking...")
            thinking_response = self._batch_generate_hard_thinking(test_cases, topic, question_type)
            
            if not thinking_response:
                offline_logger.error("❌ Hard mode thinking generation failed")
                return []
            
            # Step 2: Extract individual thinking blocks
            offline_logger.info("📋 Step 2/3: Extracting individual thinking blocks...")
            thinking_list = self._extract_individual_thinking(thinking_response, test_cases)
            
            if not thinking_list or len(thinking_list) < num_questions:
                offline_logger.warning(f"⚠️ Only got {len(thinking_list)}/{num_questions} thinking blocks")
                # Pad with the last available thinking block
                while len(thinking_list) < num_questions:
                    thinking_list.append(thinking_list[-1] if thinking_list else "Graduate-level analysis required")
            
            # Step 3: Generate JSON for each question
            offline_logger.info("📄 Step 3/3: Generating JSON for each question...")
            json_results = self._batch_generate_hard_json(thinking_list, test_cases)
            
            # Filter out None results
            valid_questions = [q for q in json_results if q is not None]
            
            offline_logger.info(f"🏁 HARD MODE COMPLETED: {len(valid_questions)}/{num_questions} questions generated")
            
            if valid_questions:
                return valid_questions
            else:
                offline_logger.error("❌ No valid hard mode questions generated")
                return []
                
        except Exception as e:
            offline_logger.error(f"❌ Hard mode batch generation failed: {e}")
            offline_logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return []

    def _batch_generate_hard_thinking(self, test_cases: List[Dict], topic: str, question_type: str = "mixed") -> Optional[str]:
        """Step 1: Generate graduate-level thinking for hard mode questions"""
        
        # Question type specific instructions for hard mode
        type_instruction = ""
        if question_type.lower() == "numerical":
            type_instruction = """
🔢🚨🔢 HARD MODE NUMERICAL REQUIREMENTS 🔢🚨🔢

**GRADUATE-LEVEL NUMERICAL PURITY:**
🎯 MANDATORY CALCULATION VERBS: "Calculate", "Compute", "Solve", "Determine", "Find", "Evaluate"
🎯 ADVANCED MATHEMATICS: Complex equations, multi-step derivations, sophisticated analysis
🎯 QUANTITATIVE RIGOR: Graduate-level formulas, numerical methods, computational techniques
🎯 NUMERICAL OPTIONS: ALL 4 options = advanced numerical values with proper units
🎯 CALCULATION COMPLEXITY: Multi-step problem solving requiring expert mathematical skills

🚫 CONCEPTUAL CONTAMINATION - ZERO TOLERANCE:
❌ "explain" ❌ "why" ❌ "how" ❌ "describe" ❌ "analyze" ❌ "discuss"
❌ Text-based options ❌ Conceptual explanations ❌ Theoretical discussions
❌ NO qualitative reasoning, NO descriptive content

**HARD MODE NUMERICAL EXAMPLES:**
✅ "Calculate the relativistic momentum of an electron moving at 0.8c"
✅ "Determine the magnetic field strength at the center of a solenoid with 500 turns/cm carrying 2.5A"
✅ "Solve for the equilibrium constant Kc at 298K given ΔG° = -25.6 kJ/mol"
"""
        elif question_type.lower() == "conceptual":
            type_instruction = """
🧠🚨🧠 HARD MODE CONCEPTUAL REQUIREMENTS 🧠🚨🧠

**GRADUATE-LEVEL CONCEPTUAL PURITY:**
🎯 MANDATORY UNDERSTANDING VERBS: "Explain", "Why", "How", "What happens", "Describe", "Analyze"
🎯 THEORETICAL DEPTH: Advanced principles, complex mechanisms, sophisticated theories
🎯 QUALITATIVE REASONING: Deep understanding of cause-effect relationships
🎯 CONCEPTUAL OPTIONS: ALL 4 options = sophisticated theoretical explanations
🎯 ANALYTICAL COMPLEXITY: Multi-layered reasoning requiring expert theoretical knowledge

🚫 NUMERICAL CONTAMINATION - ZERO TOLERANCE:
❌ "calculate" ❌ "compute" ❌ "solve" ❌ "determine" ❌ "find" ❌ "evaluate"
❌ Numbers with units ❌ Mathematical expressions ❌ Equations ❌ Formulas
❌ NO computational elements, NO numerical analysis

**HARD MODE CONCEPTUAL EXAMPLES:**
✅ "Explain the quantum mechanical basis for the Pauli exclusion principle in multi-electron atoms"
✅ "Why does the entropy of the universe increase during spontaneous chemical reactions?"
✅ "How does electromagnetic induction relate to relativistic effects in moving conductors?"
"""
        else:
            type_instruction = """
🔀 HARD MODE MIXED REQUIREMENTS:
✅ Can include either advanced numerical OR sophisticated conceptual elements
✅ Vary between quantitative graduate-level analysis and theoretical understanding
✅ Mix complex calculations with deep theoretical reasoning
✅ Balance advanced mathematical skills with conceptual expertise
"""

        prompt_parts = [f"""Generate graduate-level multiple choice questions that require ADVANCED ANALYTICAL THINKING. Each question MUST be:

1. **GRADUATE COMPLEXITY** - Minimum 80+ characters, requiring advanced understanding
2. **MULTI-STEP REASONING** - Cannot be answered by simple recall or basic formulas
3. **ANALYTICAL DEPTH** - Test understanding of complex relationships and systems
4. **ADVANCED TERMINOLOGY** - Use technical vocabulary appropriately
5. **SYNTHESIS REQUIRED** - Test ability to combine multiple concepts

{type_instruction}

🔥 HARD MODE REQUIREMENTS - ABSOLUTELY FORBIDDEN:
❌ Basic formula applications (F=ma, E=mc², KE=½mv², etc.)
❌ Single-step textbook problems
❌ "What is..." definition questions
❌ Simple recall or memorization
❌ Undergraduate homework-level questions

✅ HARD MODE REQUIREMENTS - MANDATORY:
✅ Multi-step problem solving requiring 3+ concepts
✅ Advanced analytical techniques and methods
✅ Complex systems with multiple interacting components
✅ Graduate-level complexity (master's degree level)
✅ Synthesis of multiple principles
✅ Advanced applications requiring deep domain knowledge

Generate questions for these test cases with GRADUATE-LEVEL COMPLEXITY:

"""]
        
        for i, case in enumerate(test_cases, 1):
            domain = case.get('domain', 'General')
            topic_text = case.get('topic', 'General')
            context = case.get('context', '')
            
            complexity_instruction = f"""
**HARD MODE (GRADUATE-LEVEL) REQUIREMENTS for {question_type.upper()} questions:**
- Question must require master's degree-level knowledge
- Include advanced analytical techniques or complex theoretical concepts
- Test synthesis of multiple advanced concepts
- Require multi-step logical reasoning
- Use technical terminology appropriately for graduate level
- Question length: MINIMUM 80 characters
- Should challenge advanced undergraduate and graduate students
- Must avoid basic formula applications and single-step problems
- MUST follow {question_type.upper()} requirements above
"""
            
            prompt_parts.append(f"""
Test Case {i}: {domain} - {topic_text} (HARD difficulty - Graduate Level {question_type.upper()})
{complexity_instruction}

Think through:
- What ADVANCED {question_type} concept should be tested that requires graduate-level analysis?
- What complex relationship or system behavior is involved?
- What multi-step reasoning should be required?
- What sophisticated analysis techniques apply?
- How to make this challenging for graduate students?
- What advanced terminology and concepts must be included?

MAKE THIS QUESTION GRADUATE-LEVEL CHALLENGING AND ANALYTICAL FOR {question_type.upper()} TYPE!

""")
        
        prompt_parts.append(f"""
CRITICAL HARD MODE INSTRUCTIONS:
- HARD questions must be graduate-level complexity (master's degree)
- Use advanced analytical methods and complex reasoning
- Test deep understanding of systems and relationships
- Questions should require multiple steps of reasoning
- Include analysis of complex interactions where appropriate
- Make questions that would challenge graduate students
- MINIMUM 80 characters for hard questions
- Avoid basic formulas and single-step problems
- Focus on synthesis, analysis, and evaluation
- MUST STRICTLY FOLLOW {question_type.upper()} REQUIREMENTS

Generate sophisticated, analytically challenging {question_type} questions!""")
        
        full_prompt = "".join(prompt_parts)
        
        offline_logger.info(f"🧠 Graduate-level {question_type} thinking generation with {self.thinking_model}")
        response = self._generate_with_retry(full_prompt, model_override=self.thinking_model, max_tokens=12000)
        
        if response:
            offline_logger.info(f"✅ Graduate-level {question_type} thinking generated ({len(response)} chars)")
            return response
        else:
            offline_logger.error(f"❌ No graduate-level {question_type} thinking generated")
            return None

    def _batch_generate_hard_json(self, thinking_list: List[str], test_cases: List[Dict]) -> List[Optional[Dict]]:
        """Step 3: Generate JSON for each hard mode question with graduate-level requirements"""
        
        results = []
        
        for i, (thinking, test_case) in enumerate(zip(thinking_list, test_cases)):
            domain = test_case.get('domain', 'General')
            topic = test_case.get('topic', 'General') 
            
            complexity_reminder = """
HARD MODE (GRADUATE-LEVEL) JSON REQUIREMENTS:
- Question MUST be graduate-level complexity (minimum 80 characters)
- Include advanced analytical concepts or multi-step reasoning
- Test deep understanding requiring synthesis of concepts
- Use appropriate technical vocabulary for graduate level
- Options must be analytically sophisticated, not simple choices
- Avoid basic formulas and single-step problems
- QUESTION MUST END WITH A QUESTION MARK (?)
- Focus on analysis, synthesis, evaluation rather than recall
"""
            
            prompt = f"""Generate a valid JSON MCQ from this thinking:

THINKING: {thinking}

🔥 HARD MODE - GRADUATE LEVEL COMPLEXITY REQUIRED

🚨 CRITICAL JSON FORMAT - EXACT OUTPUT REQUIRED:
{{
  "question": "EXACTLY ONE graduate-level analytical question ending with ?",
  "options": {{
    "A": "First option",
    "B": "Second option", 
    "C": "Third option",
    "D": "Fourth option"
  }},
  "correct": "A",
  "explanation": "Brief explanation"
}}

{complexity_reminder}

🚨 MANDATORY RULES:
- EXACTLY 4 options in A,B,C,D format  
- Use "correct" not "correct_answer"
- Question must end with ?
- No extra commas or brackets
- No explanatory text outside JSON
- Generate ONLY the JSON object above
- Must require graduate-level analytical thinking"""
            
            offline_logger.info(f"📄 Hard mode JSON generation {i+1}/{len(test_cases)} with {self.json_model}")
            
            # Try up to 3 times for better reliability
            json_data = None
            for attempt in range(3):
                response = self._generate_with_chunked_buffer(prompt, model_override=self.json_model, max_tokens=2000)
                
                if response:
                    json_data = self._parse_json_response_robust(response)
                    if json_data:
                        break  # Success, exit retry loop
                    elif attempt < 2:  # Not the last attempt
                        offline_logger.warning(f"   ⚠️  Retry {attempt + 1}/3 for JSON {i+1}")
                
                if attempt == 2:  # Last attempt failed
                    offline_logger.error(f"   ❌ JSON {i+1} failed after 3 attempts")
            
            if json_data:
                # Fix question mark if missing
                question = json_data.get('question', '')
                if question and not question.endswith('?'):
                    json_data['question'] = question.rstrip('.!') + '?'
                
                # Add metadata
                json_data['difficulty'] = 'hard'
                json_data['domain'] = domain
                json_data['topic'] = topic
                json_data['generated_by'] = f"{self.thinking_model} + {self.json_model}"
                json_data['pipeline'] = "batch_two_model_hard"
                results.append(json_data)
                offline_logger.info(f"   ✅ JSON {i+1} generated")
            else:
                results.append(None)
                offline_logger.error(f"   ❌ JSON {i+1} no valid response")
        
        return results