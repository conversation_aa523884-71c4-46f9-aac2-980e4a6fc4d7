#!/usr/bin/env python3
"""
Force PhD-Level Generation - Aggressive prompt enhancement for truly advanced content
"""

import asyncio
import time
from datetime import datetime

# Ultra-specific PhD-level prompts that force advanced content
PHD_LEVEL_PROMPTS = {
    "quantum field theory": "Generate a question about the mathematical formalism of path integrals in quantum field theory, specifically addressing how <PERSON><PERSON><PERSON> diagrams represent perturbative expansions and their relationship to renormalization procedures in gauge theories.",
    
    "machine learning theory": "Create a question about the theoretical foundations of PAC learning, focusing on the relationship between VC dimension, sample complexity bounds, and generalization error in the context of statistical learning theory.",
    
    "organic synthesis": "Develop a question about stereoselective synthesis mechanisms, specifically addressing how chiral auxiliaries control enantioselectivity in asymmetric aldol reactions and the mechanistic rationale behind observed stereochemical outcomes.",
    
    "distributed systems": "Generate a question about Byzantine fault tolerance in distributed consensus protocols, focusing on the mathematical proofs underlying the impossibility results and how practical algorithms like PBFT circumvent these limitations.",
    
    "differential geometry": "Create a question about the curvature tensor in Riemannian manifolds, specifically addressing how the Riemann curvature tensor relates to parallel transport and the geometric interpretation of sectional curvature.",
    
    "immunology": "<PERSON><PERSON><PERSON> a question about T cell receptor signaling cascades, focusing on the molecular mechanisms of TCR clustering, lipid raft formation, and the kinetic proofreading model of T cell activation.",
    
    "computational complexity": "Generate a question about the polynomial hierarchy and oracle separations, specifically addressing how relativization results demonstrate the limitations of certain proof techniques in complexity theory.",
    
    "protein folding": "Create a question about the energy landscape theory of protein folding, focusing on the relationship between folding funnels, kinetic traps, and the speed-stability trade-off in protein evolution.",
    
    "cryptography": "Develop a question about zero-knowledge proofs and their applications to secure multi-party computation, focusing on the mathematical foundations of soundness, completeness, and zero-knowledge properties.",
    
    "neural networks": "Generate a question about the theoretical analysis of deep learning optimization, specifically addressing the loss landscape geometry, critical points, and the role of overparameterization in generalization."
}

async def test_forced_phd_generation(topic, specific_prompt, test_num, total_tests):
    """Test with ultra-specific PhD-level prompts"""
    print(f"\n🎓 FORCED PhD Test {test_num:2d}/{total_tests}: {topic}")
    print(f"   🎯 FORCING: {specific_prompt[:100]}...")
    
    try:
        from src.knowledge_app.core.unified_inference_manager import generate_mcq_unified
        
        start_time = time.time()
        
        # Use the specific PhD-level prompt as context to force advanced content
        result = generate_mcq_unified(
            topic=f"{topic} - {specific_prompt}",  # Inject PhD-level context
            difficulty="expert",
            question_type="mixed",
            timeout=120.0
        )
        
        duration = time.time() - start_time
        
        if not result:
            print(f"   ❌ GENERATION FAILED")
            return False
        
        question = result.get('question', '')
        options = result.get('options', [])
        explanation = result.get('explanation', '')
        
        print(f"   📝 QUESTION ({len(question)} chars):")
        print(f"      {question}")
        print(f"   📋 OPTIONS:")
        for i, opt in enumerate(options):
            print(f"      {chr(65 + i)}) {opt}")
        
        if explanation:
            print(f"   💡 EXPLANATION: {explanation[:200]}...")
        
        # Manual assessment of PhD-level complexity
        is_advanced = assess_phd_level_manually(question, options, topic)
        
        print(f"   ⏱️ TIME: {duration:.1f}s")
        
        if is_advanced:
            print(f"   ✅ SUCCESS - This is genuinely PhD-level!")
            return True
        else:
            print(f"   ❌ STILL TOO BASIC - Not PhD-level despite forcing")
            return False
        
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False

def assess_phd_level_manually(question, options, topic):
    """Manual assessment of whether content is truly PhD-level"""
    question_lower = question.lower()
    
    # Check for truly advanced indicators
    advanced_indicators = {
        'quantum_field': any(term in question_lower for term in [
            'feynman diagram', 'path integral', 'renormalization', 'gauge theory',
            'perturbative', 'lagrangian', 'hamiltonian', 'symmetry breaking'
        ]),
        
        'machine_learning': any(term in question_lower for term in [
            'pac learning', 'vc dimension', 'sample complexity', 'generalization bound',
            'statistical learning', 'empirical risk', 'rademacher complexity'
        ]),
        
        'organic_chemistry': any(term in question_lower for term in [
            'stereoselective', 'enantioselective', 'chiral auxiliary', 'asymmetric',
            'stereochemistry', 'mechanism', 'transition state', 'catalyst'
        ]),
        
        'distributed_systems': any(term in question_lower for term in [
            'byzantine fault', 'consensus protocol', 'impossibility result',
            'pbft', 'fault tolerance', 'distributed consensus', 'agreement'
        ]),
        
        'differential_geometry': any(term in question_lower for term in [
            'curvature tensor', 'riemannian manifold', 'parallel transport',
            'sectional curvature', 'riemann curvature', 'geodesic', 'metric tensor'
        ]),
        
        'general_advanced': any(term in question_lower for term in [
            'theorem', 'proof', 'mathematical', 'theoretical framework',
            'algorithm', 'optimization', 'complexity', 'analysis'
        ])
    }
    
    # Check if question contains advanced mathematical/technical language
    technical_depth = any([
        len([word for word in question_lower.split() if len(word) > 12]) >= 2,
        any(char.isdigit() for char in question),
        'research' in question_lower,
        'study' in question_lower,
        'analysis' in question_lower
    ])
    
    # Check options for technical depth
    options_text = ' '.join(str(opt) for opt in options).lower()
    options_advanced = any(term in options_text for term in [
        'mechanism', 'process', 'algorithm', 'method', 'approach',
        'theory', 'principle', 'framework', 'analysis', 'optimization'
    ])
    
    # Overall assessment
    has_advanced_terms = any(advanced_indicators.values())
    is_technically_deep = technical_depth and options_advanced
    is_substantial = len(question) > 100
    
    return has_advanced_terms and is_technically_deep and is_substantial

async def run_forced_phd_test():
    """Run forced PhD-level test with specific prompts"""
    print("🚀 FORCED PhD-LEVEL GENERATION TEST")
    print("=" * 60)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialize system
    print("\n🔧 Initializing system...")
    try:
        from src.knowledge_app.core.unified_inference_manager import initialize_unified_inference
        
        success = initialize_unified_inference({
            'timeout': 120.0,
            'mode': 'auto',
            'prefer_local': True
        })
        
        if not success:
            print("❌ System initialization failed")
            return False
        
        print("✅ System ready for forced PhD-level testing")
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return False
    
    # Test with forced PhD-level prompts
    topics = list(PHD_LEVEL_PROMPTS.keys())
    
    print(f"\n🎯 Testing {len(topics)} topics with FORCED PhD-level prompts...")
    
    start_time = time.time()
    success_count = 0
    
    for i, topic in enumerate(topics, 1):
        specific_prompt = PHD_LEVEL_PROMPTS[topic]
        success = await test_forced_phd_generation(topic, specific_prompt, i, len(topics))
        if success:
            success_count += 1
    
    duration = time.time() - start_time
    success_rate = (success_count / len(topics)) * 100
    
    # Results
    print("\n" + "=" * 60)
    print("🏆 FORCED PhD-LEVEL RESULTS")
    print("=" * 60)
    
    print(f"📊 Performance:")
    print(f"   PhD-Level Success: {success_count}/{len(topics)} ({success_rate:.1f}%)")
    print(f"   Duration: {duration:.1f} seconds")
    print(f"   Average per test: {duration/len(topics):.1f} seconds")
    
    if success_rate >= 80:
        print(f"\n🎉 EXCELLENT: {success_rate:.1f}% PhD-level success!")
        print("🚀 With proper prompting, the system CAN generate PhD-level content!")
        return True
    elif success_rate >= 60:
        print(f"\n👏 GOOD: {success_rate:.1f}% PhD-level success!")
        print("🔧 System has potential but needs prompt optimization")
        return True
    else:
        print(f"\n❌ POOR: {success_rate:.1f}% PhD-level success")
        print("🔧 System needs major improvements for PhD-level content")
        return False

async def main():
    """Main test runner"""
    try:
        success = await run_forced_phd_test()
        return 0 if success else 1
    except Exception as e:
        print(f"❌ Test crashed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
