#!/usr/bin/env python3
"""
🔥 Force Optimized Models for Expert Mode
Ensures the app uses the GPU-optimized models
"""

import sys
import os
import json
import requests
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def force_optimized_model_selection():
    """Force the DeepSeek pipeline to use optimized models"""
    print("🔥 FORCING OPTIMIZED MODEL SELECTION")
    print("=" * 50)
    
    try:
        # Check available models
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code != 200:
            print("❌ Ollama not running")
            return False
            
        models = response.json().get('models', [])
        model_names = [m['name'] for m in models]
        
        # Check if optimized models exist
        optimized_thinking = "deepseek-r1:14b-optimized"
        optimized_json = "llama3.1:8b-optimized"
        
        if optimized_thinking not in model_names:
            print(f"❌ {optimized_thinking} not found")
            return False
            
        if optimized_json not in model_names:
            print(f"❌ {optimized_json} not found")
            return False
            
        print(f"✅ Found optimized models:")
        print(f"   - {optimized_thinking}")
        print(f"   - {optimized_json}")
        
        # Update the model selection logic
        from knowledge_app.core.deepseek_integration import DeepSeekTwoModelPipeline
        
        # Monkey patch the model selection to force optimized models
        original_select_best_models = DeepSeekTwoModelPipeline._select_best_models
        
        def force_optimized_selection(self):
            """Force selection of optimized models"""
            self.thinking_model = optimized_thinking
            self.json_model = optimized_json
            print(f"🚀 FORCED: Using {self.thinking_model} for thinking")
            print(f"🚀 FORCED: Using {self.json_model} for JSON")
        
        DeepSeekTwoModelPipeline._select_best_models = force_optimized_selection
        
        print("✅ Model selection patched to use optimized models")
        return True
        
    except Exception as e:
        print(f"❌ Error forcing optimized models: {e}")
        return False

def test_forced_optimization():
    """Test that forced optimization works"""
    print("\n🧪 TESTING FORCED OPTIMIZATION")
    print("=" * 50)
    
    try:
        from knowledge_app.core.deepseek_integration import get_deepseek_pipeline
        
        # Get pipeline (should now use optimized models)
        pipeline = get_deepseek_pipeline()
        
        print(f"Pipeline thinking model: {pipeline.thinking_model}")
        print(f"Pipeline JSON model: {pipeline.json_model}")
        
        if "-optimized" in pipeline.thinking_model and "-optimized" in pipeline.json_model:
            print("✅ Pipeline is using optimized models")
            
            # Quick test
            print("🚀 Quick test generation...")
            import time
            start_time = time.time()
            
            result = pipeline.generate_expert_question(
                topic="physics",
                difficulty="expert"
            )
            
            end_time = time.time()
            generation_time = end_time - start_time
            
            if result:
                print(f"✅ Generated in {generation_time:.2f}s")
                if generation_time < 60:
                    print("🎉 SIGNIFICANT IMPROVEMENT!")
                return True
            else:
                print("❌ Generation failed")
                return False
        else:
            print("❌ Pipeline still using non-optimized models")
            return False
            
    except Exception as e:
        print(f"❌ Error testing: {e}")
        return False

def create_startup_script():
    """Create a startup script that applies optimizations"""
    print("\n📝 CREATING STARTUP OPTIMIZATION SCRIPT")
    print("=" * 50)
    
    startup_script = """#!/usr/bin/env python3
# Auto-generated startup optimization script
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def apply_expert_mode_optimizations():
    '''Apply all expert mode optimizations on startup'''
    try:
        # Set environment variables
        os.environ['OLLAMA_NUM_GPU'] = '50'
        os.environ['OLLAMA_GPU_LAYERS'] = '50'
        os.environ['OLLAMA_NUM_THREAD'] = '16'
        os.environ['OLLAMA_NUM_PARALLEL'] = '4'
        os.environ['OLLAMA_FLASH_ATTENTION'] = '1'
        
        # Force optimized model selection
        from knowledge_app.core.deepseek_integration import DeepSeekTwoModelPipeline
        
        def force_optimized_models(self):
            self.thinking_model = "deepseek-r1:14b-optimized"
            self.json_model = "llama3.1:8b-optimized"
        
        DeepSeekTwoModelPipeline._select_best_models = force_optimized_models
        
        print("✅ Expert mode optimizations applied")
        return True
        
    except Exception as e:
        print(f"❌ Error applying optimizations: {e}")
        return False

if __name__ == "__main__":
    apply_expert_mode_optimizations()
"""
    
    try:
        with open("startup_optimizations.py", "w") as f:
            f.write(startup_script)
        
        print("✅ Created startup_optimizations.py")
        print("   Import this at the beginning of main.py to apply optimizations")
        return True
        
    except Exception as e:
        print(f"❌ Error creating startup script: {e}")
        return False

def main():
    """Main function"""
    print("🔥 EXPERT MODE OPTIMIZATION ENFORCER")
    print("=" * 60)
    
    success_count = 0
    total_steps = 3
    
    # Step 1: Force optimized model selection
    if force_optimized_model_selection():
        success_count += 1
        print("✅ Step 1/3: Optimized model selection forced")
    else:
        print("❌ Step 1/3: Failed to force optimized models")
    
    # Step 2: Test forced optimization
    if test_forced_optimization():
        success_count += 1
        print("✅ Step 2/3: Forced optimization working")
    else:
        print("❌ Step 2/3: Forced optimization failed")
    
    # Step 3: Create startup script
    if create_startup_script():
        success_count += 1
        print("✅ Step 3/3: Startup script created")
    else:
        print("❌ Step 3/3: Failed to create startup script")
    
    print(f"\n🎯 ENFORCEMENT COMPLETE: {success_count}/{total_steps} steps successful")
    
    if success_count >= 2:
        print("🎉 EXPERT MODE OPTIMIZATION ENFORCED!")
        print("\n📋 NEXT STEPS:")
        print("1. Restart your app")
        print("2. Try expert mode - it should be MUCH faster now")
        print("3. Monitor system resources - you should see higher CPU usage")
        print("4. Generation time should be under 60 seconds")
    else:
        print("❌ ENFORCEMENT FAILED")
        print("   Manual intervention may be required")

if __name__ == "__main__":
    main()
