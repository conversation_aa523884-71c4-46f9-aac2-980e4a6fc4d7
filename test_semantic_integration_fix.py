#!/usr/bin/env python3
"""
Test script to verify the semantic preprocessing integration in MCQ Manager
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_ollama_availability():
    """Test if Ollama is running and has Phi model"""
    print("🔍 TESTING OLLAMA AVAILABILITY")
    print("=" * 50)
    
    try:
        import requests
        
        # Test Ollama connection
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = [m['name'] for m in response.json().get('models', [])]
            print(f"✅ Ollama running with {len(models)} models")
            
            # Check for Phi models
            phi_models = [m for m in models if 'phi' in m.lower()]
            if phi_models:
                print(f"✅ Phi models available: {phi_models}")
                return True, phi_models[0]
            else:
                print(f"❌ No Phi models found. Available: {models[:5]}")
                return False, None
        else:
            print(f"❌ Ollama not responding: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Ollama test failed: {e}")
        return False, None

def test_semantic_mapper():
    """Test semantic mapper directly"""
    print("\n🧠 TESTING SEMANTIC MAPPER")
    print("=" * 50)
    
    try:
        from knowledge_app.core.intelligent_semantic_mapper import get_semantic_mapper
        
        mapper = get_semantic_mapper()
        print("✅ Semantic mapper created")
        
        # Test with a simple abbreviation
        test_input = "CBT"
        print(f"🧪 Testing input: '{test_input}'")
        
        result = mapper.map_topic_semantically(test_input)
        print(f"✅ Semantic mapping result:")
        print(f"   Original: {result.original_input}")
        print(f"   Expanded: {result.expanded_topic}")
        print(f"   Type: {result.question_type}")
        print(f"   Confidence: {result.confidence:.2f}")
        print(f"   Reasoning: {result.reasoning}")
        print(f"   Is abbreviation: {result.is_abbreviation}")
        if result.full_form:
            print(f"   Full form: {result.full_form}")
        
        return True
        
    except Exception as e:
        print(f"❌ Semantic mapper test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mcq_manager_integration():
    """Test MCQ Manager semantic preprocessing"""
    print("\n🎯 TESTING MCQ MANAGER INTEGRATION")
    print("=" * 50)
    
    try:
        from knowledge_app.core.mcq_manager import MCQManager
        
        # Create MCQ Manager
        mcq_manager = MCQManager()
        print("✅ MCQ Manager created")
        
        # Test semantic preprocessing method directly
        test_topic = "CBT"
        print(f"🧪 Testing semantic preprocessing for: '{test_topic}'")
        
        result = mcq_manager._preprocess_topic_with_semantic_mapping(test_topic)
        print(f"✅ Semantic preprocessing result:")
        for key, value in result.items():
            if key == 'semantic_analysis' and isinstance(value, dict):
                print(f"   {key}:")
                for sub_key, sub_value in value.items():
                    print(f"     {sub_key}: {sub_value}")
            else:
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ MCQ Manager integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_quiz_generation():
    """Test full quiz generation with semantic preprocessing"""
    print("\n🚀 TESTING FULL QUIZ GENERATION")
    print("=" * 50)
    
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        # Get MCQ Manager
        mcq_manager = get_mcq_manager()
        print("✅ MCQ Manager obtained")
        
        # Test quiz parameters
        quiz_params = {
            "topic": "CBT",  # Test abbreviation
            "difficulty": "medium",
            "game_mode": "casual",
            "submode": "mixed",
            "num_questions": 1,
            "mode": "auto"
        }
        
        print(f"🧪 Testing quiz generation with params:")
        for key, value in quiz_params.items():
            print(f"   {key}: {value}")
        
        # This should trigger semantic preprocessing
        print("🔄 Starting quiz generation (this will test semantic preprocessing)...")
        result = mcq_manager.generate_quiz(quiz_params)
        
        if result:
            print("✅ Quiz generation completed successfully")
            # Handle MCQResult object
            if hasattr(result, 'question'):
                print(f"   Question: {result.question[:100]}...")
                print(f"   Options: {len(result.options)} options")
                print(f"   Correct: {result.correct_answer}")
            else:
                print(f"   Result type: {type(result)}")
            return True
        else:
            print("❌ Quiz generation returned None")
            return False
        
    except Exception as e:
        print(f"❌ Full quiz generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 SEMANTIC INTEGRATION TEST SUITE")
    print("=" * 60)
    
    tests = [
        ("Ollama Availability", test_ollama_availability),
        ("Semantic Mapper", test_semantic_mapper),
        ("MCQ Manager Integration", test_mcq_manager_integration),
        ("Full Quiz Generation", test_full_quiz_generation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if test_name == "Ollama Availability":
                result, model = test_func()
                results.append((test_name, result))
                if not result:
                    print("⚠️ Skipping remaining tests due to Ollama unavailability")
                    break
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Summary: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Semantic integration is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
