#!/usr/bin/env python3
"""
Debug quiz generation to see what's actually happening
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging to see what's happening
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

def debug_quiz_generation():
    """Debug the quiz generation process step by step"""
    print("🔍 DEBUG: Quiz Generation Process")
    print("=" * 50)
    
    try:
        # Import the unified inference manager
        from knowledge_app.core.unified_inference_manager import get_unified_inference_manager, initialize_unified_inference
        
        # Initialize
        print("🔧 Initializing unified inference system...")
        init_success = initialize_unified_inference()
        if not init_success:
            print("❌ Failed to initialize")
            return False
        
        # Get the manager
        manager = get_unified_inference_manager()
        print(f"✅ Manager initialized: {manager}")
        print(f"📊 Manager state: {manager._state}")
        print(f"🌐 Cloud available: {manager._cloud_available}")
        print(f"🎮 Local available: {manager._local_available}")
        print(f"🔄 Mode: {manager._mode}")

        # Check what generators are available
        print(f"🔍 Available generators:")
        for attr in dir(manager):
            if 'generator' in attr.lower():
                value = getattr(manager, attr, None)
                print(f"   {attr}: {value is not None}")

        # Try to generate a simple question using the unified API
        print("\n🚀 Testing unified generation API...")

        try:
            from knowledge_app.core.unified_inference_manager import generate_mcq_unified
            result = generate_mcq_unified(
                topic="physics",
                difficulty="medium",
                question_type="mixed",
                timeout=30.0
            )

            if result:
                print(f"✅ Generated question: {result.get('question', 'N/A')}")
                print(f"📋 Options: {result.get('options', [])}")
                print(f"✅ Correct: {result.get('correct', 'N/A')}")
                return True
            else:
                print("❌ No result from unified API")
        except Exception as e:
            print(f"❌ Unified API failed: {e}")
            import traceback
            traceback.print_exc()

        # Test the ollama generator directly
        if hasattr(manager, '_ollama_generator') and manager._ollama_generator:
            print("🎯 Testing Ollama generator directly...")
            ollama_gen = manager._ollama_generator

            # Test with a simple topic
            result = ollama_gen.generate_mcq("physics", "", 1, "medium", "casual", "mixed")
            print(f"📝 Ollama result: {result}")

            if result and len(result) > 0:
                question = result[0]
                print(f"✅ Generated question: {question.get('question', 'N/A')}")
                print(f"📋 Options: {question.get('options', [])}")
                print(f"✅ Correct: {question.get('correct_answer', 'N/A')}")
                return True
            else:
                print("❌ No result from Ollama generator")
        else:
            print("❌ No Ollama generator available")

        print("❌ No generators available or working")
        return False
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ollama_connection():
    """Test if Ollama is running and accessible"""
    print("\n🔌 Testing Ollama connection...")
    
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama is running with {len(models)} models")
            for model in models[:3]:  # Show first 3 models
                print(f"   📦 {model.get('name', 'Unknown')}")
            return True
        else:
            print(f"❌ Ollama responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        return False

def main():
    print("🧠 Quiz Generation Debug Tool")
    print("=" * 60)
    
    # Test Ollama connection first
    ollama_ok = test_ollama_connection()
    
    # Test quiz generation
    generation_ok = debug_quiz_generation()
    
    if generation_ok:
        print("\n🎉 SUCCESS! Quiz generation is working!")
    else:
        print("\n❌ Quiz generation is broken.")
        if not ollama_ok:
            print("💡 Suggestion: Make sure Ollama is running with 'ollama serve'")
        else:
            print("💡 Suggestion: Check the parsing logic or model responses")
    
    return 0 if generation_ok else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
