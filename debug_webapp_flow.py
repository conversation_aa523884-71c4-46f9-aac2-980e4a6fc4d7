#!/usr/bin/env python3

import sys
import os
import time
import json

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_webapp_generation_flow():
    """Test the exact flow that the web app uses"""
    
    print("🔍 TESTING WEB APP GENERATION FLOW")
    print("=" * 50)
    
    try:
        # Test 1: Test MCQ Manager (what the web app uses)
        print("\n1️⃣ Testing MCQ Manager (Web App Path)...")
        from knowledge_app.core.mcq_manager import get_mcq_manager
        from knowledge_app.core.app_config import AppConfig
        
        config = AppConfig()
        mcq_manager = get_mcq_manager(config)
        
        print(f"   MCQ Manager: {mcq_manager}")
        
        # Test the exact parameters the web app uses
        quiz_params = {
            "topic": "atoms",
            "difficulty": "medium",
            "game_mode": "casual",
            "submode": "mixed",
            "num_questions": 1
        }
        
        print(f"   Quiz params: {quiz_params}")
        
        # Test sync generation (what FastQuestionGenerator uses)
        print("   Testing sync generation...")
        result = mcq_manager.generate_quiz(quiz_params)
        
        if result:
            print("   ✅ MCQ Manager generation successful!")
            print(f"   Result type: {type(result)}")
            if hasattr(result, 'get'):
                print(f"   Question: {result.get('question', 'N/A')[:100]}...")
            else:
                print(f"   Result: {str(result)[:100]}...")
            return True
        else:
            print("   ❌ MCQ Manager returned None")
            return False
            
    except Exception as e:
        print(f"   ❌ MCQ Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_thread_safe_inference():
    """Test the thread-safe inference that FastQuestionGenerator uses"""
    
    print("\n2️⃣ Testing Thread-Safe Inference...")
    
    try:
        from knowledge_app.core.thread_safe_inference import get_thread_safe_inference

        # Create thread-safe inference (what FastQuestionGenerator uses)
        thread_safe_inference = get_thread_safe_inference()
        
        print("   Starting async generation...")
        operation_id = thread_safe_inference.generate_mcq_async(
            topic="atoms",
            difficulty="medium",
            question_type="mixed",
            timeout=30.0
        )
        
        print(f"   Operation ID: {operation_id}")
        
        # Wait for result
        print("   Waiting for result...")
        result = thread_safe_inference.get_result(operation_id, timeout=35.0)
        
        if result:
            print("   ✅ Thread-safe generation successful!")
            print(f"   Question: {result.get('question', 'N/A')[:100]}...")
            return True
        else:
            print("   ❌ Thread-safe generation returned None")
            return False
            
    except Exception as e:
        print(f"   ❌ Thread-safe inference test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fast_question_generator():
    """Test the FastQuestionGenerator class directly"""
    
    print("\n3️⃣ Testing FastQuestionGenerator...")
    
    try:
        from knowledge_app.webengine_app import FastQuestionGenerator
        from knowledge_app.core.mcq_manager import get_mcq_manager
        from knowledge_app.core.app_config import AppConfig
        
        config = AppConfig()
        mcq_manager = get_mcq_manager(config)
        
        quiz_params = {
            "topic": "atoms",
            "difficulty": "medium",
            "game_mode": "casual",
            "submode": "mixed",
            "num_questions": 1
        }
        
        # Create FastQuestionGenerator
        generator = FastQuestionGenerator(mcq_manager, quiz_params, num_questions=1)
        
        print("   Created FastQuestionGenerator")
        
        # Test the _generate_single_question_safe method directly
        print("   Testing _generate_single_question_safe...")
        question_data = generator._generate_single_question_safe(0)
        
        if question_data:
            print("   ✅ FastQuestionGenerator successful!")
            print(f"   Question: {question_data.get('question', 'N/A')[:100]}...")
            return True
        else:
            print("   ❌ FastQuestionGenerator returned None")
            return False
            
    except Exception as e:
        print(f"   ❌ FastQuestionGenerator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    
    print("Testing the exact web app generation flow to find the failure point...\n")
    
    # Test each component in the chain
    tests = [
        ("MCQ Manager", test_webapp_generation_flow),
        ("Thread-Safe Inference", test_thread_safe_inference),
        ("FastQuestionGenerator", test_fast_question_generator)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"TESTING: {test_name}")
        print('='*60)
        
        try:
            success = test_func()
            results[test_name] = success
            
            if success:
                print(f"\n✅ {test_name} PASSED")
            else:
                print(f"\n❌ {test_name} FAILED")
                
        except Exception as e:
            print(f"\n💥 {test_name} CRASHED: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print('='*60)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    # Find the failure point
    failed_tests = [name for name, success in results.items() if not success]
    
    if not failed_tests:
        print("\n🎉 All tests passed! The issue might be elsewhere.")
    else:
        print(f"\n🔍 Failure point: {failed_tests[0]}")
        print("This is where the web app generation is breaking down.")

if __name__ == "__main__":
    main()
