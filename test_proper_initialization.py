#!/usr/bin/env python3
"""
Test proper initialization of UnifiedInferenceManager
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_proper_initialization():
    """Test the proper initialization function"""
    print("🔧 TESTING PROPER UNIFIED INFERENCE MANAGER INITIALIZATION")
    print("=" * 70)
    
    try:
        from knowledge_app.core.unified_inference_manager import initialize_unified_inference, get_unified_inference_manager
        
        print("\n1️⃣ Calling initialize_unified_inference...")
        success = initialize_unified_inference()
        print(f"   🚀 Initialization result: {success}")
        
        print("\n2️⃣ Getting manager and checking status...")
        manager = get_unified_inference_manager()
        status = manager.get_status()
        
        print(f"   📊 State: {status.get('state', 'unknown')}")
        print(f"   🏠 Local available: {status.get('local_available', False)}")
        print(f"   ☁️ Cloud available: {status.get('cloud_available', False)}")
        
        local_available = status.get('local_available', False)
        if local_available:
            print("\n🎉 SUCCESS: Local models are available!")
            return True
        else:
            print("\n❌ FAILED: Local models are not available")
            return False
            
    except Exception as e:
        print(f"❌ Proper initialization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_expert_mode_generation():
    """Test expert mode generation with properly initialized manager"""
    print("\n🧪 TESTING EXPERT MODE GENERATION")
    print("=" * 50)
    
    try:
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        print("\n1️⃣ Getting MCQ Manager...")
        mcq_manager = get_mcq_manager()
        
        print("\n2️⃣ Testing expert mode generation...")
        quiz_params = {
            "topic": "basic algebra",
            "difficulty": "expert",
            "game_mode": "serious",
            "submode": "mixed",
            "num_questions": 1
        }
        
        print(f"   🧪 Testing with params: {quiz_params}")
        
        result = await mcq_manager.generate_quiz_async(quiz_params)
        
        if result:
            print("   ✅ Expert mode generation successful!")
            print(f"   ❓ Question: {result.question[:100]}...")
            print(f"   🔧 Method: {getattr(result, 'generation_method', 'unknown')}")
            return True
        else:
            print("   ❌ Expert mode generation failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Expert mode generation error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Test proper initialization
    init_success = test_proper_initialization()
    
    if init_success:
        # Test expert mode generation
        gen_success = asyncio.run(test_expert_mode_generation())
        
        if gen_success:
            print("\n🎉 COMPLETE SUCCESS: Expert mode is working!")
        else:
            print("\n⚠️ PARTIAL SUCCESS: Initialization works but generation failed")
    else:
        print("\n❌ FAILED: Initialization failed")
