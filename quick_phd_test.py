#!/usr/bin/env python3
"""
Quick test to see if PhD-level generation is working but timing out
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_phd_generation():
    """Test PhD-level generation with my fixes"""
    
    print("🎓 TESTING PhD-LEVEL GENERATION WITH FIXES")
    print("=" * 60)
    
    try:
        from knowledge_app.core.deepseek_integration import DeepSeekTwoModelPipeline
        
        # Initialize DeepSeek pipeline
        pipeline = DeepSeekTwoModelPipeline()
        
        print("✅ DeepSeek pipeline initialized")
        print("🧠 Generating PhD-level question...")
        
        # Test with my PhD-level fix
        result = pipeline.generate_expert_question(
            topic="atoms",
            difficulty="expert",  # This should trigger PhD-level prompts
            question_type="numerical",
            context="",
            progress_callback=None,
            generation_instructions="Generate a PhD-level question about quantum mechanics"
        )
        
        if result:
            question = result.get('question', '')
            print(f"📝 Generated question: {question}")
            
            # Check if it's PhD-level
            phd_terms = ['relativistic', 'dirac', 'perturbation', 'fine structure', 'hyperfine', 'quantum field', 'many-body', 'correlation']
            is_phd = any(term in question.lower() for term in phd_terms)
            
            if is_phd:
                print("🎉 SUCCESS: PhD-level question generated!")
                print("✅ My DeepSeek prompt fix is working!")
            else:
                print("❌ FAILED: Still generating basic questions")
                print("🔍 The prompt fix didn't work")
                
        else:
            print("❌ No result generated")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_phd_generation()
