"""
Complete NumPy Array Pydantic Schema Fix

This module provides a comprehensive solution to eliminate the pydantic schema warning:
"Unable to generate pydantic-core schema for <class 'numpy.ndarray'>"

The fix works by intercepting pydantic's schema generation process and providing
a custom schema for numpy arrays before any warnings can be generated.
"""

import logging
import warnings
from typing import Any, Type, Optional

logger = logging.getLogger(__name__)


def suppress_pydantic_warnings():
    """Apply comprehensive fix for numpy array pydantic schema issues"""

    try:
        import numpy as np
        from pydantic_core import core_schema
        from pydantic import GetCoreSchemaHandler

        # Suppress the specific warning we're trying to fix
        warnings.filterwarnings(
            "ignore",
            message=".*Unable to generate pydantic-core schema for.*numpy.ndarray.*",
            category=UserWarning,
        )

        # Create the most robust numpy array schema possible
        def ultimate_numpy_schema(
            source: Type[Any], handler: GetCoreSchemaHandler, info=None
        ) -> core_schema.CoreSchema:
            """Ultimate numpy array schema that handles all cases"""

            def validate_numpy_input(value: Any) -> Any:
                """Validate and convert input to numpy array or pass through"""
                if value is None:
                    return None
                if isinstance(value, np.ndarray):
                    return value
                # For any other type, just pass it through to avoid conversion errors
                return value

            return core_schema.no_info_after_validator_function(
                validate_numpy_input, core_schema.any_schema()
            )

        # Method 1: Direct schema assignment (most effective when it works)
        try:
            if not hasattr(np.ndarray, "__get_pydantic_core_schema__"):
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    np.ndarray.__get_pydantic_core_schema__ = classmethod(ultimate_numpy_schema)
                    logger.debug("✅ Applied direct numpy schema assignment")
                    return True
        except (TypeError, AttributeError):
            # Expected - numpy.ndarray is immutable
            pass

        # Method 2: Monkey patch pydantic's schema generation (with compatibility check)
        try:
            from pydantic._internal._generate_schema import GenerateSchema

            # Check if the method exists before trying to patch it
            if hasattr(GenerateSchema, "generate_schema_from_property"):
                # Store original method
                if not hasattr(GenerateSchema, "_original_generate_schema_from_property"):
                    GenerateSchema._original_generate_schema_from_property = (
                        GenerateSchema.generate_schema_from_property
                    )

                def patched_generate_schema_from_property(self, obj, source):
                    """Patched schema generation that handles numpy arrays"""
                    try:
                        # Check if this is a numpy array type
                        if obj is np.ndarray or (
                            hasattr(obj, "__origin__") and obj.__origin__ is np.ndarray
                        ):
                            return ultimate_numpy_schema(obj, self.generate_schema, None)

                        # For numpy array instances
                        if isinstance(obj, type) and issubclass(obj, np.ndarray):
                            return ultimate_numpy_schema(obj, self.generate_schema, None)

                        # Fall back to original method
                        return self._original_generate_schema_from_property(obj, source)

                    except Exception:
                        # If anything fails, fall back to original method
                        return self._original_generate_schema_from_property(obj, source)

                GenerateSchema.generate_schema_from_property = patched_generate_schema_from_property
                logger.debug("✅ Applied pydantic schema generation patch")
                return True
            else:
                logger.debug(
                    "⚠️ GenerateSchema.generate_schema_from_property not found - using alternative approach"
                )

        except (ImportError, AttributeError) as e:
            logger.debug(f"⚠️ Could not patch GenerateSchema: {e}")
            pass

        # Method 3: Global type registration
        try:
            from pydantic._internal._typing_extra import eval_type_lenient
            from pydantic import TypeAdapter

            # Create a type adapter that doesn't generate warnings
            class SilentNumpyAdapter(TypeAdapter):
                def __init__(self):
                    # Initialize without calling parent __init__ to avoid schema generation
                    pass

                def validate_python(self, obj):
                    if isinstance(obj, np.ndarray):
                        return obj
                    return obj

            # Register the adapter globally
            globals()["_silent_numpy_adapter"] = SilentNumpyAdapter()
            logger.debug("✅ Created silent numpy adapter")
            return True

        except Exception:
            pass

        # Method 4: Override pydantic core schema generation at the module level
        try:
            import pydantic_core

            # Store original function
            if not hasattr(pydantic_core, "_original_generate_schema"):
                pydantic_core._original_generate_schema = getattr(
                    pydantic_core, "generate_schema", None
                )

            def patched_core_generate_schema(*args, **kwargs):
                """Patched core schema generation"""
                try:
                    # Check if numpy array is involved
                    for arg in args:
                        if arg is np.ndarray or (
                            hasattr(arg, "__name__") and "ndarray" in arg.__name__
                        ):
                            # Return a simple any schema for numpy arrays
                            return core_schema.any_schema()

                    # Fall back to original if available
                    if pydantic_core._original_generate_schema:
                        return pydantic_core._original_generate_schema(*args, **kwargs)
                    else:
                        return core_schema.any_schema()

                except Exception:
                    return core_schema.any_schema()

            if hasattr(pydantic_core, "generate_schema"):
                pydantic_core.generate_schema = patched_core_generate_schema
                logger.debug("✅ Applied pydantic core schema patch")
                return True

        except Exception:
            pass

        logger.warning("⚠️ Could not apply comprehensive numpy fix with any method")
        return False

    except ImportError:
        logger.warning("⚠️ NumPy or pydantic not available for schema fix")
        return False
    except Exception as e:
        logger.warning(f"⚠️ Comprehensive numpy fix failed: {e}")
        return False


def suppress_numpy_warnings():
    """Suppress specific numpy-related pydantic warnings"""

    # Suppress the exact warning we're trying to eliminate
    warnings.filterwarnings(
        "ignore",
        message=".*Unable to generate pydantic-core schema for.*numpy.ndarray.*",
        category=UserWarning,
    )

    warnings.filterwarnings(
        "ignore",
        message=".*Set `arbitrary_types_allowed=True` in the model_config.*",
        category=UserWarning,
    )

    warnings.filterwarnings(
        "ignore",
        message=".*implement `__get_pydantic_core_schema__` on your type.*",
        category=UserWarning,
    )

    logger.debug("✅ Suppressed numpy-related pydantic warnings")


# Apply fixes immediately when module is imported
suppress_numpy_warnings()
fix_applied = suppress_pydantic_warnings()

if fix_applied:
    logger.debug("✅ Comprehensive numpy pydantic fix applied successfully")
else:
    logger.debug("⚠️ Comprehensive numpy pydantic fix could not be applied")