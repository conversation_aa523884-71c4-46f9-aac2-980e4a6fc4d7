#!/usr/bin/env python3
"""
Final test of expert mode generation
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_expert_mode_final():
    """Final test of expert mode generation"""
    print("🎯 FINAL TEST: EXPERT MODE GENERATION")
    print("=" * 50)
    
    try:
        # Step 1: Initialize unified inference
        print("\n1️⃣ Initializing unified inference...")
        from knowledge_app.core.unified_inference_manager import initialize_unified_inference
        
        success = initialize_unified_inference()
        print(f"   🚀 Initialization: {success}")
        
        if not success:
            print("   ❌ Initialization failed")
            return False
        
        # Step 2: Get MCQ manager
        print("\n2️⃣ Getting MCQ manager...")
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        mcq_manager = get_mcq_manager()
        print("   ✅ MCQ manager obtained")
        
        # Step 3: Test expert mode generation
        print("\n3️⃣ Testing expert mode generation...")
        quiz_params = {
            "topic": "calculus derivatives",
            "difficulty": "expert",
            "game_mode": "serious",
            "submode": "mixed",
            "num_questions": 1
        }
        
        print(f"   🧪 Params: {quiz_params}")
        print("   ⏳ Generating (this may take a moment)...")
        
        result = await mcq_manager.generate_quiz_async(quiz_params)
        
        if result:
            print("   ✅ Generation successful!")
            print(f"   ❓ Question: {result.question[:150]}...")
            print(f"   🔧 Method: {getattr(result, 'generation_method', 'unknown')}")
            print(f"   📊 Options: {len(result.options)} choices")
            print(f"   ✅ Correct: {result.correct_answer}")
            return True
        else:
            print("   ❌ Generation failed - no result returned")
            return False
            
    except Exception as e:
        print(f"   ❌ Expert mode test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_expert_mode_final())
    if success:
        print("\n🎉 SUCCESS: Expert mode is working perfectly!")
        print("The 'No fallback content available' error should now be fixed.")
    else:
        print("\n❌ FAILED: Expert mode still has issues")
