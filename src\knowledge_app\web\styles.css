/* Global Styles */
:root {
    /* Light theme colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --shadow: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
    /* Dark theme colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --text-primary: #f8fafc;
    --text-secondary: #94a3b8;
    --border-color: #334155;
    --primary-color: #6366f1;
    --primary-hover: #818cf8;
    --shadow: rgba(0, 0, 0, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

#app {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header Styles */
.app-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px var(--shadow);
}

.header-content h1 {
    font-size: 1.75rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.25rem;
}

.header-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.theme-toggle {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background-color 0.3s ease;
}

.theme-toggle:hover {
    background-color: var(--border-color);
}

/* Main Layout */
.app-main {
    display: flex;
    flex: 1;
}

/* Navigation */
.side-nav {
    width: 250px;
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    border-radius: 0.5rem;
    color: var(--text-primary);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    width: 100%;
}

.nav-item:hover {
    background-color: var(--border-color);
    transform: translateX(4px);
}

.nav-item.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-item .icon {
    font-size: 1.25rem;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.screen {
    display: none;
    animation: fadeIn 0.3s ease;
}

.screen.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Welcome Card */
.welcome-card {
    background-color: var(--bg-secondary);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px var(--shadow);
    max-width: 800px;
    margin: 0 auto;
}

.welcome-card h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.welcome-card p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: var(--bg-primary);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    border: 1px solid var(--border-color);
}

.stat-card h3 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-card p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow);
}

.btn-secondary {
    background-color: var(--border-color);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background-color: var(--text-secondary);
    color: white;
}

/* DeepSeek Integration Styles */

/* DeepSeek Section Styles */
.deepseek-section {
    background: linear-gradient(135deg, rgba(102, 102, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border: 2px solid rgba(102, 102, 234, 0.2);
    border-radius: 12px;
    padding: 16px;
    margin: 16px 0;
}

.deepseek-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.deepseek-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.1em;
}

.deepseek-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9em;
}

.status-indicator {
    font-size: 1.2em;
}

.status-indicator.ready {
    color: var(--success-color);
}

.status-indicator.error {
    color: var(--danger-color);
}

.status-indicator.loading {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.deepseek-info {
    color: var(--text-secondary);
    font-size: 0.85em;
    line-height: 1.4;
}



/* Quiz Container */
.quiz-container {
    max-width: 800px;
    margin: 0 auto;
}

.quiz-setup,
.quiz-game,
.quiz-results {
    background-color: var(--bg-secondary);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px var(--shadow);
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Quiz Game */
.quiz-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    color: var(--text-secondary);
}

.question-container {
    margin-bottom: 2rem;
}

.question-container h3 {
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

.options-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.option-button {
    padding: 1rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    font-size: 1rem;
}

.option-button:hover {
    border-color: var(--primary-color);
    transform: translateX(4px);
}

.option-button.selected {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.option-button.correct-answer {
    background-color: var(--success-color) !important;
    color: white !important;
    border-color: var(--success-color) !important;
}

.option-button.incorrect-answer {
    background-color: var(--danger-color) !important;
    color: white !important;
    border-color: var(--danger-color) !important;
}

.option-button:disabled {
    cursor: not-allowed;
    opacity: 0.8;
}

/* Feedback Containers */
.feedback-container {
    margin: 1.5rem 0;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
}

.feedback-message {
    padding: 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 1rem;
}

.feedback-message.correct {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.feedback-message.incorrect {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.explanation-container {
    margin: 1.5rem 0;
    padding: 1.5rem;
    background-color: var(--bg-secondary);
    border-radius: 0.75rem;
    border-left: 4px solid var(--primary-color);
}

.explanation h4 {
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.explanation p {
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
}

/* Status Display */
.status-display {
    margin-bottom: 1.5rem;
    padding: 1rem;
    border-radius: 0.75rem;
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px var(--shadow);
}

.status-message {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-weight: 600;
    animation: statusPulse 2s ease-in-out infinite;
}

.status-message.turbo {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    color: var(--primary-color);
    border: 1px solid rgba(99, 102, 241, 0.3);
}

.status-message.gpu {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(6, 182, 212, 0.1));
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-message.success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1));
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-message.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1));
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-message.error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(248, 113, 113, 0.1));
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-message.info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
    color: var(--primary-color);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.status-message.api {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(34, 197, 94, 0.1));
    color: #3B82F6;
    border: 1px solid rgba(59, 130, 246, 0.3);
    animation: networkPulse 3s ease-in-out infinite;
}

.status-message.cloud {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(59, 130, 246, 0.1));
    color: #A855F7;
    border: 1px solid rgba(168, 85, 247, 0.3);
}

.status-message.network {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(59, 130, 246, 0.1));
    color: #22C55E;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-icon {
    font-size: 1.5rem;
    animation: iconSpin 3s linear infinite;
}

.status-text {
    flex: 1;
    font-size: 1rem;
}

.status-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Status Animations */
@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.02);
    }
}

@keyframes iconSpin {
    0% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(90deg);
    }
    50% {
        transform: rotate(180deg);
    }
    75% {
        transform: rotate(270deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes networkPulse {
    0%, 100% {
        opacity: 1;
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
    }
    50% {
        opacity: 0.9;
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Navigation Buttons */
.navigation-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.quiz-footer {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Quiz Results */
.score-display {
    text-align: center;
    margin: 2rem 0;
}

.score-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 4px 8px var(--shadow);
}

.score-circle span {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
}

#score-text {
    text-align: center;
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 0.75rem;
    padding: 3rem;
    text-align: center;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-primary);
}

.upload-area.drag-over {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    background-opacity: 0.1;
}

.file-list {
    margin-bottom: 1.5rem;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Settings */
.settings-container {
    max-width: 600px;
    margin: 0 auto;
}

.settings-section {
    background-color: var(--bg-secondary);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.settings-section h3 {
    margin-bottom: 1rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
    border-bottom: none;
}

/* Footer */
.app-footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: 1rem;
    text-align: center;
    color: var(--text-secondary);
}

/* Mode Info and API Status */
.mode-info {
    margin-top: 5px;
    padding: 5px 0;
    color: var(--text-secondary);
}

.api-status {
    margin-top: 10px;
    padding: 15px;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.api-providers {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.api-provider {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.api-provider.available {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-color: var(--success-color);
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.2);
}

.api-provider.unavailable {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.api-provider.unknown {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border-color: var(--warning-color);
}

.api-help {
    padding-top: 10px;
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-size: 11px;
    line-height: 1.4;
}

.api-help strong {
    color: var(--text-primary);
}

/* API Provider Configuration */
.api-providers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.api-provider-card {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.api-provider-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px var(--shadow);
    border-color: var(--primary-color);
}

.provider-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.provider-icon {
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.provider-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-primary);
    flex: 1;
}

.provider-status {
    font-size: 1.2rem;
    margin-left: 0.5rem;
}

.provider-status.available {
    color: var(--success-color);
}

.provider-status.unavailable {
    color: var(--danger-color);
}

.provider-status.testing {
    color: var(--warning-color);
    animation: pulse 1.5s ease-in-out infinite;
}

.api-key-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.api-key-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.api-key-input:not(:placeholder-shown) {
    border-color: var(--success-color);
    background-color: rgba(16, 185, 129, 0.05);
}

.provider-info {
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.4;
}

.section-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    line-height: 1.5;
}

.api-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.api-help {
    background: var(--bg-primary);
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
}

.api-help details {
    cursor: pointer;
}

.api-help summary {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: background-color 0.3s ease;
}

.api-help summary:hover {
    background-color: var(--bg-secondary);
}

.api-help ul {
    margin: 1rem 0 0 0;
    padding-left: 1.5rem;
}

.api-help li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.api-help a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.api-help a:hover {
    text-decoration: underline;
}

/* API Status Animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Enhanced API Provider Card States */
.api-provider-card.connected {
    border-color: var(--success-color);
    background: linear-gradient(135deg, var(--bg-primary), rgba(16, 185, 129, 0.05));
}

.api-provider-card.connected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--success-color), rgba(16, 185, 129, 0.5));
    border-radius: 0.75rem 0.75rem 0 0;
}

.api-provider-card.error {
    border-color: var(--danger-color);
    background: linear-gradient(135deg, var(--bg-primary), rgba(239, 68, 68, 0.05));
}

.api-provider-card.error::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--danger-color), rgba(239, 68, 68, 0.5));
    border-radius: 0.75rem 0.75rem 0 0;
}

.api-provider-card.testing {
    border-color: var(--warning-color);
    background: linear-gradient(135deg, var(--bg-primary), rgba(245, 158, 11, 0.05));
}

.api-provider-card.testing::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--warning-color), rgba(245, 158, 11, 0.5));
    border-radius: 0.75rem 0.75rem 0 0;
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0% {
        opacity: 0.5;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.5;
    }
}

/* API Test Modal Styles */
.api-test-modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.api-test-modal-content {
    position: relative;
    background: var(--bg-primary);
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.api-test-modal-content.success {
    border: 3px solid var(--success-color);
}

.api-test-modal-content.error {
    border: 3px solid var(--danger-color);
}

.api-test-modal-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem 1.5rem 0;
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
}

.api-test-modal-emoji {
    font-size: 2rem;
    animation: bounce 1s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.api-test-modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.api-test-modal-body {
    padding: 1.5rem;
}

.api-test-modal-body p {
    margin: 0 0 1rem;
    color: var(--text-primary);
    line-height: 1.6;
}

.api-test-success-details {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid var(--success-color);
    border-radius: 0.5rem;
    padding: 1rem;
    color: var(--success-color);
    font-weight: 500;
    margin-top: 1rem;
}

.api-test-error-details {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--danger-color);
    border-radius: 0.5rem;
    padding: 1rem;
    color: var(--danger-color);
    font-weight: 500;
    margin-top: 1rem;
}

.api-test-modal-footer {
    padding: 0 1.5rem 1.5rem;
    display: flex;
    justify-content: center;
}

.api-test-modal-footer .btn {
    min-width: 120px;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .side-nav {
        position: fixed;
        left: -250px;
        height: 100%;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .side-nav.open {
        left: 0;
    }
    
    .content-area {
        margin-left: 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .api-providers {
        justify-content: center;
    }

    .api-test-modal-content {
        width: 95%;
        margin: 1rem;
    }
}

/* LaTeX and MathJax Styling */
.MathJax {
    font-size: 1.1em !important;
    color: var(--text-primary) !important;
}

.MathJax_Display {
    margin: 0.75em 0 !important;
}

/* LaTeX in questions */
.question-container .MathJax {
    font-size: 1.2em !important;
    color: var(--text-primary) !important;
}

/* LaTeX in options */
.option-button .MathJax {
    font-size: 1em !important;
    vertical-align: middle !important;
    color: inherit !important;
}

/* LaTeX in explanations */
.explanation .MathJax {
    font-size: 1.05em !important;
    color: var(--text-secondary) !important;
}

/* Inline math styling */
.MathJax .mjx-math {
    color: var(--text-primary) !important;
}

/* Math expressions in dark mode */
[data-theme="dark"] .MathJax {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .MathJax .mjx-math {
    color: var(--text-primary) !important;
}

/* Ensure math doesn't break layout */
.mjx-container {
    display: inline-block !important;
    margin: 0 2px !important;
}

/* Fix MathJax positioning in buttons */
.option-button .mjx-container {
    vertical-align: baseline !important;
    margin: 0 1px !important;
}

/* Media queries for responsive MathJax */
@media (max-width: 768px) {
    .side-nav {
        position: fixed;
        left: -250px;
        height: 100%;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .side-nav.open {
        left: 0;
    }
    
    .content-area {
        margin-left: 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .api-providers {
        justify-content: center;
    }

    .api-test-modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    /* Responsive MathJax for mobile */
    .MathJax {
        font-size: 1em !important;
    }
    
    .question-container .MathJax {
        font-size: 1.1em !important;
    }
}

/* API Key Management Enhancement Styles */
.api-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
    margin-top: 1rem;
}

.api-actions .btn {
    font-size: 0.9rem;
    padding: 0.6rem 1rem;
    white-space: nowrap;
}

.api-status-summary {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-indicators {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    font-size: 0.85rem;
    flex-wrap: wrap;
}

.status-indicators span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    backdrop-filter: blur(10px);
}

/* Advanced Reset Modal Styles */
.reset-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.reset-modal {
    background: var(--bg-color, #ffffff);
    color: var(--text-color, #333333);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 420px;
    width: 90%;
    text-align: center;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.reset-modal h3 {
    margin-bottom: 1rem;
    color: var(--primary-color, #2563eb);
}

.reset-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 1.5rem 0;
}

.reset-options .btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.25rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.reset-options .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.reset-options .btn small {
    opacity: 0.8;
    font-size: 0.8rem;
    font-weight: normal;
    line-height: 1.3;
}

.reset-options .btn-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.reset-options .btn-warning:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
}

.reset-options .btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.reset-options .btn-danger:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

/* Enhanced API Provider Cards */
.api-provider-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.api-provider-card.connected {
    border-color: rgba(16, 185, 129, 0.3);
    background: rgba(16, 185, 129, 0.05);
}

.api-provider-card.error {
    border-color: rgba(239, 68, 68, 0.3);
    background: rgba(239, 68, 68, 0.05);
}

.api-provider-card.testing {
    border-color: rgba(59, 130, 246, 0.3);
    background: rgba(59, 130, 246, 0.05);
    animation: testingPulse 2s infinite;
}

@keyframes testingPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
}

/* Enhanced Provider Status Indicators */
.provider-status {
    font-size: 1.2rem;
    font-weight: bold;
    min-width: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.provider-status.available {
    color: #10b981;
    text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.provider-status.unavailable {
    color: #ef4444;
}

.provider-status.testing {
    color: #3b82f6;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design for API Management */
@media (max-width: 768px) {
    .api-actions {
        grid-template-columns: 1fr 1fr;
    }
    
    .status-indicators {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .reset-modal {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .reset-options .btn {
        padding: 1rem;
    }
}

/* Dark Mode Adaptations */
[data-theme="dark"] .api-status-summary {
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
}

[data-theme="dark"] .status-indicators span {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .reset-modal {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* 🧠 TOPIC ANALYSIS STYLES - Intelligent Question Type Recommendations */

/* Disabled buttons for topic analysis */
.disabled-button,
.topic-disabled {
    opacity: 0.4 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
    background-color: var(--border-color) !important;
    color: var(--text-secondary) !important;
    border-color: var(--border-color) !important;
    transform: none !important;
    transition: all 0.3s ease;
}

/* Disabled options in select dropdowns */
option.disabled-option,
option.topic-disabled {
    background-color: var(--border-color) !important;
    color: var(--text-secondary) !important;
    font-style: italic;
    opacity: 0.6;
}

option:disabled {
    background-color: var(--border-color) !important;
    color: var(--text-secondary) !important;
    font-style: italic;
    opacity: 0.6;
}

/* Enhanced select styling for topic analysis */
#quiz-submode {
    transition: all 0.3s ease;
}

#quiz-submode:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.disabled-button:hover,
.topic-disabled:hover {
    transform: none !important;
    background-color: var(--border-color) !important;
    border-color: var(--border-color) !important;
}

/* Recommendation indicators */
.topic-recommended {
    position: relative;
    border: 2px solid rgba(99, 102, 241, 0.3) !important;
    background: linear-gradient(135deg, var(--bg-primary), rgba(99, 102, 241, 0.05)) !important;
    animation: topicRecommendedPulse 2s ease-in-out infinite;
}

.topic-recommended::before {
    content: "👍";
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px var(--shadow);
    z-index: 10;
}

.topic-optimal {
    position: relative;
    border: 2px solid var(--success-color) !important;
    background: linear-gradient(135deg, var(--bg-primary), rgba(16, 185, 129, 0.1)) !important;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.2) !important;
    animation: topicOptimalGlow 1.5s ease-in-out infinite alternate;
}

.topic-optimal::before {
    content: "⭐";
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--success-color);
    color: white;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
    z-index: 10;
    animation: starBounce 2s ease-in-out infinite;
}

/* Topic analysis animations */
@keyframes topicRecommendedPulse {
    0%, 100% {
        box-shadow: 0 0 0 rgba(99, 102, 241, 0.3);
    }
    50% {
        box-shadow: 0 0 15px rgba(99, 102, 241, 0.4);
    }
}

@keyframes topicOptimalGlow {
    0% {
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
    }
    100% {
        box-shadow: 0 0 30px rgba(16, 185, 129, 0.4);
    }
}

@keyframes starBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-4px);
    }
    60% {
        transform: translateY(-2px);
    }
}

/* Enhanced button states for better topic analysis feedback */
.form-group select.enhanced {
    transition: all 0.3s ease;
    position: relative;
}

.form-group select.enhanced:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Topic input enhancement */
#quiz-topic {
    transition: all 0.3s ease;
}

#quiz-topic:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

#quiz-topic.analyzing {
    border-color: var(--warning-color);
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

/* Topic analysis feedback tooltip */
.topic-analysis-tooltip {
    position: absolute;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 8px 16px var(--shadow);
    z-index: 1000;
    max-width: 300px;
    font-size: 14px;
    line-height: 1.4;
    animation: tooltipFadeIn 0.3s ease;
}

.topic-analysis-tooltip::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid var(--border-color);
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark theme adjustments for topic analysis */
[data-theme="dark"] .topic-recommended {
    background: linear-gradient(135deg, var(--bg-primary), rgba(99, 102, 241, 0.1)) !important;
}

[data-theme="dark"] .topic-optimal {
    background: linear-gradient(135deg, var(--bg-primary), rgba(16, 185, 129, 0.15)) !important;
}

[data-theme="dark"] .topic-analysis-tooltip {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
}

/* Mobile responsiveness for topic analysis */
@media (max-width: 768px) {
    .topic-recommended::before,
    .topic-optimal::before {
        width: 20px;
        height: 20px;
        font-size: 10px;
        top: -6px;
        right: -6px;
    }
    
    .topic-analysis-tooltip {
        max-width: 250px;
        font-size: 13px;
        padding: 10px;
    }
}

/* Accessibility improvements */
.disabled-button:focus,
.topic-disabled:focus {
    outline: 2px solid var(--text-secondary);
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    .topic-recommended,
    .topic-optimal,
    .topic-recommended::before,
    .topic-optimal::before {
        animation: none;
    }
}

/* 🏆 QUESTION REVIEW & HISTORY STYLES */

.review-container {
    max-width: 1200px;
    margin: 0 auto;
}

.review-container h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.review-container p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

/* Review Controls */
.review-controls {
    background-color: var(--bg-secondary);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px var(--shadow);
}

.filter-section h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.filter-item {
    display: flex;
    flex-direction: column;
}

.filter-item label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.filter-item input,
.filter-item select {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.filter-item input:focus,
.filter-item select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.review-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Question Statistics */
.question-stats {
    background-color: var(--bg-secondary);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px var(--shadow);
}

.question-stats h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.question-stats .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.question-stats .stat-card {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    border: 1px solid var(--border-color);
    padding: 1rem;
}

.question-stats .stat-card h3 {
    font-size: 2rem;
    color: var(--primary-color);
}

/* Question History Container */
.question-history-container {
    min-height: 400px;
}

.loading-message {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.no-questions {
    text-align: center;
    padding: 3rem;
    background-color: var(--bg-secondary);
    border-radius: 1rem;
    border: 2px dashed var(--border-color);
}

.no-questions p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

/* Questions List */
.questions-list {
    display: grid;
    gap: 1rem;
}

.question-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px var(--shadow);
}

.question-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px var(--shadow);
    border-color: var(--primary-color);
}

.question-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.question-meta {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.difficulty-badge,
.topic-badge,
.type-badge,
.date-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-badge {
    background-color: var(--border-color);
    color: var(--text-primary);
}

.difficulty-easy {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.difficulty-medium {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.difficulty-hard {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.difficulty-expert {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(168, 85, 247, 0.1));
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
    animation: expertGlow 2s ease-in-out infinite alternate;
}

@keyframes expertGlow {
    from {
        box-shadow: 0 0 5px rgba(239, 68, 68, 0.3);
    }
    to {
        box-shadow: 0 0 15px rgba(239, 68, 68, 0.6);
    }
}

.topic-badge {
    background-color: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(99, 102, 241, 0.3);
}

.type-badge {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.date-badge {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.question-date {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.question-preview h4 {
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    line-height: 1.4;
    color: var(--text-primary);
}

.question-stats {
    margin-bottom: 1rem;
}

.accuracy-stat {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.never-attempted {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.question-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-small.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-small.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-small.btn-secondary {
    background-color: var(--border-color);
    color: var(--text-primary);
}

.btn-small.btn-secondary:hover {
    background-color: var(--text-secondary);
    color: white;
}

/* Question Detail Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: 1rem;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin: 0;
}

.modal-close {
    font-size: 2rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 2rem;
}

.question-detail {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.question-meta-detail {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.question-content-detail h3 {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    line-height: 1.4;
    color: var(--text-primary);
}

.options-detail h4,
.explanation-detail h4,
.performance-detail h4 {
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.options-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-secondary);
}

.option-item.correct-option {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
}

.option-letter {
    background-color: var(--primary-color);
    color: white;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.option-item.correct-option .option-letter {
    background-color: var(--success-color);
}

.option-text {
    flex: 1;
    color: var(--text-primary);
}

.correct-indicator {
    color: var(--success-color);
    font-weight: 600;
    font-size: 0.875rem;
}

.explanation-detail p {
    line-height: 1.6;
    color: var(--text-primary);
}

.performance-detail {
    background-color: var(--bg-secondary);
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid var(--success-color);
}

.accuracy-bar {
    width: 100%;
    height: 8px;
    background-color: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.accuracy-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--primary-color));
    transition: width 0.3s ease;
}

.question-actions-detail {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Responsive Design for Review */
@media (max-width: 768px) {
    .filter-grid {
        grid-template-columns: 1fr;
    }
    
    .review-actions {
        flex-direction: column;
    }
    
    .question-card-header {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .question-meta {
        order: 2;
    }
    
    .question-date {
        order: 1;
        align-self: flex-end;
    }
    
    .modal-content {
        margin: 1rem;
        width: calc(100% - 2rem);
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .question-actions-detail {
        flex-direction: column;
    }
    
    .option-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .option-letter {
        align-self: flex-start;
    }
}

/* Dark theme adjustments for review */
[data-theme="dark"] .question-card {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .question-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .modal-content {
    background-color: var(--bg-primary);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .option-item {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .option-item.correct-option {
    background-color: rgba(16, 185, 129, 0.15);
    border-color: rgba(16, 185, 129, 0.4);
}

[data-theme="dark"] .performance-detail {
    background-color: var(--bg-primary);
}

/* 🚀 Phase 2: Enhanced Training UI Styles */

.training-configuration {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.config-section {
    margin-bottom: 24px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    transition: box-shadow 0.3s ease;
}

.config-section:hover {
    box-shadow: 0 4px 12px var(--shadow);
}

.config-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.config-section input[type="text"],
.config-section select {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.3s ease;
    font-family: inherit;
}

.config-section input[type="text"]:focus,
.config-section select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.config-section small {
    display: block;
    margin-top: 6px;
    color: var(--text-secondary);
    font-size: 12px;
    font-style: italic;
}

.file-selection {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 16px;
    background: var(--bg-primary);
    transition: border-color 0.3s ease;
}

.file-selection:hover {
    border-color: var(--primary-color);
}

.file-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin: 4px 0;
    background: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-option:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateX(4px);
}

.file-option input[type="checkbox"] {
    margin-right: 12px;
    cursor: pointer;
    transform: scale(1.1);
}

.file-name {
    flex: 1;
    font-weight: 500;
    color: inherit;
    transition: color 0.2s ease;
}

.file-size {
    color: var(--text-secondary);
    font-size: 12px;
    opacity: 0.8;
}

.file-option:hover .file-size {
    color: rgba(255, 255, 255, 0.8);
}

.no-files {
    text-align: center;
    padding: 24px;
    color: var(--text-secondary);
    font-style: italic;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
}

.file-selection-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
    justify-content: center;
}

.model-info,
.preset-description {
    margin-top: 8px;
    padding: 12px;
    background: var(--bg-primary);
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
    transition: all 0.3s ease;
}

.model-info:hover,
.preset-description:hover {
    box-shadow: 0 2px 8px var(--shadow);
    transform: translateX(2px);
}

.training-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 32px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.training-actions .btn {
    min-width: 140px;
    transition: all 0.3s ease;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border: 1px solid var(--danger-color);
}

.btn-danger:hover {
    background-color: #dc2626;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.training-progress {
    margin-top: 24px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--primary-color);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.1);
    animation: progressSlideIn 0.3s ease;
}

@keyframes progressSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.progress-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.progress-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.progress-status span {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.progress-bar-container {
    position: relative;
    background: var(--bg-primary);
    border-radius: 8px;
    height: 24px;
    overflow: hidden;
    margin-bottom: 16px;
    border: 1px solid var(--border-color);
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), #8b5cf6);
    transition: width 0.5s ease;
    position: relative;
    border-radius: 7px;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: 600;
    font-size: 12px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    z-index: 1;
}

.training-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    padding: 16px;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.training-details > div {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

#current-phase {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-color);
}

#training-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

#training-stats > div {
    background: var(--bg-secondary);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-size: 11px;
    transition: background-color 0.3s ease;
}

#training-stats > div:hover {
    background: var(--primary-color);
    color: white;
}

/* Enhanced notification styles for Phase 2 */
.notification {
    max-width: 320px;
    border-radius: 8px;
    font-family: inherit;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    border: 1px solid rgba(255,255,255,0.1);
    backdrop-filter: blur(8px);
    animation: notificationSlide 0.3s ease;
}

@keyframes notificationSlide {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.notification-error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.notification-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* Responsive design for training UI */
@media (max-width: 768px) {
    .training-configuration {
        padding: 16px;
    }
    
    .config-section {
        padding: 16px;
        margin-bottom: 16px;
    }
    
    .training-actions {
        flex-direction: column;
        padding: 16px;
    }
    
    .training-actions .btn {
        min-width: auto;
        width: 100%;
    }
    
    .progress-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .progress-status {
        align-items: flex-start;
    }
    
    .training-details {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 12px;
    }
    
    .file-option {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .file-option input[type="checkbox"] {
        margin-right: 0;
        margin-bottom: 4px;
    }
}

/* Dark theme enhancements for training UI */
[data-theme="dark"] .training-progress {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

[data-theme="dark"] .config-section {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .file-selection {
    background: var(--bg-secondary);
}

[data-theme="dark"] .file-option {
    background: var(--bg-primary);
}

[data-theme="dark"] .training-details {
    background: var(--bg-secondary);
}

/* 🚀 CRITICAL FIX: Error and Loading Styles */
.error-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.error-message {
    background: var(--danger-color);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;
    animation: slideInRight 0.3s ease;
}

.error-icon {
    font-size: 16px;
}

.error-text {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
}

.error-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.error-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.loading-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background: rgba(255, 255, 255, 0.95);
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

.loading-message {
    display: flex;
    align-items: center;
    gap: 12px;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

[data-theme="dark"] .loading-container {
    background: rgba(15, 23, 42, 0.95);
}

[data-theme="dark"] .loading-text {
    color: var(--text-primary);
}

/* 🌊 Token Streaming Styles */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin: 8px 0;
}

.toggle-checkbox {
    width: 18px;
    height: 18px;
    margin-top: 2px;
    accent-color: var(--primary-color);
    cursor: pointer;
}

.toggle-label {
    display: flex;
    flex-direction: column;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-primary);
}

.feature-description {
    font-size: 0.85em;
    color: var(--text-secondary);
    font-weight: normal;
    margin-top: 4px;
    line-height: 1.4;
}

/* Token Stream Container */
.token-stream-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    background: var(--bg-primary);
    border: 2px solid var(--primary-color);
    border-radius: 16px;
    box-shadow: 0 20px 40px var(--shadow);
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
    overflow: hidden;
}

.token-stream-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.token-stream-title {
    font-size: 1.2em;
    font-weight: 600;
}

.token-stream-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background 0.2s ease;
}

.token-stream-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.token-stream-content {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.95em;
    line-height: 1.6;
    background: var(--bg-secondary);
}

.token-chunk {
    display: inline;
    animation: tokenFadeIn 0.3s ease-in;
    margin-right: 2px;
}

.token-chunk.thinking {
    color: #8b5cf6;
    font-weight: 500;
}

.token-chunk.question {
    color: var(--primary-color);
    font-weight: 600;
}

.token-chunk.option {
    color: var(--success-color);
    font-weight: 500;
}

.token-chunk.explanation {
    color: var(--warning-color);
}

.token-chunk.special {
    color: #ec4899;
    font-weight: 600;
}

@keyframes tokenFadeIn {
    from {
        opacity: 0;
        transform: translateY(-2px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stream-progress-bar {
    height: 4px;
    background: var(--bg-secondary);
    overflow: hidden;
}

.stream-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    width: 0%;
    transition: width 0.3s ease;
}

.token-stream-stats {
    padding: 16px 20px;
    background: var(--bg-secondary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9em;
    color: var(--text-secondary);
}

.token-counter, .token-speed {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Dark theme adjustments */
[data-theme="dark"] .token-stream-container {
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .token-chunk.thinking {
    color: #a78bfa;
}

[data-theme="dark"] .token-chunk.question {
    color: #818cf8;
}

[data-theme="dark"] .token-chunk.option {
    color: #34d399;
}

[data-theme="dark"] .token-chunk.explanation {
    color: #fbbf24;
}

[data-theme="dark"] .token-chunk.special {
    color: #f472b6;
}