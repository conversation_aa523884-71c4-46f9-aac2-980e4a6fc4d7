"""
Advanced Startup Performance Analyzer

This module provides comprehensive startup performance analysis and optimization
recommendations for the Knowledge App, focusing on memory usage, load times,
and resource utilization during the critical startup phase.
"""

from .async_converter import async_time_sleep


from .async_converter import async_time_sleep


import time
import psutil
import threading
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Individual performance metric data"""

    name: str
    start_time: float
    end_time: Optional[float] = None
    memory_start: float = 0.0
    memory_end: float = 0.0
    memory_peak: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

    @property
    def duration(self) -> float:
        """Get duration in seconds"""
        if self.end_time is None:
            return time.time() - self.start_time
        return self.end_time - self.start_time

    @property
    def memory_delta(self) -> float:
        """Get memory change in MB"""
        return self.memory_end - self.memory_start


@dataclass
class StartupAnalysisReport:
    """Comprehensive startup analysis report"""

    total_startup_time: float
    total_memory_used: float
    peak_memory: float
    phases: Dict[str, PerformanceMetric]
    bottlenecks: List[str]
    recommendations: List[str]
    timestamp: datetime = field(default_factory=datetime.now)


class StartupPerformanceAnalyzer:
    """
    Advanced startup performance analyzer that provides detailed insights
    into application startup performance and optimization opportunities.
    """

    def __init__(self):
        self.start_time = time.time()
        self.metrics: Dict[str, PerformanceMetric] = {}
        self.active_metrics: Dict[str, PerformanceMetric] = {}
        self.memory_samples: List[Tuple[float, float]] = []
        self.monitoring_thread: Optional[threading.Thread] = None
        self.stop_monitoring = threading.Event()
        self.process = psutil.Process()

        # Performance thresholds
        self.thresholds = {
            "startup_time_warning": 5.0,  # seconds
            "startup_time_critical": 10.0,  # seconds
            "memory_warning": 500.0,  # MB
            "memory_critical": 1000.0,  # MB
            "phase_time_warning": 2.0,  # seconds
            "phase_time_critical": 5.0,  # seconds
        }

        # Start continuous monitoring
        self._start_monitoring()
        logger.info("🔍 Startup performance analyzer initialized")

    def _start_monitoring(self):
        """Start continuous memory monitoring"""
        self.stop_monitoring.clear()
        self.monitoring_thread = threading.Thread(
            target=self._monitor_memory, name="StartupMemoryMonitor", daemon=True
        )
        self.monitoring_thread.start()

    def _monitor_memory(self):
        """Continuously monitor memory usage"""
        while not self.stop_monitoring.is_set():
            try:
                memory_mb = self.process.memory_info().rss / 1024 / 1024
                timestamp = time.time()
                self.memory_samples.append((timestamp, memory_mb))

                # Keep only last 1000 samples to prevent memory bloat
                if len(self.memory_samples) > 1000:
                    self.memory_samples = self.memory_samples[-1000:]

                # Update peak memory for active metrics
                for metric in self.active_metrics.values():
                    metric.memory_peak = max(metric.memory_peak, memory_mb)

                await async_time_await async_time_await async_time_sleep(0.1)  # Sample every 100ms

            except Exception as e:
                logger.warning(f"Memory monitoring error: {e}")
                await async_time_await async_time_await async_time_sleep(1.0)

    def start_phase(self, phase_name: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Start timing a startup phase"""
        current_memory = self.process.memory_info().rss / 1024 / 1024

        metric = PerformanceMetric(
            name=phase_name,
            start_time=time.time(),
            memory_start=current_memory,
            memory_peak=current_memory,
            metadata=metadata or {},
        )

        self.active_metrics[phase_name] = metric
        logger.debug(f"📊 Started phase: {phase_name} (Memory: {current_memory:.1f}MB)")

    def end_phase(self, phase_name: str) -> float:
        """End timing a startup phase and return duration"""
        if phase_name not in self.active_metrics:
            logger.warning(f"Phase '{phase_name}' was not started")
            return 0.0

        metric = self.active_metrics.pop(phase_name)
        metric.end_time = time.time()
        metric.memory_end = self.process.memory_info().rss / 1024 / 1024

        self.metrics[phase_name] = metric

        duration = metric.duration
        memory_delta = metric.memory_delta

        # Log performance with appropriate level
        if duration > self.thresholds["phase_time_critical"]:
            logger.warning(
                f"🐌 SLOW phase: {phase_name} took {duration:.2f}s (+{memory_delta:.1f}MB)"
            )
        elif duration > self.thresholds["phase_time_warning"]:
            logger.info(f"⚠️ Phase: {phase_name} took {duration:.2f}s (+{memory_delta:.1f}MB)")
        else:
            logger.debug(
                f"✅ Phase: {phase_name} completed in {duration:.2f}s (+{memory_delta:.1f}MB)"
            )

        return duration

    def get_current_memory(self) -> float:
        """Get current memory usage in MB"""
        return self.process.memory_info().rss / 1024 / 1024

    def get_peak_memory(self) -> float:
        """Get peak memory usage during startup"""
        if not self.memory_samples:
            return self.get_current_memory()
        return max(sample[1] for sample in self.memory_samples)

    def analyze_startup_performance(self) -> StartupAnalysisReport:
        """Generate comprehensive startup performance analysis"""
        total_time = time.time() - self.start_time
        current_memory = self.get_current_memory()
        peak_memory = self.get_peak_memory()

        # Identify bottlenecks
        bottlenecks = []
        for name, metric in self.metrics.items():
            if metric.duration > self.thresholds["phase_time_critical"]:
                bottlenecks.append(f"{name}: {metric.duration:.2f}s (CRITICAL)")
            elif metric.duration > self.thresholds["phase_time_warning"]:
                bottlenecks.append(f"{name}: {metric.duration:.2f}s (WARNING)")

        # Generate recommendations
        recommendations = self._generate_recommendations(total_time, peak_memory, bottlenecks)

        return StartupAnalysisReport(
            total_startup_time=total_time,
            total_memory_used=current_memory,
            peak_memory=peak_memory,
            phases=self.metrics.copy(),
            bottlenecks=bottlenecks,
            recommendations=recommendations,
        )

    def _generate_recommendations(
        self, total_time: float, peak_memory: float, bottlenecks: List[str]
    ) -> List[str]:
        """Generate optimization recommendations based on analysis"""
        recommendations = []

        # Startup time recommendations
        if total_time > self.thresholds["startup_time_critical"]:
            recommendations.append(
                "🚨 CRITICAL: Startup time exceeds 10 seconds - implement aggressive lazy loading"
            )
            recommendations.append("💡 Consider deferring heavy ML library imports until first use")
            recommendations.append(
                "💡 Implement background initialization for non-critical components"
            )
        elif total_time > self.thresholds["startup_time_warning"]:
            recommendations.append(
                "⚠️ Startup time could be improved - consider additional lazy loading"
            )

        # Memory recommendations
        if peak_memory > self.thresholds["memory_critical"]:
            recommendations.append("🚨 CRITICAL: Peak memory usage exceeds 1GB during startup")
            recommendations.append(
                "💡 Implement more aggressive memory management during initialization"
            )
            recommendations.append("💡 Consider using memory-mapped files for large datasets")
        elif peak_memory > self.thresholds["memory_warning"]:
            recommendations.append("⚠️ Memory usage is high - monitor for potential optimizations")

        # Phase-specific recommendations
        if bottlenecks:
            recommendations.append("🔍 Identified performance bottlenecks:")
            for bottleneck in bottlenecks:
                recommendations.append(f"  • {bottleneck}")

        # General recommendations
        if not recommendations:
            recommendations.append("✅ Startup performance is within acceptable thresholds")
            recommendations.append(
                "💡 Consider implementing preemptive optimizations for future scalability"
            )

        return recommendations

    def save_report(self, report: StartupAnalysisReport, filepath: Optional[Path] = None) -> Path:
        """Save performance report to file"""
        if filepath is None:
            filepath = (
                Path("logs")
                / f"startup_performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )

        filepath.parent.mkdir(parents=True, exist_ok=True)

        # Convert report to JSON-serializable format
        report_data = {
            "timestamp": report.timestamp.isoformat(),
            "total_startup_time": report.total_startup_time,
            "total_memory_used": report.total_memory_used,
            "peak_memory": report.peak_memory,
            "phases": {
                name: {
                    "duration": metric.duration,
                    "memory_delta": metric.memory_delta,
                    "memory_peak": metric.memory_peak,
                    "metadata": metric.metadata,
                }
                for name, metric in report.phases.items()
            },
            "bottlenecks": report.bottlenecks,
            "recommendations": report.recommendations,
        }

        with open(filepath, "w") as f:
            json.dump(report_data, f, indent=2)

        logger.info(f"📊 Performance report saved to: {filepath}")
        return filepath

    def stop(self) -> StartupAnalysisReport:
        """Stop monitoring and generate final report"""
        self.stop_monitoring.set()
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1.0)

        # End any remaining active phases
        for phase_name in list(self.active_metrics.keys()):
            self.end_phase(phase_name)

        report = self.analyze_startup_performance()

        # Log summary
        logger.info(f"🎯 Startup Analysis Summary:")
        logger.info(f"  Total Time: {report.total_startup_time:.2f}s")
        logger.info(f"  Peak Memory: {report.peak_memory:.1f}MB")
        logger.info(f"  Phases: {len(report.phases)}")
        logger.info(f"  Bottlenecks: {len(report.bottlenecks)}")

        return report


# Global analyzer instance
_global_analyzer: Optional[StartupPerformanceAnalyzer] = None


def get_startup_analyzer() -> StartupPerformanceAnalyzer:
    """Get the global startup performance analyzer"""
    global _global_analyzer
    if _global_analyzer is None:
        _global_analyzer = StartupPerformanceAnalyzer()
    return _global_analyzer


def start_phase(phase_name: str, metadata: Optional[Dict[str, Any]] = None) -> None:
    """Start timing a startup phase"""
    analyzer = get_startup_analyzer()
    analyzer.start_phase(phase_name, metadata)


def end_phase(phase_name: str) -> float:
    """End timing a startup phase"""
    analyzer = get_startup_analyzer()
    return analyzer.end_phase(phase_name)


def generate_startup_report() -> StartupAnalysisReport:
    """Generate and save startup performance report"""
    analyzer = get_startup_analyzer()
    report = analyzer.stop()
    analyzer.save_report(report)
    return report