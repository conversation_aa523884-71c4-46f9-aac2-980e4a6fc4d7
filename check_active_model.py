#!/usr/bin/env python3
"""
Check what model is actually being used in the UI
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def check_active_models():
    """Check what models are actually being used"""
    
    print("🔍 CHECKING ACTIVE MODELS IN THE SYSTEM")
    print("=" * 60)
    
    try:
        # Check MCQ Manager active model
        from knowledge_app.core.mcq_manager import get_mcq_manager
        mcq_manager = get_mcq_manager()
        
        print(f"📋 MCQ Manager type: {type(mcq_manager)}")
        
        # Check if it has an active model attribute
        if hasattr(mcq_manager, 'active_model'):
            print(f"🎯 MCQ Manager active model: {mcq_manager.active_model}")
        
        # Check unified inference manager
        from knowledge_app.core.unified_inference_manager import get_unified_inference_manager
        unified_manager = get_unified_inference_manager()
        
        print(f"📋 Unified Manager type: {type(unified_manager)}")
        
        if hasattr(unified_manager, 'active_model'):
            print(f"🎯 Unified Manager active model: {unified_manager.active_model}")
        
        # Check Ollama JSON generator
        from knowledge_app.core.ollama_json_generator import OllamaJSONGenerator
        ollama_gen = OllamaJSONGenerator()
        
        print(f"📋 Ollama Generator type: {type(ollama_gen)}")
        
        if hasattr(ollama_gen, 'active_model'):
            print(f"🎯 Ollama Generator active model: {ollama_gen.active_model}")
        
        # Check available models in Ollama
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json()
            model_names = [model['name'] for model in models.get('models', [])]
            
            print(f"\n📋 Available Ollama models: {model_names}")
            
            # Check for DeepSeek models
            deepseek_models = [name for name in model_names if 'deepseek' in name.lower()]
            mathstral_models = [name for name in model_names if 'mathstral' in name.lower()]
            
            print(f"🧠 DeepSeek models: {deepseek_models}")
            print(f"🧮 Mathstral models: {mathstral_models}")
            
            if 'deepseek-r1:14b' in model_names:
                print("✅ deepseek-r1:14b is available")
            else:
                print("❌ deepseek-r1:14b is NOT available")
                
            if mathstral_models:
                print(f"⚠️ Mathstral is available and might be used instead: {mathstral_models}")
        
        print("\n" + "=" * 60)
        print("🎯 CONCLUSION:")
        print("- If Mathstral is being used instead of DeepSeek R1,")
        print("  that explains why PhD-level generation isn't working")
        print("- Need to force the system to use DeepSeek R1")
        
    except Exception as e:
        print(f"❌ Check failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_active_models()
