"""
The Inquisitor's Mandate v2.0 - Ultra-Strict MCQ Generation Prompt

This prompt is engineered to force local models to return clean, raw JSON
by giving them no room for interpretation.
"""

def _create_inquisitor_prompt(context_text: str, topic: str, difficulty: str, question_type: str = "mixed") -> str:
    """
    Creates the ultra-strict "Inquisitor's Mandate" prompt.

    Args:
        context_text: The text content to generate questions from.
        topic: The topic/subject of the question.
        difficulty: Question difficulty level (easy, medium, hard).

    Returns:
        The standardized, ultra-strict prompt string.
    """
    difficulty_map = {
        "easy": {
            "audience": "a high-school student",
            "requirements": "basic recall and fundamental understanding",
            "complexity": "simple definitions and basic concepts"
        },
        "medium": {
            "audience": "an undergraduate university student",
            "requirements": "analytical thinking, concept application, and moderate synthesis",
            "complexity": "multi-step reasoning, connecting concepts, and practical problem-solving"
        },
        "hard": {
            "audience": "a graduate student specializing in the field",
            "requirements": "advanced analysis, critical evaluation, and expert-level synthesis",
            "complexity": "complex mechanisms, research-level understanding, and sophisticated reasoning"
        },
        "expert": {
            "audience": "a domain expert or PhD-level researcher",
            "requirements": "cutting-edge research understanding, novel problem-solving, and professional-level expertise",
            "complexity": "advanced theoretical frameworks, interdisciplinary connections, and research-grade analysis requiring deep domain mastery"
        },
    }
    difficulty_config = difficulty_map.get(difficulty.lower(), difficulty_map["medium"])
    target_audience = difficulty_config["audience"]
    requirements = difficulty_config["requirements"]
    complexity = difficulty_config["complexity"]

    # Add question type enforcement
    question_type_enforcement = ""
    if question_type == "numerical":
        question_type_enforcement = """🔢 CRITICAL NUMERICAL REQUIREMENT:
- Question MUST involve calculations, numbers, formulas, or quantitative analysis
- Include specific numerical values (masses, energies, wavelengths, frequencies, etc.)
- All answer options MUST be numerical values with appropriate units
- Question should require mathematical problem-solving
- Use formulas like E=hf, E=-13.6/n², λ=hc/E, binding energy calculations
- FORBIDDEN: Conceptual questions, theory explanations, electron configuration patterns
- EXAMPLES: "Calculate the energy...", "What is the wavelength...", "Determine the binding energy..."
"""
    elif question_type == "conceptual":
        question_type_enforcement = "Focus on understanding, principles, and theory. Avoid calculations."
    else:
        question_type_enforcement = "Can combine numerical and conceptual elements as appropriate."

    prompt = f"""### INSTRUCTION ###
You are a machine. Your ONLY task is to generate a single, valid JSON object.
Do NOT output any text, explanation, or markdown before or after the JSON object.

### JSON SCHEMA ###
{{
  "question": "string",
  "options": {{
    "A": "string",
    "B": "string",
    "C": "string",
    "D": "string"
  }},
  "correct": "string (must be 'A', 'B', 'C', or 'D')",
  "explanation": "string"
}}

### TASK ###
Generate a multiple-choice question about '{topic}' for {target_audience}.

### QUESTION TYPE ENFORCEMENT ###
{question_type_enforcement}

### DIFFICULTY REQUIREMENTS ###
Target Audience: {target_audience}
Cognitive Requirements: {requirements}
Question Complexity: {complexity}

### CONTEXT ###
{context_text}

### SPECIFIC REQUIREMENTS ###
1. Question must require {requirements}
2. Question complexity must involve {complexity}
3. Test deep, non-obvious concepts (not basic definitions)
4. Create plausible distractors based on common misconceptions
5. Ensure the question is appropriately challenging for {target_audience}
6. Provide educational explanation demonstrating the required cognitive level

### 🚀 FINAL QUALITY BOOST FOR 95%+ SUCCESS ###

ULTRA-AGGRESSIVE LENGTH REQUIREMENTS:
- Expert: 150+ characters - Add extensive technical detail, specific examples, numerical values
- Hard: 120+ characters - Include comprehensive context and specific scenarios
- Medium: 100+ characters - Provide detailed examples and clear context
- Easy: 80+ characters - Include clear examples and sufficient detail

MANDATORY DOMAIN KEYWORDS (MUST INCLUDE 2+):
- Physics: force, energy, momentum, wave, particle, field, quantum, electromagnetic
- Chemistry: molecule, atom, bond, reaction, compound, solution, acid, base, catalyst
- Mathematics: equation, function, derivative, integral, matrix, variable, theorem, proof

EXPERT COMPLEXITY AMPLIFICATION:
- MUST include advanced terms: theoretical, framework, mechanism, phenomenon, principle
- MUST reference specific theories, laws, or cutting-edge research concepts
- MUST include numerical parameters, formulas, or quantitative analysis
- MUST test synthesis of multiple advanced concepts

OPTION QUALITY STANDARDS:
- Each option minimum 20 characters with specific technical terms
- Make distractors highly plausible with subtle technical differences
- Use parallel structure and consistent complexity across all options
- Include domain-specific terminology in options

CRITICAL SUCCESS CHECKLIST:
✓ Question ends with exactly one "?"
✓ Length meets enhanced requirements (Expert: 150+, Hard: 120+, Medium: 100+, Easy: 80+)
✓ Contains 2+ required domain keywords
✓ All 4 options are substantial (20+ characters each)
✓ Expert questions include complexity keywords
✓ Perfect JSON formatting with no syntax errors

### OUTPUT ###
Respond with ONLY the raw JSON object.
"""
    return prompt
