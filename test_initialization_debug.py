#!/usr/bin/env python3
"""
Debug the UnifiedInferenceManager initialization process
"""

import sys
import os
import asyncio
import time
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging to see what's happening
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

async def test_initialization_debug():
    """Debug the initialization process step by step"""
    print("🔍 DEBUGGING UNIFIED INFERENCE MANAGER INITIALIZATION")
    print("=" * 70)
    
    try:
        from knowledge_app.core.unified_inference_manager import UnifiedInferenceManager, InferenceMode
        
        print("\n1️⃣ Creating UnifiedInferenceManager directly...")
        
        # Create manager directly instead of using singleton
        manager = UnifiedInferenceManager()
        
        print(f"   📊 Initial mode: {manager._mode}")
        print(f"   🔍 Should initialize local: {manager._should_initialize_local()}")
        
        print("\n2️⃣ Starting initialization...")
        
        # Call initialize directly
        success = await manager.initialize()
        
        print(f"   🚀 Initialization result: {success}")
        
        print("\n3️⃣ Final status...")
        status = manager.get_status()
        print(f"   📊 State: {status.get('state', 'unknown')}")
        print(f"   🏠 Local available: {status.get('local_available', False)}")
        print(f"   ☁️ Cloud available: {status.get('cloud_available', False)}")
        
        # Check individual components
        print(f"\n4️⃣ Component status:")
        print(f"   🤖 Global singleton: {manager._global_model_singleton is not None}")
        print(f"   🎮 LM Studio generator: {manager._enhanced_lmstudio_generator is not None}")
        print(f"   📝 Offline MCQ generator: {manager._offline_mcq_generator is not None}")
        print(f"   ☁️ Online generator: {manager._online_generator is not None}")
        
        if manager._offline_mcq_generator:
            print(f"   📝 Offline MCQ initialized: {manager._offline_mcq_generator.is_engine_initialized}")
        
        return success
        
    except Exception as e:
        print(f"❌ Initialization debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_initialization_debug())
    if success:
        print("\n🎉 SUCCESS: Initialization completed!")
    else:
        print("\n❌ FAILED: Initialization had issues")
