#!/usr/bin/env python3
"""
Test the specific "atoms" case that's failing
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_atoms_generation():
    """Test the specific atoms case that's failing"""
    print("🔬 Testing 'atoms' generation...")
    
    try:
        from knowledge_app.core.unified_inference_manager import generate_mcq_unified, initialize_unified_inference
        
        # Initialize
        print("🔧 Initializing system...")
        init_success = initialize_unified_inference()
        if not init_success:
            print("❌ Failed to initialize")
            return False
        
        # Test atoms specifically
        print("🧪 Testing 'atoms' generation...")
        
        try:
            result = generate_mcq_unified(
                topic="atoms",
                difficulty="medium",
                question_type="mixed",
                timeout=30.0
            )
            
            if result:
                print(f"✅ SUCCESS for 'atoms'!")
                print(f"   ❓ Question: {result.get('question', 'N/A')}")
                print(f"   🔢 Options: {result.get('options', [])}")
                print(f"   ✅ Correct: {result.get('correct_answer', 'N/A')}")
                print(f"   📝 Explanation: {result.get('explanation', 'N/A')}")
                return True
            else:
                print(f"❌ FAILED for 'atoms' - No result returned")
                return False
                
        except Exception as e:
            print(f"❌ EXCEPTION for 'atoms': {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_ollama_atoms():
    """Test atoms generation directly with Ollama"""
    print("\n🔬 Testing atoms with direct Ollama call...")
    
    try:
        import requests
        
        prompt = """You are a science educator creating a question about atoms.

Context: Detected chemistry/physics content based on keywords in 'atoms' Include moderate complexity and practical applications.

Create a question that tests scientific understanding and concepts related to atoms.

Generate a JSON response with this exact format:
{
  "question": "Scientific question about atoms",
  "options": ["Option A", "Option B", "Option C", "Option D"],
  "correct": "Option A",
  "explanation": "Scientific explanation with reasoning"
}

Difficulty: MEDIUM - Use moderate complexity, practical examples, and clear reasoning."""

        payload = {
            "model": "mathstral:latest",
            "prompt": prompt,
            "format": "json",
            "stream": False,
            "options": {
                "temperature": 0.4,
                "num_predict": 1800
            }
        }
        
        print(f"🚀 Sending to Ollama...")
        response = requests.post("http://localhost:11434/api/generate", json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            raw_response = result.get('response', '')
            
            print(f"📄 Raw response:")
            print(raw_response)
            
            # Try to parse it
            try:
                parsed = json.loads(raw_response)
                print(f"✅ JSON parsing successful!")
                print(f"📋 Parsed data: {parsed}")
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                return False
        else:
            print(f"❌ Ollama request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Direct test failed: {e}")
        return False

def test_fallback_removal():
    """Check if fallback mechanisms are properly removed"""
    print("\n🔍 Checking fallback mechanisms...")
    
    try:
        # Check if there are any hardcoded fallbacks still in the code
        from knowledge_app.core.unified_inference_manager import get_unified_inference_manager
        
        manager = get_unified_inference_manager()
        
        # Check if fallback methods exist
        fallback_methods = [
            '_generate_fallback_question',
            '_generate_fallback_question_dict',
            'generate_fallback_mcq',
            '_fallback_generation'
        ]
        
        found_fallbacks = []
        for method in fallback_methods:
            if hasattr(manager, method):
                found_fallbacks.append(method)
        
        if found_fallbacks:
            print(f"⚠️ Found fallback methods: {found_fallbacks}")
            return False
        else:
            print(f"✅ No fallback methods found - good!")
            return True
            
    except Exception as e:
        print(f"❌ Fallback check failed: {e}")
        return False

def main():
    print("🔬 Atoms Generation Debug")
    print("=" * 60)
    
    # Test 1: Unified generation
    unified_works = test_atoms_generation()
    
    # Test 2: Direct Ollama
    direct_works = test_direct_ollama_atoms()
    
    # Test 3: Check fallbacks
    no_fallbacks = test_fallback_removal()
    
    print(f"\n📊 SUMMARY:")
    print(f"   Unified generation: {'✅' if unified_works else '❌'}")
    print(f"   Direct Ollama: {'✅' if direct_works else '❌'}")
    print(f"   No fallbacks: {'✅' if no_fallbacks else '❌'}")
    
    if unified_works and direct_works:
        print(f"\n🎉 'atoms' generation is working!")
        return 0
    else:
        print(f"\n❌ 'atoms' generation needs fixing")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
