#!/usr/bin/env python3
"""
🔗 Test UI Semantic Integration
Verifies that the semantic mapping works through the full UI flow
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_topic_analyzer_integration():
    """Test that get_topic_analyzer() uses semantic mapping"""
    print("🔗 TESTING TOPIC ANALYZER INTEGRATION")
    print("=" * 50)
    
    try:
        from knowledge_app.core.topic_analyzer import get_topic_analyzer
        
        # Get the global analyzer instance
        analyzer = get_topic_analyzer()
        
        print(f"✅ TopicAnalyzer created:")
        print(f"   Use semantic mapping: {analyzer.use_semantic_mapping}")
        print(f"   Semantic mapper: {analyzer.semantic_mapper is not None}")
        
        if not analyzer.use_semantic_mapping:
            print("❌ ISSUE: Semantic mapping not enabled!")
            return False
        
        if analyzer.semantic_mapper is None:
            print("❌ ISSUE: Semantic mapper not initialized!")
            return False
        
        print("✅ Semantic mapping properly enabled")
        return True
        
    except Exception as e:
        print(f"❌ Error testing analyzer integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_webengine_analyze_topic():
    """Test the analyzeTopic method that the UI calls"""
    print("\n🌐 TESTING WEBENGINE ANALYZE TOPIC")
    print("=" * 50)
    
    try:
        from knowledge_app.webengine_app import KnowledgeAppWebEngine
        
        # Create a mock webengine instance
        app = KnowledgeAppWebEngine()
        
        # Test the analyzeTopic method directly
        print("🧪 Testing analyzeTopic('CBT')...")
        
        # We can't easily test the Qt signal emission, but we can test the logic
        topic_analyzer = app._get_topic_analyzer()
        
        if topic_analyzer is None:
            print("❌ ISSUE: Topic analyzer not available in WebEngine!")
            return False
        
        profile = topic_analyzer.get_topic_profile("CBT")
        
        print(f"✅ WebEngine topic analysis results:")
        print(f"   Detected type: {profile.get('detected_type')}")
        print(f"   Confidence: {profile.get('confidence')}")
        print(f"   Conceptual possible: {profile.get('is_conceptual_possible')}")
        print(f"   Numerical possible: {profile.get('is_numerical_possible')}")
        print(f"   Expanded topic: {profile.get('expanded_topic', 'N/A')}")
        print(f"   Is abbreviation: {profile.get('is_abbreviation', 'N/A')}")
        
        # Check if CBT is properly handled
        if (profile.get('detected_type') == 'conceptual' and 
            profile.get('is_abbreviation') and
            'cognitive' in profile.get('expanded_topic', '').lower()):
            print("🎉 SUCCESS: CBT properly analyzed through WebEngine!")
            return True
        else:
            print("⚠️ CBT analysis could be improved")
            return False
            
    except Exception as e:
        print(f"❌ Error testing WebEngine: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_ui_flow_simulation():
    """Simulate the full UI flow for topic analysis"""
    print("\n🎮 TESTING FULL UI FLOW SIMULATION")
    print("=" * 50)
    
    try:
        # Simulate what happens when user types "CBT" in the UI
        
        # Step 1: UI calls pythonBridge.analyzeTopic("CBT")
        # Step 2: This calls webengine_app.analyzeTopic("CBT")
        # Step 3: Which calls topic_analyzer.get_topic_profile("CBT")
        # Step 4: Which should use semantic mapping
        
        from knowledge_app.core.topic_analyzer import get_topic_analyzer
        
        analyzer = get_topic_analyzer()
        
        test_cases = [
            ("CBT", "conceptual"),
            ("MBT", "mixed"),  # Your test case
            ("AI", "mixed"),
            ("GDP", "numerical"),
            ("physics", "mixed")
        ]
        
        success_count = 0
        
        for topic, expected_category in test_cases:
            print(f"\n🧪 Simulating UI input: '{topic}'")
            
            # This is exactly what the UI flow does
            profile = analyzer.get_topic_profile(topic)
            
            detected_type = profile.get('detected_type')
            expanded_topic = profile.get('expanded_topic', topic)
            is_abbreviation = profile.get('is_abbreviation', False)
            
            print(f"   Result: {detected_type}")
            print(f"   Expanded: {expanded_topic}")
            print(f"   Is abbreviation: {is_abbreviation}")
            
            # Check if semantic mapping was used
            if profile.get('semantic_analysis'):
                print("   ✅ Semantic mapping used")
                success_count += 1
            elif detected_type in ['conceptual', 'numerical', 'mixed']:
                print("   ✅ Valid analysis (fallback)")
                success_count += 1
            else:
                print("   ❌ Analysis failed")
        
        print(f"\n📊 UI Flow Results: {success_count}/{len(test_cases)} successful")
        return success_count >= len(test_cases) * 0.8  # 80% success rate
        
    except Exception as e:
        print(f"❌ Error testing UI flow: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mbt_specifically():
    """Test MBT specifically since that's what you tried"""
    print("\n🎯 TESTING MBT SPECIFICALLY")
    print("=" * 50)
    
    try:
        from knowledge_app.core.topic_analyzer import get_topic_analyzer
        
        analyzer = get_topic_analyzer()
        
        print("🧪 Testing 'MBT' (your test case)...")
        profile = analyzer.get_topic_profile("MBT")
        
        print(f"✅ MBT Analysis Results:")
        print(f"   Original input: {profile.get('original_input', 'N/A')}")
        print(f"   Expanded topic: {profile.get('expanded_topic', 'N/A')}")
        print(f"   Detected type: {profile.get('detected_type')}")
        print(f"   Confidence: {profile.get('confidence')}")
        print(f"   Is abbreviation: {profile.get('is_abbreviation', False)}")
        print(f"   Full form: {profile.get('full_form', 'N/A')}")
        print(f"   Semantic analysis used: {profile.get('semantic_analysis', False)}")
        
        # Check UI recommendations
        ui_recs = profile.get('ui_recommendations', {})
        if ui_recs:
            print(f"   UI Recommendations:")
            print(f"     - Highlight conceptual: {ui_recs.get('highlight_conceptual')}")
            print(f"     - Highlight numerical: {ui_recs.get('highlight_numerical')}")
            print(f"     - Disable numerical: {ui_recs.get('disable_numerical')}")
            print(f"     - Show expansion: {ui_recs.get('show_expansion')}")
        
        # If semantic analysis was used and gave a result, it's working
        if profile.get('semantic_analysis') and profile.get('detected_type') != 'unknown':
            print("🎉 SUCCESS: MBT properly analyzed with semantic mapping!")
            return True
        elif profile.get('detected_type') != 'unknown':
            print("✅ PARTIAL: MBT analyzed (may have used fallback)")
            return True
        else:
            print("❌ FAILED: MBT not properly analyzed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing MBT: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🔗 UI SEMANTIC INTEGRATION TEST")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # Test 1: Topic analyzer integration
    if test_topic_analyzer_integration():
        success_count += 1
        print("✅ Test 1/4: Topic analyzer integration working")
    else:
        print("❌ Test 1/4: Topic analyzer integration failed")
    
    # Test 2: WebEngine integration
    if test_webengine_analyze_topic():
        success_count += 1
        print("✅ Test 2/4: WebEngine integration working")
    else:
        print("❌ Test 2/4: WebEngine integration failed")
    
    # Test 3: Full UI flow
    if test_full_ui_flow_simulation():
        success_count += 1
        print("✅ Test 3/4: Full UI flow working")
    else:
        print("❌ Test 3/4: Full UI flow failed")
    
    # Test 4: MBT specifically
    if test_mbt_specifically():
        success_count += 1
        print("✅ Test 4/4: MBT analysis working")
    else:
        print("❌ Test 4/4: MBT analysis failed")
    
    print(f"\n🎯 UI INTEGRATION TEST RESULTS: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 UI SEMANTIC INTEGRATION WORKING PERFECTLY!")
        print("   MBT and other abbreviations should now work in the UI")
    elif success_count >= 3:
        print("✅ MOSTLY WORKING")
        print("   UI integration is functional with minor issues")
    else:
        print("❌ MAJOR ISSUES")
        print("   UI integration needs debugging")
    
    print("\n💡 NEXT STEPS:")
    print("   1. Restart your app completely")
    print("   2. Type 'MBT' in the topic field")
    print("   3. Check if it shows expansion and suggests question type")
    print("   4. Try 'CBT', 'AI', 'GDP' as well")

if __name__ == "__main__":
    main()
