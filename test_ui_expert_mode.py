#!/usr/bin/env python3
"""
Test expert mode UI integration - check if questions are generated and reaching UI
"""

import sys
import os
import asyncio
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

async def test_expert_mode_ui_flow():
    """Test the complete expert mode flow from generation to UI"""
    print("🔍 TESTING EXPERT MODE UI FLOW")
    print("=" * 60)
    
    try:
        # Test 1: Check MCQ Manager
        print("\n1️⃣ Testing MCQ Manager...")
        from knowledge_app.core.mcq_manager import get_mcq_manager
        
        mcq_manager = get_mcq_manager()
        
        quiz_params = {
            "topic": "quantum mechanics",
            "difficulty": "expert",
            "game_mode": "serious",
            "submode": "mixed",
            "num_questions": 1
        }
        
        print(f"   🧪 Generating with params: {quiz_params}")
        
        # Generate question
        result = await mcq_manager.generate_quiz_async(quiz_params)
        
        if result:
            print("   ✅ MCQ generation successful!")
            print(f"   ❓ Question: {result.question[:100]}...")
            print(f"   🔧 Method: {getattr(result, 'generation_method', 'unknown')}")
            
            # Convert to dict format that UI expects
            question_data = {
                "question": result.question,
                "options": result.options,
                "correct_answer": result.correct_answer,
                "explanation": result.explanation,
                "question_number": 1,
                "total_questions": 1,
                "is_loading": False,
                "review_mode": False
            }
            
            print("   📋 Question data formatted for UI:")
            print(f"   📝 Question length: {len(question_data['question'])}")
            print(f"   🔢 Options count: {len(question_data['options'])}")
            print(f"   ✅ Has correct answer: {bool(question_data['correct_answer'])}")
            print(f"   💡 Has explanation: {bool(question_data['explanation'])}")
            
            return question_data
        else:
            print("   ❌ MCQ generation failed")
            return None
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_webengine_signal_flow():
    """Test if WebEngine signals are properly set up"""
    print("\n2️⃣ Testing WebEngine Signal Flow...")
    
    try:
        from knowledge_app.webengine_app import KnowledgeApp
        from PyQt5.QtCore import QObject
        
        # Check if signals exist
        app_class = KnowledgeApp
        
        # Check for required signals
        required_signals = [
            'questionReceived',
            'answerFeedback', 
            'quizCompleted',
            'errorOccurred',
            'updateStatus'
        ]
        
        for signal_name in required_signals:
            if hasattr(app_class, signal_name):
                print(f"   ✅ Signal '{signal_name}' exists")
            else:
                print(f"   ❌ Signal '{signal_name}' missing")
        
        return True
        
    except Exception as e:
        print(f"   ❌ WebEngine signal test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🔍 EXPERT MODE UI INTEGRATION TEST")
    print("=" * 60)
    
    # Test async generation
    question_data = asyncio.run(test_expert_mode_ui_flow())
    
    # Test signal flow
    signal_test = test_webengine_signal_flow()
    
    print(f"\n📊 RESULTS:")
    print(f"   Question generation: {'✅' if question_data else '❌'}")
    print(f"   Signal flow: {'✅' if signal_test else '❌'}")
    
    if question_data:
        print(f"\n💡 DIAGNOSIS:")
        print(f"   - Expert questions ARE being generated")
        print(f"   - Question data is properly formatted")
        print(f"   - Issue is likely in signal emission or JavaScript handling")
        print(f"\n🔧 NEXT STEPS:")
        print(f"   1. Check if questionReceived signal is being emitted")
        print(f"   2. Check JavaScript console for errors")
        print(f"   3. Verify WebChannel connection is working")
    else:
        print(f"\n💡 DIAGNOSIS:")
        print(f"   - Expert question generation is failing")
        print(f"   - Need to fix generation pipeline first")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
