<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="aca48e23-8070-4c8a-a663-f92957ba7f1a" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/config/api_keys.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/config/app_settings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/config/settings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/knowledge_app/core/course_system.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/knowledge_app/core/enhanced_latex_renderer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/config/real_7b_config.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/conftest.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/gui_automation_debugger.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/run_clean.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_config_manager.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_core_functionality.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_enterprise_fortification.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_gui_automation_integration.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_hardware_acceleration.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_integration_comprehensive.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_memory_manager.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_model_manager.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_mvc_implementation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_storage_manager.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_training_arguments_fix.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tests/test_training_estimator.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/user_data/user_settings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.gitmodules" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/AI_MODEL_BUGS_FIXED.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MCQ_OPTIMIZATION_SUMMARY.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/directory_structure.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/models/gguf_models/MiMo-7B-RL-Q8_0.gguf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/models/gguf_models/llama-2-7b-chat.Q8_0.gguf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/nltk_data_fixed/tokenizers/punkt.zip" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/nx.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pyproject.toml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/quiz_storage/quiz_database.sqlite" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app_icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/seducedevata" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/quiz_engine.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app.egg-info/PKG-INFO" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app.egg-info/SOURCES.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app.egg-info/dependency_links.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app.egg-info/requires.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app.egg-info/top_level.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/app_health.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/app_health.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/app_init.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/config/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/advanced_quiz_controller.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/advanced_rag_mcq_generator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/advanced_rag_mcq_generator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/app_config.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/app_config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/async_model_interface.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/async_model_interface.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/async_model_manager.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/attention_optimizer.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/attention_optimizer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/audio_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/audio_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/auto_training_manager.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/cache_manager.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/dependency_health_checker.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/dependency_health_checker.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/dependency_injection.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/dependency_injection.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/di_container.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/di_container.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/document_exporter.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/document_exporter.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/document_processor.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/document_processor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/domain_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/domain_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/enhanced_lmstudio_generator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/enhanced_lmstudio_generator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/enterprise_di_container.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/enterprise_di_container.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/fire_estimator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/fire_estimator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/fire_v21_estimator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/fire_v21_estimator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/fire_web_monitor.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/fire_web_monitor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/global_model_singleton.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/global_model_singleton.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/golden_path_trainer.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/gpu_manager.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/hardware_utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/hardware_utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/image_captioner.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/image_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/image_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/inference.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/inference.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/inquisitor_prompt.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/inquisitor_prompt.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/intelligent_mcq_generator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/intelligent_mcq_generator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/interfaces.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/interfaces.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/lazy_rag_loader.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/lazy_rag_loader.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/lm_studio_mcq_generator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/lm_studio_mcq_generator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/lora_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/lora_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/mcq_coherence_monitor.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/mcq_coherence_monitor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/mcq_generator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/mcq_generator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/mcq_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/mcq_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/mcq_post_processor.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/mcq_post_processor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/mcq_validator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/mcq_validator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/memory_consolidation.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/memory_consolidation.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/ml_availability.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/model_evaluator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/model_evaluator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/model_factory.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/model_manager_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/model_manager_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/model_server_client.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/model_server_client.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/multimodal_processor.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/multimodal_processor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/numpy_pydantic_fix.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/numpy_pydantic_fix.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/offline_mcq_generator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/offline_mcq_generator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/ollama_json_generator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/ollama_json_generator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/ollama_model_inference.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/ollama_model_inference.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/online_mcq_generator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/online_mcq_generator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/prompt_quality_helper.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/proper_config_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/proper_config_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/proper_pydantic_config.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/proper_pydantic_config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/pydantic_config.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/pydantic_config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/qa_extractor.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/qa_extractor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/question_generator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/question_generator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/question_history_storage.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/question_history_storage.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/question_repository.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/question_repository.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/question_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/question_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/quiz_engine.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/quiz_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/quiz_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/quiz_storage.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/quiz_storage.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/realtime_mcq_generator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/realtime_mcq_generator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/session_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/session_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/singleton_model_manager.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/startup_optimizer.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/startup_optimizer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/startup_performance_analyzer.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/startup_performance_analyzer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/startup_profiler.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/startup_profiler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/storage_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/storage_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/streaming_dataset.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/streaming_dataset.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/thread_safe_inference.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/thread_safe_inference.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/topic_analyzer.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/topic_analyzer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/training_callbacks.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/training_controller.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/training_data_processor.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/training_estimator.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/training_management.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/training_manager.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/training_metrics.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/training_orchestrator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/training_orchestrator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/training_service.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/training_thread.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/training_worker.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/ultra_lazy_loader.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/ultra_lazy_loader.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/unified_config_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/unified_config_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/unified_inference_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/unified_inference_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/core/unified_resource_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/core/unified_resource_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/fonts.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/install_dependencies.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/latex_renderer.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/lazy_imports/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/lazy_imports/datasets_lazy.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/lazy_imports/faiss_lazy.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/lazy_imports/torch_lazy.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/models/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/models/question.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/models/question.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/question_examples.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/question_generator.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/quick_test.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/quiz_logic.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/rag_engine.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/icons/dropdown.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/icons/exit.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/icons/placeholder.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/icons/quiz.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/icons/review.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/icons/settings.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/icons/train.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/icons/upload.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/resources.qrc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/resources_rc.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/splash.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/resources/splash.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/simple_rag_engine.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/test_ui_responsiveness.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/themes.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/ui/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/ui/adapters/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/ui/controllers/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/ui/spinner.gif" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/ui/web/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/user_data/settings.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/app_utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/app_utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/callbacks.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/callbacks.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/css_cleaner.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/css_cleaner.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/css_warning_suppressor.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/css_warning_suppressor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/dependency_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/dependency_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/enhanced_mcq_parser.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/enhanced_mcq_parser.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/error_handler.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/error_handler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/fonts.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/gpu_utils.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/logging_config.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/logging_config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/logging_setup.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/memory_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/memory_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/model_generation.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/model_generation.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/pyqt_compat.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/pyqt_compat.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/qt_safe_access.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/qt_safe_access.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/qt_warning_suppressor.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/qt_warning_suppressor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/resource_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/resource_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/shutdown_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/shutdown_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/storage.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/text_filter.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/text_filter.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/timeout.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/training_args_helper.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/warning_suppressor.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/warning_suppressor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/utils/worker.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/utils/worker.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/web/app.html" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/web/app.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/web/app.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/web/app.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/web/styles.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/web/styles.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/knowledge_app/webengine_app.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/knowledge_app/webengine_app.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uploaded_books/2ba9658cfafadd713ea5623b714f8cac.pdf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/uploaded_books/41 Years IIT JEE Mathematics by Amit M Agarwal.pdf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/user_data/quiz_database.sqlite" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zgNnZ8JlAHLAlmcd2a2dJLPHMf" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/shared folder/knowledge_app&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;advanced.settings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\shared folder\knowledge_app\cache" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-PY-251.26927.90" />
        <option value="bundled-python-sdk-41e8cd69c857-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="aca48e23-8070-4c8a-a663-f92957ba7f1a" name="Changes" comment="" />
      <created>1752148927289</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752148927289</updated>
      <workItem from="1752148928940" duration="17000" />
      <workItem from="1752252402307" duration="343000" />
      <workItem from="1752252770202" duration="14760000" />
      <workItem from="1752296654777" duration="4947000" />
      <workItem from="1752302678506" duration="609000" />
      <workItem from="1752303383802" duration="1365000" />
      <workItem from="1752309876463" duration="407000" />
      <workItem from="1752313059709" duration="602000" />
      <workItem from="1752313831983" duration="391000" />
      <workItem from="1752570288272" duration="100000" />
      <workItem from="1752570425316" duration="1446000" />
      <workItem from="1752572083921" duration="1439000" />
      <workItem from="1752588245694" duration="1362000" />
      <workItem from="1752589633349" duration="6106000" />
      <workItem from="1752596176102" duration="2319000" />
      <workItem from="1752598549733" duration="725000" />
      <workItem from="1752615289237" duration="26000" />
      <workItem from="1752616752447" duration="460000" />
      <workItem from="1752617449192" duration="941000" />
      <workItem from="1752618429367" duration="112000" />
      <workItem from="1752618558345" duration="2951000" />
      <workItem from="1752622002689" duration="1376000" />
      <workItem from="1752623419109" duration="146000" />
      <workItem from="1752623582228" duration="3723000" />
      <workItem from="1752627414615" duration="1039000" />
      <workItem from="1752628482684" duration="4319000" />
      <workItem from="1752632918967" duration="2211000" />
      <workItem from="1752635200375" duration="286000" />
      <workItem from="1752635502272" duration="4278000" />
      <workItem from="1752650223043" duration="1143000" />
      <workItem from="1752651752609" duration="4051000" />
      <workItem from="1752656347855" duration="2146000" />
      <workItem from="1752661396680" duration="5226000" />
      <workItem from="1752666848487" duration="4753000" />
      <workItem from="1752672710757" duration="753000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="javascript:npm:react" />
    <option featureType="dependencySupport" implementationName="javascript:npm:prettier" />
    <option featureType="dependencySupport" implementationName="javascript:npm:nx" />
    <option featureType="dependencySupport" implementationName="javascript:npm:vite" />
  </component>
</project>