#!/usr/bin/env python3
"""
📸 SCREENSHOT TEST RUNNER
Launches the app and takes periodic screenshots so you can manually test
while the script captures everything for analysis
"""

import sys
import os
import time
import subprocess
import threading
from pathlib import Path
from datetime import datetime

try:
    import pyautogui
    import winsound
except ImportError:
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyautogui", "pillow"])
    import pyautogui
    import winsound

class ScreenshotCapture:
    """Captures screenshots while you manually test"""
    
    def __init__(self):
        self.screenshot_dir = Path("manual_test_screenshots")
        self.screenshot_dir.mkdir(exist_ok=True)
        self.capturing = False
        self.screenshot_count = 0
        
    def take_screenshot(self, prefix="manual"):
        """Take a screenshot with timestamp"""
        self.screenshot_count += 1
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{prefix}_{self.screenshot_count:03d}_{timestamp}.png"
        filepath = self.screenshot_dir / filename
        
        try:
            screenshot = pyautogui.screenshot()
            screenshot.save(str(filepath))
            print(f"📸 Screenshot {self.screenshot_count}: {filename}")
            return str(filepath)
        except Exception as e:
            print(f"❌ Screenshot failed: {e}")
            return None
    
    def start_periodic_capture(self, interval=3):
        """Start taking screenshots every N seconds"""
        self.capturing = True
        
        def capture_loop():
            while self.capturing:
                self.take_screenshot("auto")
                time.sleep(interval)
        
        capture_thread = threading.Thread(target=capture_loop, daemon=True)
        capture_thread.start()
        print(f"📸 Started automatic screenshot capture every {interval} seconds")
    
    def stop_capture(self):
        """Stop automatic capture"""
        self.capturing = False
        print("📸 Stopped automatic screenshot capture")
    
    def play_sound(self, frequency=1000, duration=300):
        """Play a beep sound"""
        try:
            winsound.Beep(frequency, duration)
        except:
            print("🔔 *BEEP*")

def launch_app():
    """Launch the Knowledge App"""
    print("🚀 Launching Knowledge App...")
    
    launch_script = '''
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication
from knowledge_app.webengine_app import KnowledgeAppWebEngine

app = QApplication(sys.argv)
window = KnowledgeAppWebEngine()
window.show()
window.setWindowTitle("Knowledge App - Manual Test")
sys.exit(app.exec_())
'''
    
    # Write launch script to temp file
    with open('temp_launch.py', 'w') as f:
        f.write(launch_script)
    
    # Launch the app
    process = subprocess.Popen([sys.executable, 'temp_launch.py'], cwd=os.getcwd())
    return process

def main():
    """Main test runner"""
    print("📸 MANUAL TEST WITH SCREENSHOT CAPTURE")
    print("=" * 60)
    print("This script will:")
    print("1. 🚀 Launch the Knowledge App")
    print("2. 📸 Take screenshots every 3 seconds automatically")
    print("3. 🎯 YOU manually perform the test:")
    print("   - Click Quiz section")
    print("   - Enter 'atoms' as topic")
    print("   - Select 'expert more' difficulty")
    print("   - Select 'numerical' question type")
    print("   - Click 'Start Generate Quiz'")
    print("   - Wait for generation and question to load")
    print("4. 🔚 Press Ctrl+C here to stop and close app")
    print("=" * 60)
    
    input("Press Enter to start...")
    
    # Initialize screenshot capture
    capture = ScreenshotCapture()
    
    # Launch the app
    app_process = launch_app()
    
    print("⏳ Waiting for app to load...")
    time.sleep(5)
    
    # Take initial screenshot
    capture.take_screenshot("initial")
    
    # Start automatic screenshot capture
    capture.start_periodic_capture(interval=3)
    
    print("\n🎯 APP IS READY - PERFORM YOUR MANUAL TEST NOW!")
    print("=" * 50)
    print("MANUAL TEST STEPS:")
    print("1. Click on 'Quiz' in the left sidebar")
    print("2. Enter 'atoms' in the topic input")
    print("3. Select 'expert more' difficulty")
    print("4. Select 'numerical' question type")
    print("5. Click 'Start Generate Quiz'")
    print("6. Watch the generation process")
    print("7. See if question loads properly")
    print("8. Press Ctrl+C here when done")
    print("=" * 50)
    
    try:
        # Wait for user to finish testing
        while True:
            time.sleep(1)
            
            # Check if app is still running
            if app_process.poll() is not None:
                print("📱 App closed by user")
                break
                
    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")
    
    # Stop screenshot capture
    capture.stop_capture()
    
    # Take final screenshot
    capture.take_screenshot("final")
    
    # Close the app if still running
    if app_process.poll() is None:
        print("🔚 Closing app...")
        app_process.terminate()
        try:
            app_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            app_process.kill()
    
    # Clean up temp file
    try:
        os.remove('temp_launch.py')
    except:
        pass
    
    # Play completion sound
    print("🔔 Playing completion sound...")
    capture.play_sound(1000, 300)
    time.sleep(0.3)
    capture.play_sound(1200, 300)
    time.sleep(0.3)
    capture.play_sound(1400, 500)
    
    print(f"\n✅ TEST COMPLETED!")
    print(f"📁 Screenshots saved in: {capture.screenshot_dir.absolute()}")
    print(f"📸 Total screenshots taken: {capture.screenshot_count}")
    print("\n🔍 NEXT STEPS:")
    print("1. Check the screenshots to see what happened")
    print("2. Share the screenshots with me to analyze issues")
    print("3. Look for:")
    print("   - Did token streaming show up?")
    print("   - Did the same question repeat?")
    print("   - Was numerical option respected?")

if __name__ == "__main__":
    main()
