"""
🔥 UNIFIED RESOURCE MANAGER - Eliminates Memory Leaks
Centralized resource management for ALL app components
"""

import logging
import threading
import weakref
import gc
from typing import Dict, Any, Optional
from PyQt5.QtCore import QObject, QTimer, pyqtSignal
import time

logger = logging.getLogger(__name__)


class UnifiedResourceManager(QObject):
    """
    🔥 UNIFIED RESOURCE MANAGER - Master Controller for ALL Resources
    
    Prevents memory leaks by tracking and cleaning up all allocated resources
    """
    
    memory_usage_updated = pyqtSignal(dict)
    
    _instance: Optional["UnifiedResourceManager"] = None
    _lock = threading.Lock()
    _initialized_flag = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        # CRITICAL FIX: Check class-level flag instead of instance attribute
        if UnifiedResourceManager._initialized_flag:
            return
            
        # Always call super().__init__() first
        super().__init__()
        
        # Resource tracking
        self._resources: Dict[str, Any] = {}
        self._resource_lock = threading.RLock()
        self._resource_counter = 0
        
        # Cleanup callbacks
        self._cleanup_callbacks = []
        
        # Memory monitoring timer - DEFERRED until QGuiApplication exists
        self._memory_timer = None
        self._timer_started = False
        
        # Set class-level flag to prevent re-initialization
        UnifiedResourceManager._initialized_flag = True
        # Silent initialization
    
    def register_resource(self, resource_obj: Any, resource_type: str) -> str:
        """Register a resource for tracking"""
        with self._resource_lock:
            self._resource_counter += 1
            resource_id = f"{resource_type}_{self._resource_counter}"
            
            self._resources[resource_id] = {
                'object': resource_obj,
                'type': resource_type,
                'created_at': time.time()
            }
            
            return resource_id
    
    def unregister_resource(self, resource_id: str):
        """Unregister a resource"""
        with self._resource_lock:
            if resource_id in self._resources:
                del self._resources[resource_id]
    
    def add_cleanup_callback(self, callback):
        """Add a cleanup callback"""
        self._cleanup_callbacks.append(callback)
    
    def start_monitoring(self):
        """Start memory monitoring - call this after QGuiApplication is created"""
        try:
            if not self._timer_started and self._memory_timer is None:
                from PyQt5.QtCore import QCoreApplication
                # Check if QGuiApplication exists
                if QCoreApplication.instance() is not None:
                    self._memory_timer = QTimer(self)
                    self._memory_timer.timeout.connect(self._monitor_memory)
                    self._memory_timer.start(30000)  # Every 30 seconds
                    self._timer_started = True
                    # Silent start
        except Exception as e:
            pass  # Silent failure
    
    def _monitor_memory(self):
        """Monitor memory usage"""
        try:
            # Force garbage collection
            collected = gc.collect()
            
            # Emit memory stats
            stats = {
                'active_resources': len(self._resources),
                'gc_collected': collected
            }
            self.memory_usage_updated.emit(stats)
            
        except Exception as e:
            pass  # Silent failure
    
    def emergency_cleanup(self):
        """Emergency cleanup of all resources"""
        logger.warning("🚨 EMERGENCY CLEANUP initiated!")
        
        try:
            # Call all cleanup callbacks
            for callback in self._cleanup_callbacks:
                try:
                    callback()
                except Exception as e:
                    logger.error(f"❌ Cleanup callback failed: {e}")
            
            # Clear all tracked resources
            with self._resource_lock:
                self._resources.clear()
            
            # Force garbage collection
            for _ in range(3):
                gc.collect()
            
            pass  # Silent completion
            
        except Exception as e:
            pass  # Silent failure
    
    def shutdown(self):
        """Shutdown the resource manager"""
        if self._memory_timer:
            self._memory_timer.stop()
        self.emergency_cleanup()


# Global singleton access
_unified_resource_manager = None
_manager_lock = threading.Lock()


def get_unified_resource_manager() -> UnifiedResourceManager:
    """Get the global unified resource manager instance"""
    global _unified_resource_manager
    
    if _unified_resource_manager is None:
        with _manager_lock:
            if _unified_resource_manager is None:
                _unified_resource_manager = UnifiedResourceManager()
    
    return _unified_resource_manager